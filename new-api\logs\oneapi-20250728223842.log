[SYS] 2025/07/28 - 22:38:42 | initializing token encoders 
[SYS] 2025/07/28 - 22:38:42 | token encoders initialized 
[SYS] 2025/07/28 - 22:38:42 | SQL_DSN not set, using SQLite as database 
[SYS] 2025/07/28 - 22:38:42 | database migration started 
[SYS] 2025/07/28 - 22:38:42 | database migrated 
[SYS] 2025/07/28 - 22:38:42 | system is not initialized and no root user exists 
[SYS] 2025/07/28 - 22:38:42 | REDIS_CONN_STRING not set, Redis is not enabled 
[SYS] 2025/07/28 - 22:38:42 | New API v0.0.0 started 
[SYS] 2025/07/28 - 22:38:42 | 正在更新数据看板数据... 
[SYS] 2025/07/28 - 22:38:42 | 保存数据看板数据成功，共保存0条数据 
[SYS] 2025/07/28 - 22:38:57 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:38:57 | 任务进度轮询完成 
[SYS] 2025/07/28 - 22:39:12 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:39:12 | 任务进度轮询完成 
[GIN] 2025/07/28 - 22:39:21 | 20250728223921617901300IYslazQc | 200 |    171.7634ms |             ::1 |     GET /
[GIN] 2025/07/28 - 22:39:26 | 202507282239265793388003wGcYpgo | 200 |       706.5µs |             ::1 |     GET /
[GIN] 2025/07/28 - 22:39:26 | 20250728223926654537400i108x7QI | 200 |      3.8963ms |             ::1 |     GET /assets/tools-C3llIrvJ.js
[GIN] 2025/07/28 - 22:39:26 | 20250728223926656827200sQTUedEi | 200 |      1.6065ms |             ::1 |     GET /assets/react-components-C55tCU1e.js
[GIN] 2025/07/28 - 22:39:26 | 20250728223926651547100BuaAd1rq | 200 |      15.332ms |             ::1 |     GET /assets/react-core-DskXcPn0.js
[GIN] 2025/07/28 - 22:39:26 | 20250728223926670591900kZuWkbuH | 200 |      3.6597ms |             ::1 |     GET /assets/i18n-bne0o_C4.js
[GIN] 2025/07/28 - 22:39:26 | 20250728223926656827200atsVxgjT | 200 |     21.8238ms |             ::1 |     GET /assets/semi-ui-CTP9T3wo.css
[GIN] 2025/07/28 - 22:39:26 | 202507282239266705919006v0Y1xII | 200 |     21.7923ms |             ::1 |     GET /assets/index-KeO_Wjpo.css
[GIN] 2025/07/28 - 22:39:26 | 20250728223926654537400u40EV6mY | 200 |      84.077ms |             ::1 |     GET /assets/semi-ui-Csx8wKaA.js
[GIN] 2025/07/28 - 22:39:26 | 20250728223926651547100sRQaD5NS | 200 |     96.2212ms |             ::1 |     GET /assets/index-DTzYmM8W.js
[GIN] 2025/07/28 - 22:39:27 | 20250728223927165788300cojfBfLG | 200 |      2.0726ms |             ::1 |     GET /assets/index-DKQphakQ.js
[GIN] 2025/07/28 - 22:39:27 | 20250728223927225958700tbmpzscO | 200 |       636.8µs |             ::1 |     GET /api/status
[GIN] 2025/07/28 - 22:39:27 | 20250728223927273500100P0HLeAQn | 200 |            0s |             ::1 |     GET /setup
[GIN] 2025/07/28 - 22:39:27 | 20250728223927274123500ZfrJIYYY | 200 |       553.7µs |             ::1 |     GET /api/notice
[GIN] 2025/07/28 - 22:39:27 | 202507282239272746772001QhdJQ2l | 200 |            0s |             ::1 |     GET /api/home_page_content
[GIN] 2025/07/28 - 22:39:27 | 20250728223927525404900BALGwNN5 | 200 |       754.3µs |             ::1 |     GET /api/status
[GIN] 2025/07/28 - 22:39:27 | 202507282239275254049007WoJFKD2 | 200 |       754.3µs |             ::1 |     GET /api/setup
[SYS] 2025/07/28 - 22:39:27 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:39:27 | 任务进度轮询完成 
[GIN] 2025/07/28 - 22:39:27 | 20250728223927610921000Aa5SPxOZ | 200 |            0s |             ::1 |     GET /logo.png
[GIN] 2025/07/28 - 22:39:28 | 202507282239288648817002uG9p2ZF | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/28 - 22:39:29 | 20250728223929135823200QlvDWqKB | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/28 - 22:39:29 | 20250728223929176194800J9BfTCP5 | 200 |            0s |             ::1 |     GET /setup
[GIN] 2025/07/28 - 22:39:29 | 20250728223929178214000ngKiOA0x | 200 |            0s |             ::1 |     GET /api/notice
[GIN] 2025/07/28 - 22:39:29 | 20250728223929179237900YrfpDKXY | 200 |            0s |             ::1 |     GET /api/home_page_content
[GIN] 2025/07/28 - 22:39:29 | 20250728223929311071600M9PrqyAO | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/28 - 22:39:29 | 20250728223929311071600oNO4SCJo | 200 |       506.1µs |             ::1 |     GET /api/setup
[SYS] 2025/07/28 - 22:39:42 | syncing options from database 
[SYS] 2025/07/28 - 22:39:42 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:39:42 | 任务进度轮询完成 
[GIN] 2025/07/28 - 22:39:43 | 20250728223943410833200nuI94yGK | 200 |    142.0002ms |             ::1 |    POST /api/setup
[GIN] 2025/07/28 - 22:39:45 | 20250728223945104772900wYZ1hfba | 200 |            0s |             ::1 |     GET /setup
[GIN] 2025/07/28 - 22:39:45 | 20250728223945242775500YJ6SUUa2 | 200 |            0s |             ::1 |     GET /api/setup
[GIN] 2025/07/28 - 22:39:45 | 20250728223945243340500E1dmO3ea | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/28 - 22:39:45 | 20250728223945299244700ssbrVk2T | 200 |       508.7µs |             ::1 |     GET /
[GIN] 2025/07/28 - 22:39:45 | 202507282239454283678008OGae5RL | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/28 - 22:39:45 | 2025072822394546475610086G1gtzi | 200 |            0s |             ::1 |     GET /api/notice
[GIN] 2025/07/28 - 22:39:45 | 20250728223945465493800L2yPUALH | 200 |            0s |             ::1 |     GET /api/home_page_content
[GIN] 2025/07/28 - 22:39:50 | 20250728223950164634400TcvUGykd | 200 |      1.0531ms |             ::1 |     GET /assets/index-BiMGlUr7.js
[GIN] 2025/07/28 - 22:39:50 | 20250728223950165687500OTnFcbW2 | 200 |      1.0702ms |             ::1 |     GET /assets/IllustrationConstruction-BxqVZ4HA.js
[GIN] 2025/07/28 - 22:39:50 | 20250728223950165144400IoMRXNon | 200 |    109.3401ms |             ::1 |     GET /assets/visactor-Csuqn0ps.js
[GIN] 2025/07/28 - 22:39:50 | 20250728223950384508300NdZv06WA | 401 |       507.8µs |             ::1 |     GET /api/user/self
[GIN] 2025/07/28 - 22:39:50 | 20250728223950389404000tmgKtMds | 401 |       506.5µs |             ::1 |     GET /api/data/?username=&start_timestamp=1753627190&end_timestamp=1753717190&default_time=hour
[GIN] 2025/07/28 - 22:39:50 | 20250728223950507201900EuTlbgcZ | 200 |       618.2µs |             ::1 |     GET /login?expired=true
[GIN] 2025/07/28 - 22:39:50 | 20250728223950573300800Vj4l3xX8 | 200 |            0s |             ::1 |     GET /login?expired=true
[GIN] 2025/07/28 - 22:39:50 | 20250728223950746165800OLMF1Io9 | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/28 - 22:39:52 | 20250728223951975463600biKwXY4V | 200 |    106.6662ms |             ::1 |    POST /api/user/login?turnstile=
[GIN] 2025/07/28 - 22:39:52 | 20250728223952195151100qqWw7KDG | 200 |       506.8µs |             ::1 |     GET /api/user/self
[GIN] 2025/07/28 - 22:39:52 | 20250728223952197763100E2O1SkyL | 200 |       998.6µs |             ::1 |     GET /api/data/?username=&start_timestamp=1753627192&end_timestamp=1753717192&default_time=hour
[GIN] 2025/07/28 - 22:39:52 | 20250728223952301631100LVva2APs | 200 |            0s |             ::1 |     GET /api/uptime/status
[GIN] 2025/07/28 - 22:39:55 | 20250728223955774401500o2XCQCHg | 200 |       508.3µs |             ::1 |     GET /api/group/
[GIN] 2025/07/28 - 22:39:55 | 20250728223955774401500i4jGB9yF | 200 |       508.3µs |             ::1 |     GET /api/channel/models
[GIN] 2025/07/28 - 22:39:55 | 20250728223955778534600zxUjllZW | 200 |       543.4µs |             ::1 |     GET /api/models
[GIN] 2025/07/28 - 22:39:55 | 20250728223955777024000zbJw4tgt | 200 |       2.054ms |             ::1 |     GET /api/channel/?p=1&page_size=10&id_sort=false&tag_mode=false
[SYS] 2025/07/28 - 22:39:57 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:39:57 | 任务进度轮询完成 
[SYS] 2025/07/28 - 22:40:12 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:40:12 | 任务进度轮询完成 
[SYS] 2025/07/28 - 22:40:27 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:40:27 | 任务进度轮询完成 
[SYS] 2025/07/28 - 22:40:42 | syncing options from database 
[SYS] 2025/07/28 - 22:40:42 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:40:42 | 任务进度轮询完成 
[SYS] 2025/07/28 - 22:40:57 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:40:57 | 任务进度轮询完成 
[SYS] 2025/07/28 - 22:41:12 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:41:12 | 任务进度轮询完成 
[SYS] 2025/07/28 - 22:41:27 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:41:27 | 任务进度轮询完成 
[SYS] 2025/07/28 - 22:41:42 | syncing options from database 
[SYS] 2025/07/28 - 22:41:42 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:41:42 | 任务进度轮询完成 
[GIN] 2025/07/28 - 22:41:54 | 20250728224153975829000Sma6IqWZ | 200 |     69.1555ms |             ::1 |    POST /api/channel/
[GIN] 2025/07/28 - 22:41:54 | 2025072822415452748900Y04LeCKg | 200 |      1.3032ms |             ::1 |     GET /api/channel/?p=1&page_size=10&id_sort=false&tag_mode=false
[SYS] 2025/07/28 - 22:41:57 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:41:57 | 任务进度轮询完成 
[GIN] 2025/07/28 - 22:41:58 | 2025072822415876624600Pv7tKHR7 | 200 |      2.5618ms |             ::1 |     GET /api/channel/?p=1&page_size=50&id_sort=false&tag_mode=false
[SYS] 2025/07/28 - 22:42:02 | testing channel 1 with model gemini-2.5-pro , info {ChannelType:24 ChannelId:1 TokenId:0 TokenKey: UserId:0 UsingGroup:default UserGroup:default TokenUnlimited:false StartTime:0001-01-01 00:00:00 +0000 UTC FirstResponseTime:0000-12-31 23:59:59 +0000 UTC isFirstResponse:true ApiType:9 IsStream:false IsPlayground:false UsePrice:false RelayMode:1 UpstreamModelName:gemini-2.5-pro OriginModelName:gemini-2.5-pro RequestURLPath:/v1/chat/completions ApiVersion: PromptTokens:0 ApiKey: Organization: BaseUrl:https://generativelanguage.googleapis.com SupportStreamOptions:true ShouldIncludeUsage:false IsModelMapped:false ClientWs:<nil> TargetWs:<nil> InputAudioFormat: OutputAudioFormat: RealtimeTools:[] IsFirstRequest:false AudioUsage:false ReasoningEffort: ChannelSetting:{ForceFormat:false ThinkingToContent:false Proxy:} ParamOverride:map[] UserSetting:{NotifyType: QuotaWarningThreshold:0 WebhookUrl: WebhookSecret: NotificationEmail: AcceptUnsetRatioModel:false RecordIpLog:false} UserEmail: UserQuota:100000000 RelayFormat:openai SendResponseCount:0 ChannelCreateTime:1753713713 ThinkingContentInfo:{IsFirstThinkingContent:true SendLastThinkingContent:false HasSentThinkingContent:false} ClaudeConvertInfo:<nil> RerankerInfo:<nil> ResponsesUsageInfo:<nil>}  
[SYS] 2025/07/28 - 22:42:12 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:42:12 | 任务进度轮询完成 
[SYS] 2025/07/28 - 22:42:26 | testing channel 1 with model gemini-2.5-pro , info {ChannelType:24 ChannelId:1 TokenId:0 TokenKey: UserId:0 UsingGroup:default UserGroup:default TokenUnlimited:false StartTime:0001-01-01 00:00:00 +0000 UTC FirstResponseTime:0000-12-31 23:59:59 +0000 UTC isFirstResponse:true ApiType:9 IsStream:false IsPlayground:false UsePrice:false RelayMode:1 UpstreamModelName:gemini-2.5-pro OriginModelName:gemini-2.5-pro RequestURLPath:/v1/chat/completions ApiVersion: PromptTokens:0 ApiKey: Organization: BaseUrl:https://generativelanguage.googleapis.com SupportStreamOptions:true ShouldIncludeUsage:false IsModelMapped:false ClientWs:<nil> TargetWs:<nil> InputAudioFormat: OutputAudioFormat: RealtimeTools:[] IsFirstRequest:false AudioUsage:false ReasoningEffort: ChannelSetting:{ForceFormat:false ThinkingToContent:false Proxy:} ParamOverride:map[] UserSetting:{NotifyType: QuotaWarningThreshold:0 WebhookUrl: WebhookSecret: NotificationEmail: AcceptUnsetRatioModel:false RecordIpLog:false} UserEmail: UserQuota:100000000 RelayFormat:openai SendResponseCount:0 ChannelCreateTime:1753713713 ThinkingContentInfo:{IsFirstThinkingContent:true SendLastThinkingContent:false HasSentThinkingContent:false} ClaudeConvertInfo:<nil> RerankerInfo:<nil> ResponsesUsageInfo:<nil>}  
[SYS] 2025/07/28 - 22:42:27 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:42:27 | 任务进度轮询完成 
[GIN] 2025/07/28 - 22:42:32 | 202507282242023097037000UZu3aS5 | 200 |   30.0018994s |             ::1 |     GET /api/channel/test/1?model=
[SYS] 2025/07/28 - 22:42:42 | syncing options from database 
[SYS] 2025/07/28 - 22:42:42 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:42:42 | 任务进度轮询完成 
[GIN] 2025/07/28 - 22:42:56 | 20250728224226930492800DlxN0Lhs | 200 |   30.0015682s |             ::1 |     GET /api/channel/test/1?model=gemini-2.5-pro
[SYS] 2025/07/28 - 22:42:57 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:42:57 | 任务进度轮询完成 
[SYS] 2025/07/28 - 22:43:12 | 任务进度轮询开始 
[SYS] 2025/07/28 - 22:43:12 | 任务进度轮询完成 
[GIN] 2025/07/28 - 22:43:16 | 202507282243167773068004U7iUMMK | 200 |     16.9246ms |             ::1 |     GET /api/user/aff
