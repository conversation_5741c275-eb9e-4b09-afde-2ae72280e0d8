import{_ as e,l as o,K as i,f as n,L as p}from"./index-DTzYmM8W.js";import{p as m}from"./treemap-75Q7IDZK-CrTh5nrt.js";import"./semi-ui-Csx8wKaA.js";import"./react-core-DskXcPn0.js";import"./tools-C3llIrvJ.js";import"./react-components-C55tCU1e.js";import"./i18n-bne0o_C4.js";import"./_baseUniq-ZSwxWUik.js";import"./_basePickBy-G-l42atP.js";import"./clone-qQLK9m8k.js";var g={parse:e(async r=>{const a=await m("info",r);o.debug(a)},"parse")},v={version:p.version+""},d=e(()=>v.version,"getVersion"),c={getVersion:d},f=e((r,a,s)=>{o.debug(`rendering info diagram
`+r);const t=i(a);n(t,100,400,!0),t.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${s}`)},"draw"),l={draw:f},L={parser:g,db:c,renderer:l};export{L as diagram};
