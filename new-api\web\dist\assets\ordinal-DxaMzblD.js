import{i as a}from"./init-Gi6I4Gst.js";class o extends Map{constructor(t,n=g){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),t!=null)for(const[r,s]of t)this.set(r,s)}get(t){return super.get(c(this,t))}has(t){return super.has(c(this,t))}set(t,n){return super.set(l(this,t),n)}delete(t){return super.delete(p(this,t))}}function c({_intern:e,_key:t},n){const r=t(n);return e.has(r)?e.get(r):n}function l({_intern:e,_key:t},n){const r=t(n);return e.has(r)?e.get(r):(e.set(r,n),n)}function p({_intern:e,_key:t},n){const r=t(n);return e.has(r)&&(n=e.get(r),e.delete(r)),n}function g(e){return e!==null&&typeof e=="object"?e.valueOf():e}const f=Symbol("implicit");function h(){var e=new o,t=[],n=[],r=f;function s(i){let u=e.get(i);if(u===void 0){if(r!==f)return r;e.set(i,u=t.push(i)-1)}return n[u%n.length]}return s.domain=function(i){if(!arguments.length)return t.slice();t=[],e=new o;for(const u of i)e.has(u)||e.set(u,t.push(u)-1);return s},s.range=function(i){return arguments.length?(n=Array.from(i),s):n.slice()},s.unknown=function(i){return arguments.length?(r=i,s):r},s.copy=function(){return h(t,n).unknown(r)},a.apply(s,arguments),s}export{h as o};
