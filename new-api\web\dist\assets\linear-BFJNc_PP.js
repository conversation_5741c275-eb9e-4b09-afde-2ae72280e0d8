import{b6 as j,b7 as p,b8 as w,b9 as q,ba as k}from"./index-DTzYmM8W.js";import{i as D}from"./init-Gi6I4Gst.js";import{e as g,a as F,b as z,f as B}from"./defaultLocale-R7l282Le.js";function I(n,r){r||(r=[]);var t=n?Math.min(r.length,n.length):0,e=r.slice(),u;return function(f){for(u=0;u<t;++u)e[u]=n[u]*(1-f)+r[u]*f;return e}}function P(n){return ArrayBuffer.isView(n)&&!(n instanceof DataView)}function V(n,r){var t=r?r.length:0,e=n?Math.min(t,n.length):0,u=new Array(e),f=new Array(t),a;for(a=0;a<e;++a)u[a]=d(n[a],r[a]);for(;a<t;++a)f[a]=r[a];return function(o){for(a=0;a<e;++a)f[a]=u[a](o);return f}}function $(n,r){var t=new Date;return n=+n,r=+r,function(e){return t.setTime(n*(1-e)+r*e),t}}function x(n,r){var t={},e={},u;(n===null||typeof n!="object")&&(n={}),(r===null||typeof r!="object")&&(r={});for(u in r)u in n?t[u]=d(n[u],r[u]):e[u]=r[u];return function(f){for(u in t)e[u]=t[u](f);return e}}function d(n,r){var t=typeof r,e;return r==null||t==="boolean"?j(r):(t==="number"?p:t==="string"?(e=k(r))?(r=e,w):q:r instanceof k?w:r instanceof Date?$:P(r)?I:Array.isArray(r)?V:typeof r.valueOf!="function"&&typeof r.toString!="function"||isNaN(r)?x:p)(n,r)}function O(n,r){return n=+n,r=+r,function(t){return Math.round(n*(1-t)+r*t)}}function T(n){return Math.max(0,-g(Math.abs(n)))}function C(n,r){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(g(r)/3)))*3-g(Math.abs(n)))}function E(n,r){return n=Math.abs(n),r=Math.abs(r)-n,Math.max(0,g(r)-g(n))+1}function M(n,r){return n==null||r==null?NaN:n<r?-1:n>r?1:n>=r?0:NaN}function G(n,r){return n==null||r==null?NaN:r<n?-1:r>n?1:r>=n?0:NaN}function R(n){let r,t,e;n.length!==2?(r=M,t=(o,c)=>M(n(o),c),e=(o,c)=>n(o)-c):(r=n===M||n===G?n:H,t=n,e=n);function u(o,c,i=0,s=o.length){if(i<s){if(r(c,c)!==0)return s;do{const l=i+s>>>1;t(o[l],c)<0?i=l+1:s=l}while(i<s)}return i}function f(o,c,i=0,s=o.length){if(i<s){if(r(c,c)!==0)return s;do{const l=i+s>>>1;t(o[l],c)<=0?i=l+1:s=l}while(i<s)}return i}function a(o,c,i=0,s=o.length){const l=u(o,c,i,s-1);return l>i&&e(o[l-1],c)>-e(o[l],c)?l-1:l}return{left:u,center:a,right:f}}function H(){return 0}function J(n){return n===null?NaN:+n}const K=R(M),L=K.right;R(J).center;const Q=Math.sqrt(50),U=Math.sqrt(10),W=Math.sqrt(2);function v(n,r,t){const e=(r-n)/Math.max(0,t),u=Math.floor(Math.log10(e)),f=e/Math.pow(10,u),a=f>=Q?10:f>=U?5:f>=W?2:1;let o,c,i;return u<0?(i=Math.pow(10,-u)/a,o=Math.round(n*i),c=Math.round(r*i),o/i<n&&++o,c/i>r&&--c,i=-i):(i=Math.pow(10,u)*a,o=Math.round(n/i),c=Math.round(r/i),o*i<n&&++o,c*i>r&&--c),c<o&&.5<=t&&t<2?v(n,r,t*2):[o,c,i]}function X(n,r,t){if(r=+r,n=+n,t=+t,!(t>0))return[];if(n===r)return[n];const e=r<n,[u,f,a]=e?v(r,n,t):v(n,r,t);if(!(f>=u))return[];const o=f-u+1,c=new Array(o);if(e)if(a<0)for(let i=0;i<o;++i)c[i]=(f-i)/-a;else for(let i=0;i<o;++i)c[i]=(f-i)*a;else if(a<0)for(let i=0;i<o;++i)c[i]=(u+i)/-a;else for(let i=0;i<o;++i)c[i]=(u+i)*a;return c}function y(n,r,t){return r=+r,n=+n,t=+t,v(n,r,t)[2]}function Y(n,r,t){r=+r,n=+n,t=+t;const e=r<n,u=e?y(r,n,t):y(n,r,t);return(e?-1:1)*(u<0?1/-u:u)}function Z(n){return function(){return n}}function _(n){return+n}var A=[0,1];function m(n){return n}function N(n,r){return(r-=n=+n)?function(t){return(t-n)/r}:Z(isNaN(r)?NaN:.5)}function b(n,r){var t;return n>r&&(t=n,n=r,r=t),function(e){return Math.max(n,Math.min(r,e))}}function nn(n,r,t){var e=n[0],u=n[1],f=r[0],a=r[1];return u<e?(e=N(u,e),f=t(a,f)):(e=N(e,u),f=t(f,a)),function(o){return f(e(o))}}function rn(n,r,t){var e=Math.min(n.length,r.length)-1,u=new Array(e),f=new Array(e),a=-1;for(n[e]<n[0]&&(n=n.slice().reverse(),r=r.slice().reverse());++a<e;)u[a]=N(n[a],n[a+1]),f[a]=t(r[a],r[a+1]);return function(o){var c=L(n,o,1,e)-1;return f[c](u[c](o))}}function en(n,r){return r.domain(n.domain()).range(n.range()).interpolate(n.interpolate()).clamp(n.clamp()).unknown(n.unknown())}function tn(){var n=A,r=A,t=d,e,u,f,a=m,o,c,i;function s(){var h=Math.min(n.length,r.length);return a!==m&&(a=b(n[0],n[h-1])),o=h>2?rn:nn,c=i=null,l}function l(h){return h==null||isNaN(h=+h)?f:(c||(c=o(n.map(e),r,t)))(e(a(h)))}return l.invert=function(h){return a(u((i||(i=o(r,n.map(e),p)))(h)))},l.domain=function(h){return arguments.length?(n=Array.from(h,_),s()):n.slice()},l.range=function(h){return arguments.length?(r=Array.from(h),s()):r.slice()},l.rangeRound=function(h){return r=Array.from(h),t=O,s()},l.clamp=function(h){return arguments.length?(a=h?!0:m,s()):a!==m},l.interpolate=function(h){return arguments.length?(t=h,s()):t},l.unknown=function(h){return arguments.length?(f=h,l):f},function(h,S){return e=h,u=S,s()}}function un(){return tn()(m,m)}function an(n,r,t,e){var u=Y(n,r,t),f;switch(e=F(e??",f"),e.type){case"s":{var a=Math.max(Math.abs(n),Math.abs(r));return e.precision==null&&!isNaN(f=C(u,a))&&(e.precision=f),z(e,a)}case"":case"e":case"g":case"p":case"r":{e.precision==null&&!isNaN(f=E(u,Math.max(Math.abs(n),Math.abs(r))))&&(e.precision=f-(e.type==="e"));break}case"f":case"%":{e.precision==null&&!isNaN(f=T(u))&&(e.precision=f-(e.type==="%")*2);break}}return B(e)}function on(n){var r=n.domain;return n.ticks=function(t){var e=r();return X(e[0],e[e.length-1],t??10)},n.tickFormat=function(t,e){var u=r();return an(u[0],u[u.length-1],t??10,e)},n.nice=function(t){t==null&&(t=10);var e=r(),u=0,f=e.length-1,a=e[u],o=e[f],c,i,s=10;for(o<a&&(i=a,a=o,o=i,i=u,u=f,f=i);s-- >0;){if(i=y(a,o,t),i===c)return e[u]=a,e[f]=o,r(e);if(i>0)a=Math.floor(a/i)*i,o=Math.ceil(o/i)*i;else if(i<0)a=Math.ceil(a*i)/i,o=Math.floor(o*i)/i;else break;c=i}return n},n}function fn(){var n=un();return n.copy=function(){return en(n,fn())},D.apply(n,arguments),on(n)}export{en as a,un as c,fn as l};
