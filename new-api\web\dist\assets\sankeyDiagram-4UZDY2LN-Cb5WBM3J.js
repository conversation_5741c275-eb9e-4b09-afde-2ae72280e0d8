import{_ as g,q as _t,t as xt,a as vt,g as bt,c as wt,b as St,d as lt,B as Lt,e as q,X as Et,z as At,m as Tt}from"./index-DTzYmM8W.js";import{m as pt,s as nt,a as ct}from"./visactor-Csuqn0ps.js";import{o as Mt}from"./ordinal-DxaMzblD.js";import"./semi-ui-Csx8wKaA.js";import"./react-core-DskXcPn0.js";import"./tools-C3llIrvJ.js";import"./react-components-C55tCU1e.js";import"./i18n-bne0o_C4.js";import"./init-Gi6I4Gst.js";function Nt(t){for(var n=t.length/6|0,l=new Array(n),h=0;h<n;)l[h]="#"+t.slice(h*6,++h*6);return l}const It=Nt("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab");function Pt(t){return t.target.depth}function Ct(t){return t.depth}function Ot(t,n){return n-1-t.height}function mt(t,n){return t.sourceLinks.length?t.depth:n-1}function zt(t){return t.targetLinks.length?t.depth:t.sourceLinks.length?pt(t.sourceLinks,Pt)-1:0}function H(t){return function(){return t}}function ut(t,n){return Q(t.source,n.source)||t.index-n.index}function ht(t,n){return Q(t.target,n.target)||t.index-n.index}function Q(t,n){return t.y0-n.y0}function it(t){return t.value}function Dt(t){return t.index}function $t(t){return t.nodes}function jt(t){return t.links}function ft(t,n){const l=t.get(n);if(!l)throw new Error("missing: "+n);return l}function yt({nodes:t}){for(const n of t){let l=n.y0,h=l;for(const p of n.sourceLinks)p.y0=l+p.width/2,l+=p.width;for(const p of n.targetLinks)p.y1=h+p.width/2,h+=p.width}}function Bt(){let t=0,n=0,l=1,h=1,p=24,y=8,m,_=Dt,i=mt,r,o,x=$t,v=jt,f=6;function b(){const e={nodes:x.apply(null,arguments),links:v.apply(null,arguments)};return M(e),T(e),N(e),C(e),S(e),yt(e),e}b.update=function(e){return yt(e),e},b.nodeId=function(e){return arguments.length?(_=typeof e=="function"?e:H(e),b):_},b.nodeAlign=function(e){return arguments.length?(i=typeof e=="function"?e:H(e),b):i},b.nodeSort=function(e){return arguments.length?(r=e,b):r},b.nodeWidth=function(e){return arguments.length?(p=+e,b):p},b.nodePadding=function(e){return arguments.length?(y=m=+e,b):y},b.nodes=function(e){return arguments.length?(x=typeof e=="function"?e:H(e),b):x},b.links=function(e){return arguments.length?(v=typeof e=="function"?e:H(e),b):v},b.linkSort=function(e){return arguments.length?(o=e,b):o},b.size=function(e){return arguments.length?(t=n=0,l=+e[0],h=+e[1],b):[l-t,h-n]},b.extent=function(e){return arguments.length?(t=+e[0][0],l=+e[1][0],n=+e[0][1],h=+e[1][1],b):[[t,n],[l,h]]},b.iterations=function(e){return arguments.length?(f=+e,b):f};function M({nodes:e,links:u}){for(const[a,s]of e.entries())s.index=a,s.sourceLinks=[],s.targetLinks=[];const c=new Map(e.map((a,s)=>[_(a,s,e),a]));for(const[a,s]of u.entries()){s.index=a;let{source:k,target:w}=s;typeof k!="object"&&(k=s.source=ft(c,k)),typeof w!="object"&&(w=s.target=ft(c,w)),k.sourceLinks.push(s),w.targetLinks.push(s)}if(o!=null)for(const{sourceLinks:a,targetLinks:s}of e)a.sort(o),s.sort(o)}function T({nodes:e}){for(const u of e)u.value=u.fixedValue===void 0?Math.max(nt(u.sourceLinks,it),nt(u.targetLinks,it)):u.fixedValue}function N({nodes:e}){const u=e.length;let c=new Set(e),a=new Set,s=0;for(;c.size;){for(const k of c){k.depth=s;for(const{target:w}of k.sourceLinks)a.add(w)}if(++s>u)throw new Error("circular link");c=a,a=new Set}}function C({nodes:e}){const u=e.length;let c=new Set(e),a=new Set,s=0;for(;c.size;){for(const k of c){k.height=s;for(const{source:w}of k.targetLinks)a.add(w)}if(++s>u)throw new Error("circular link");c=a,a=new Set}}function D({nodes:e}){const u=ct(e,s=>s.depth)+1,c=(l-t-p)/(u-1),a=new Array(u);for(const s of e){const k=Math.max(0,Math.min(u-1,Math.floor(i.call(null,s,u))));s.layer=k,s.x0=t+k*c,s.x1=s.x0+p,a[k]?a[k].push(s):a[k]=[s]}if(r)for(const s of a)s.sort(r);return a}function R(e){const u=pt(e,c=>(h-n-(c.length-1)*m)/nt(c,it));for(const c of e){let a=n;for(const s of c){s.y0=a,s.y1=a+s.value*u,a=s.y1+m;for(const k of s.sourceLinks)k.width=k.value*u}a=(h-a+m)/(c.length+1);for(let s=0;s<c.length;++s){const k=c[s];k.y0+=a*(s+1),k.y1+=a*(s+1)}A(c)}}function S(e){const u=D(e);m=Math.min(y,(h-n)/(ct(u,c=>c.length)-1)),R(u);for(let c=0;c<f;++c){const a=Math.pow(.99,c),s=Math.max(1-a,(c+1)/f);B(u,a,s),P(u,a,s)}}function P(e,u,c){for(let a=1,s=e.length;a<s;++a){const k=e[a];for(const w of k){let L=0,F=0;for(const{source:X,value:et}of w.targetLinks){let Y=et*(w.layer-X.layer);L+=$(X,w)*Y,F+=Y}if(!(F>0))continue;let G=(L/F-w.y0)*u;w.y0+=G,w.y1+=G,E(w)}r===void 0&&k.sort(Q),O(k,c)}}function B(e,u,c){for(let a=e.length,s=a-2;s>=0;--s){const k=e[s];for(const w of k){let L=0,F=0;for(const{target:X,value:et}of w.sourceLinks){let Y=et*(X.layer-w.layer);L+=I(w,X)*Y,F+=Y}if(!(F>0))continue;let G=(L/F-w.y0)*u;w.y0+=G,w.y1+=G,E(w)}r===void 0&&k.sort(Q),O(k,c)}}function O(e,u){const c=e.length>>1,a=e[c];d(e,a.y0-m,c-1,u),z(e,a.y1+m,c+1,u),d(e,h,e.length-1,u),z(e,n,0,u)}function z(e,u,c,a){for(;c<e.length;++c){const s=e[c],k=(u-s.y0)*a;k>1e-6&&(s.y0+=k,s.y1+=k),u=s.y1+m}}function d(e,u,c,a){for(;c>=0;--c){const s=e[c],k=(s.y1-u)*a;k>1e-6&&(s.y0-=k,s.y1-=k),u=s.y0-m}}function E({sourceLinks:e,targetLinks:u}){if(o===void 0){for(const{source:{sourceLinks:c}}of u)c.sort(ht);for(const{target:{targetLinks:c}}of e)c.sort(ut)}}function A(e){if(o===void 0)for(const{sourceLinks:u,targetLinks:c}of e)u.sort(ht),c.sort(ut)}function $(e,u){let c=e.y0-(e.sourceLinks.length-1)*m/2;for(const{target:a,width:s}of e.sourceLinks){if(a===u)break;c+=s+m}for(const{source:a,width:s}of u.targetLinks){if(a===e)break;c-=s}return c}function I(e,u){let c=u.y0-(u.targetLinks.length-1)*m/2;for(const{source:a,width:s}of u.targetLinks){if(a===e)break;c+=s+m}for(const{target:a,width:s}of e.sourceLinks){if(a===u)break;c-=s}return c}return b}var st=Math.PI,rt=2*st,V=1e-6,Rt=rt-V;function ot(){this._x0=this._y0=this._x1=this._y1=null,this._=""}function kt(){return new ot}ot.prototype=kt.prototype={constructor:ot,moveTo:function(t,n){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+n)},closePath:function(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")},lineTo:function(t,n){this._+="L"+(this._x1=+t)+","+(this._y1=+n)},quadraticCurveTo:function(t,n,l,h){this._+="Q"+ +t+","+ +n+","+(this._x1=+l)+","+(this._y1=+h)},bezierCurveTo:function(t,n,l,h,p,y){this._+="C"+ +t+","+ +n+","+ +l+","+ +h+","+(this._x1=+p)+","+(this._y1=+y)},arcTo:function(t,n,l,h,p){t=+t,n=+n,l=+l,h=+h,p=+p;var y=this._x1,m=this._y1,_=l-t,i=h-n,r=y-t,o=m-n,x=r*r+o*o;if(p<0)throw new Error("negative radius: "+p);if(this._x1===null)this._+="M"+(this._x1=t)+","+(this._y1=n);else if(x>V)if(!(Math.abs(o*_-i*r)>V)||!p)this._+="L"+(this._x1=t)+","+(this._y1=n);else{var v=l-y,f=h-m,b=_*_+i*i,M=v*v+f*f,T=Math.sqrt(b),N=Math.sqrt(x),C=p*Math.tan((st-Math.acos((b+x-M)/(2*T*N)))/2),D=C/N,R=C/T;Math.abs(D-1)>V&&(this._+="L"+(t+D*r)+","+(n+D*o)),this._+="A"+p+","+p+",0,0,"+ +(o*v>r*f)+","+(this._x1=t+R*_)+","+(this._y1=n+R*i)}},arc:function(t,n,l,h,p,y){t=+t,n=+n,l=+l,y=!!y;var m=l*Math.cos(h),_=l*Math.sin(h),i=t+m,r=n+_,o=1^y,x=y?h-p:p-h;if(l<0)throw new Error("negative radius: "+l);this._x1===null?this._+="M"+i+","+r:(Math.abs(this._x1-i)>V||Math.abs(this._y1-r)>V)&&(this._+="L"+i+","+r),l&&(x<0&&(x=x%rt+rt),x>Rt?this._+="A"+l+","+l+",0,1,"+o+","+(t-m)+","+(n-_)+"A"+l+","+l+",0,1,"+o+","+(this._x1=i)+","+(this._y1=r):x>V&&(this._+="A"+l+","+l+",0,"+ +(x>=st)+","+o+","+(this._x1=t+l*Math.cos(p))+","+(this._y1=n+l*Math.sin(p))))},rect:function(t,n,l,h){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+n)+"h"+ +l+"v"+ +h+"h"+-l+"Z"},toString:function(){return this._}};function dt(t){return function(){return t}}function Ft(t){return t[0]}function Vt(t){return t[1]}var Wt=Array.prototype.slice;function Ut(t){return t.source}function Gt(t){return t.target}function Xt(t){var n=Ut,l=Gt,h=Ft,p=Vt,y=null;function m(){var _,i=Wt.call(arguments),r=n.apply(this,i),o=l.apply(this,i);if(y||(y=_=kt()),t(y,+h.apply(this,(i[0]=r,i)),+p.apply(this,i),+h.apply(this,(i[0]=o,i)),+p.apply(this,i)),_)return y=null,_+""||null}return m.source=function(_){return arguments.length?(n=_,m):n},m.target=function(_){return arguments.length?(l=_,m):l},m.x=function(_){return arguments.length?(h=typeof _=="function"?_:dt(+_),m):h},m.y=function(_){return arguments.length?(p=typeof _=="function"?_:dt(+_),m):p},m.context=function(_){return arguments.length?(y=_??null,m):y},m}function Yt(t,n,l,h,p){t.moveTo(n,l),t.bezierCurveTo(n=(n+h)/2,l,n,p,h,p)}function qt(){return Xt(Yt)}function Ht(t){return[t.source.x1,t.y0]}function Qt(t){return[t.target.x0,t.y1]}function Kt(){return qt().source(Ht).target(Qt)}var at=function(){var t=g(function(_,i,r,o){for(r=r||{},o=_.length;o--;r[_[o]]=i);return r},"o"),n=[1,9],l=[1,10],h=[1,5,10,12],p={trace:g(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SANKEY:4,NEWLINE:5,csv:6,opt_eof:7,record:8,csv_tail:9,EOF:10,"field[source]":11,COMMA:12,"field[target]":13,"field[value]":14,field:15,escaped:16,non_escaped:17,DQUOTE:18,ESCAPED_TEXT:19,NON_ESCAPED_TEXT:20,$accept:0,$end:1},terminals_:{2:"error",4:"SANKEY",5:"NEWLINE",10:"EOF",11:"field[source]",12:"COMMA",13:"field[target]",14:"field[value]",18:"DQUOTE",19:"ESCAPED_TEXT",20:"NON_ESCAPED_TEXT"},productions_:[0,[3,4],[6,2],[9,2],[9,0],[7,1],[7,0],[8,5],[15,1],[15,1],[16,3],[17,1]],performAction:g(function(i,r,o,x,v,f,b){var M=f.length-1;switch(v){case 7:const T=x.findOrCreateNode(f[M-4].trim().replaceAll('""','"')),N=x.findOrCreateNode(f[M-2].trim().replaceAll('""','"')),C=parseFloat(f[M].trim());x.addLink(T,N,C);break;case 8:case 9:case 11:this.$=f[M];break;case 10:this.$=f[M-1];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},{5:[1,3]},{6:4,8:5,15:6,16:7,17:8,18:n,20:l},{1:[2,6],7:11,10:[1,12]},t(l,[2,4],{9:13,5:[1,14]}),{12:[1,15]},t(h,[2,8]),t(h,[2,9]),{19:[1,16]},t(h,[2,11]),{1:[2,1]},{1:[2,5]},t(l,[2,2]),{6:17,8:5,15:6,16:7,17:8,18:n,20:l},{15:18,16:7,17:8,18:n,20:l},{18:[1,19]},t(l,[2,3]),{12:[1,20]},t(h,[2,10]),{15:21,16:7,17:8,18:n,20:l},t([1,5,10],[2,7])],defaultActions:{11:[2,1],12:[2,5]},parseError:g(function(i,r){if(r.recoverable)this.trace(i);else{var o=new Error(i);throw o.hash=r,o}},"parseError"),parse:g(function(i){var r=this,o=[0],x=[],v=[null],f=[],b=this.table,M="",T=0,N=0,C=2,D=1,R=f.slice.call(arguments,1),S=Object.create(this.lexer),P={yy:{}};for(var B in this.yy)Object.prototype.hasOwnProperty.call(this.yy,B)&&(P.yy[B]=this.yy[B]);S.setInput(i,P.yy),P.yy.lexer=S,P.yy.parser=this,typeof S.yylloc>"u"&&(S.yylloc={});var O=S.yylloc;f.push(O);var z=S.options&&S.options.ranges;typeof P.yy.parseError=="function"?this.parseError=P.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function d(L){o.length=o.length-2*L,v.length=v.length-L,f.length=f.length-L}g(d,"popStack");function E(){var L;return L=x.pop()||S.lex()||D,typeof L!="number"&&(L instanceof Array&&(x=L,L=x.pop()),L=r.symbols_[L]||L),L}g(E,"lex");for(var A,$,I,e,u={},c,a,s,k;;){if($=o[o.length-1],this.defaultActions[$]?I=this.defaultActions[$]:((A===null||typeof A>"u")&&(A=E()),I=b[$]&&b[$][A]),typeof I>"u"||!I.length||!I[0]){var w="";k=[];for(c in b[$])this.terminals_[c]&&c>C&&k.push("'"+this.terminals_[c]+"'");S.showPosition?w="Parse error on line "+(T+1)+`:
`+S.showPosition()+`
Expecting `+k.join(", ")+", got '"+(this.terminals_[A]||A)+"'":w="Parse error on line "+(T+1)+": Unexpected "+(A==D?"end of input":"'"+(this.terminals_[A]||A)+"'"),this.parseError(w,{text:S.match,token:this.terminals_[A]||A,line:S.yylineno,loc:O,expected:k})}if(I[0]instanceof Array&&I.length>1)throw new Error("Parse Error: multiple actions possible at state: "+$+", token: "+A);switch(I[0]){case 1:o.push(A),v.push(S.yytext),f.push(S.yylloc),o.push(I[1]),A=null,N=S.yyleng,M=S.yytext,T=S.yylineno,O=S.yylloc;break;case 2:if(a=this.productions_[I[1]][1],u.$=v[v.length-a],u._$={first_line:f[f.length-(a||1)].first_line,last_line:f[f.length-1].last_line,first_column:f[f.length-(a||1)].first_column,last_column:f[f.length-1].last_column},z&&(u._$.range=[f[f.length-(a||1)].range[0],f[f.length-1].range[1]]),e=this.performAction.apply(u,[M,N,T,P.yy,I[1],v,f].concat(R)),typeof e<"u")return e;a&&(o=o.slice(0,-1*a*2),v=v.slice(0,-1*a),f=f.slice(0,-1*a)),o.push(this.productions_[I[1]][0]),v.push(u.$),f.push(u._$),s=b[o[o.length-2]][o[o.length-1]],o.push(s);break;case 3:return!0}}return!0},"parse")},y=function(){var _={EOF:1,parseError:g(function(r,o){if(this.yy.parser)this.yy.parser.parseError(r,o);else throw new Error(r)},"parseError"),setInput:g(function(i,r){return this.yy=r||this.yy||{},this._input=i,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:g(function(){var i=this._input[0];this.yytext+=i,this.yyleng++,this.offset++,this.match+=i,this.matched+=i;var r=i.match(/(?:\r\n?|\n).*/g);return r?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),i},"input"),unput:g(function(i){var r=i.length,o=i.split(/(?:\r\n?|\n)/g);this._input=i+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-r),this.offset-=r;var x=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),o.length-1&&(this.yylineno-=o.length-1);var v=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:o?(o.length===x.length?this.yylloc.first_column:0)+x[x.length-o.length].length-o[0].length:this.yylloc.first_column-r},this.options.ranges&&(this.yylloc.range=[v[0],v[0]+this.yyleng-r]),this.yyleng=this.yytext.length,this},"unput"),more:g(function(){return this._more=!0,this},"more"),reject:g(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:g(function(i){this.unput(this.match.slice(i))},"less"),pastInput:g(function(){var i=this.matched.substr(0,this.matched.length-this.match.length);return(i.length>20?"...":"")+i.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:g(function(){var i=this.match;return i.length<20&&(i+=this._input.substr(0,20-i.length)),(i.substr(0,20)+(i.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:g(function(){var i=this.pastInput(),r=new Array(i.length+1).join("-");return i+this.upcomingInput()+`
`+r+"^"},"showPosition"),test_match:g(function(i,r){var o,x,v;if(this.options.backtrack_lexer&&(v={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(v.yylloc.range=this.yylloc.range.slice(0))),x=i[0].match(/(?:\r\n?|\n).*/g),x&&(this.yylineno+=x.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:x?x[x.length-1].length-x[x.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+i[0].length},this.yytext+=i[0],this.match+=i[0],this.matches=i,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(i[0].length),this.matched+=i[0],o=this.performAction.call(this,this.yy,this,r,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),o)return o;if(this._backtrack){for(var f in v)this[f]=v[f];return!1}return!1},"test_match"),next:g(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var i,r,o,x;this._more||(this.yytext="",this.match="");for(var v=this._currentRules(),f=0;f<v.length;f++)if(o=this._input.match(this.rules[v[f]]),o&&(!r||o[0].length>r[0].length)){if(r=o,x=f,this.options.backtrack_lexer){if(i=this.test_match(o,v[f]),i!==!1)return i;if(this._backtrack){r=!1;continue}else return!1}else if(!this.options.flex)break}return r?(i=this.test_match(r,v[x]),i!==!1?i:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:g(function(){var r=this.next();return r||this.lex()},"lex"),begin:g(function(r){this.conditionStack.push(r)},"begin"),popState:g(function(){var r=this.conditionStack.length-1;return r>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:g(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:g(function(r){return r=this.conditionStack.length-1-Math.abs(r||0),r>=0?this.conditionStack[r]:"INITIAL"},"topState"),pushState:g(function(r){this.begin(r)},"pushState"),stateStackSize:g(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:g(function(r,o,x,v){switch(x){case 0:return this.pushState("csv"),4;case 1:return 10;case 2:return 5;case 3:return 12;case 4:return this.pushState("escaped_text"),18;case 5:return 20;case 6:return this.popState("escaped_text"),18;case 7:return 19}},"anonymous"),rules:[/^(?:sankey-beta\b)/i,/^(?:$)/i,/^(?:((\u000D\u000A)|(\u000A)))/i,/^(?:(\u002C))/i,/^(?:(\u0022))/i,/^(?:([\u0020-\u0021\u0023-\u002B\u002D-\u007E])*)/i,/^(?:(\u0022)(?!(\u0022)))/i,/^(?:(([\u0020-\u0021\u0023-\u002B\u002D-\u007E])|(\u002C)|(\u000D)|(\u000A)|(\u0022)(\u0022))*)/i],conditions:{csv:{rules:[1,2,3,4,5,6,7],inclusive:!1},escaped_text:{rules:[6,7],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7],inclusive:!0}}};return _}();p.lexer=y;function m(){this.yy={}}return g(m,"Parser"),m.prototype=p,p.Parser=m,new m}();at.parser=at;var K=at,J=[],tt=[],Z=new Map,Zt=g(()=>{J=[],tt=[],Z=new Map,At()},"clear"),W,Jt=(W=class{constructor(n,l,h=0){this.source=n,this.target=l,this.value=h}},g(W,"SankeyLink"),W),te=g((t,n,l)=>{J.push(new Jt(t,n,l))},"addLink"),U,ee=(U=class{constructor(n){this.ID=n}},g(U,"SankeyNode"),U),ne=g(t=>{t=Tt.sanitizeText(t,lt());let n=Z.get(t);return n===void 0&&(n=new ee(t),Z.set(t,n),tt.push(n)),n},"findOrCreateNode"),ie=g(()=>tt,"getNodes"),se=g(()=>J,"getLinks"),re=g(()=>({nodes:tt.map(t=>({id:t.ID})),links:J.map(t=>({source:t.source.ID,target:t.target.ID,value:t.value}))}),"getGraph"),oe={nodesMap:Z,getConfig:g(()=>lt().sankey,"getConfig"),getNodes:ie,getLinks:se,getGraph:re,addLink:te,findOrCreateNode:ne,getAccTitle:St,setAccTitle:wt,getAccDescription:bt,setAccDescription:vt,getDiagramTitle:xt,setDiagramTitle:_t,clear:Zt},j,gt=(j=class{static next(n){return new j(n+ ++j.count)}constructor(n){this.id=n,this.href=`#${n}`}toString(){return"url("+this.href+")"}},g(j,"Uid"),j.count=0,j),ae={left:Ct,right:Ot,center:zt,justify:mt},le=g(function(t,n,l,h){const{securityLevel:p,sankey:y}=lt(),m=Lt.sankey;let _;p==="sandbox"&&(_=q("#i"+n));const i=p==="sandbox"?q(_.nodes()[0].contentDocument.body):q("body"),r=p==="sandbox"?i.select(`[id="${n}"]`):q(`[id="${n}"]`),o=(y==null?void 0:y.width)??m.width,x=(y==null?void 0:y.height)??m.width,v=(y==null?void 0:y.useMaxWidth)??m.useMaxWidth,f=(y==null?void 0:y.nodeAlignment)??m.nodeAlignment,b=(y==null?void 0:y.prefix)??m.prefix,M=(y==null?void 0:y.suffix)??m.suffix,T=(y==null?void 0:y.showValues)??m.showValues,N=h.db.getGraph(),C=ae[f];Bt().nodeId(d=>d.id).nodeWidth(10).nodePadding(10+(T?15:0)).nodeAlign(C).extent([[0,0],[o,x]])(N);const S=Mt(It);r.append("g").attr("class","nodes").selectAll(".node").data(N.nodes).join("g").attr("class","node").attr("id",d=>(d.uid=gt.next("node-")).id).attr("transform",function(d){return"translate("+d.x0+","+d.y0+")"}).attr("x",d=>d.x0).attr("y",d=>d.y0).append("rect").attr("height",d=>d.y1-d.y0).attr("width",d=>d.x1-d.x0).attr("fill",d=>S(d.id));const P=g(({id:d,value:E})=>T?`${d}
${b}${Math.round(E*100)/100}${M}`:d,"getText");r.append("g").attr("class","node-labels").attr("font-size",14).selectAll("text").data(N.nodes).join("text").attr("x",d=>d.x0<o/2?d.x1+6:d.x0-6).attr("y",d=>(d.y1+d.y0)/2).attr("dy",`${T?"0":"0.35"}em`).attr("text-anchor",d=>d.x0<o/2?"start":"end").text(P);const B=r.append("g").attr("class","links").attr("fill","none").attr("stroke-opacity",.5).selectAll(".link").data(N.links).join("g").attr("class","link").style("mix-blend-mode","multiply"),O=(y==null?void 0:y.linkColor)??"gradient";if(O==="gradient"){const d=B.append("linearGradient").attr("id",E=>(E.uid=gt.next("linearGradient-")).id).attr("gradientUnits","userSpaceOnUse").attr("x1",E=>E.source.x1).attr("x2",E=>E.target.x0);d.append("stop").attr("offset","0%").attr("stop-color",E=>S(E.source.id)),d.append("stop").attr("offset","100%").attr("stop-color",E=>S(E.target.id))}let z;switch(O){case"gradient":z=g(d=>d.uid,"coloring");break;case"source":z=g(d=>S(d.source.id),"coloring");break;case"target":z=g(d=>S(d.target.id),"coloring");break;default:z=O}B.append("path").attr("d",Kt()).attr("stroke",z).attr("stroke-width",d=>Math.max(1,d.width)),Et(void 0,r,0,v)},"draw"),ce={draw:le},ue=g(t=>t.replaceAll(/^[^\S\n\r]+|[^\S\n\r]+$/g,"").replaceAll(/([\n\r])+/g,`
`).trim(),"prepareTextForParsing"),he=g(t=>`.label {
      font-family: ${t.fontFamily};
    }`,"getStyles"),fe=he,ye=K.parse.bind(K);K.parse=t=>ye(ue(t));var we={styles:fe,parser:K,db:oe,renderer:ce};export{we as diagram};
