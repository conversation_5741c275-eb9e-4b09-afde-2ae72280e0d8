var Mu=Object.defineProperty;var Du=(n,e,t)=>e in n?Mu(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var Ze=(n,e,t)=>Du(n,typeof e!="symbol"?e+"":e,t);import{ce as Fu,cf as Gu,bf as wl,b_ as Uu,bj as Bu,bg as ee,aE as Vu,aF as $a,bQ as Ku,bT as _l,bU as Ll,bR as Wu,c3 as ka,aH as At,aI as D,bh as Sa,bb as ju,cg as ht}from"./index-DTzYmM8W.js";import{k as Yt,j as Hs,g as rn,S as Hu,w as zu,x as qu,c as bl,v as z,y as Ol,l as Yu,z as Xu,A as Ju,B as Qu,C as Zu,a as Pl,d as C,i as Ye,r as le,f as ke,D as Y}from"./_baseUniq-ZSwxWUik.js";import{j as zs,m as S,d as ed,f as Ne,g as Xt,h as N,i as qs,l as Jt,e as td}from"./_basePickBy-G-l42atP.js";import{c as ne}from"./clone-qQLK9m8k.js";var nd=Object.prototype,rd=nd.hasOwnProperty,$e=Fu(function(n,e){if(Gu(e)||wl(e)){Uu(e,Yt(e),n);return}for(var t in e)rd.call(e,t)&&Bu(n,t,e[t])});function Ml(n,e,t){var r=-1,i=n.length;e<0&&(e=-e>i?0:i+e),t=t>i?i:t,t<0&&(t+=i),i=e>t?0:t-e>>>0,e>>>=0;for(var s=Array(i);++r<i;)s[r]=n[r+e];return s}function er(n){for(var e=-1,t=n==null?0:n.length,r=0,i=[];++e<t;){var s=n[e];s&&(i[r++]=s)}return i}function id(n,e,t,r){for(var i=-1,s=n==null?0:n.length;++i<s;){var a=n[i];e(r,a,t(a),n)}return r}function sd(n,e,t,r){return Hs(n,function(i,s,a){e(r,i,t(i),a)}),r}function ad(n,e){return function(t,r){var i=ee(t)?id:sd,s=e?e():{};return i(t,n,rn(r),s)}}var od=200;function ld(n,e,t,r){var i=-1,s=zu,a=!0,o=n.length,l=[],c=e.length;if(!o)return l;e.length>=od&&(s=qu,a=!1,e=new Hu(e));e:for(;++i<o;){var u=n[i],d=u;if(u=u!==0?u:0,a&&d===d){for(var h=c;h--;)if(e[h]===d)continue e;l.push(u)}else s(e,d,r)||l.push(u)}return l}var pi=Vu(function(n,e){return $a(n)?ld(n,bl(e,1,$a,!0)):[]});function J(n,e,t){var r=n==null?0:n.length;return r?(e=e===void 0?1:zs(e),Ml(n,e<0?0:e,r)):[]}function Yn(n,e,t){var r=n==null?0:n.length;return r?(e=e===void 0?1:zs(e),e=r-e,Ml(n,0,e<0?0:e)):[]}function cd(n,e){for(var t=-1,r=n==null?0:n.length;++t<r;)if(!e(n[t],t,n))return!1;return!0}function ud(n,e){var t=!0;return Hs(n,function(r,i,s){return t=!!e(r,i,s),t}),t}function Oe(n,e,t){var r=ee(n)?cd:ud;return r(n,rn(e))}function Pe(n){return n&&n.length?n[0]:void 0}function Ee(n,e){return bl(S(n,e))}var dd=Object.prototype,fd=dd.hasOwnProperty,hd=ad(function(n,e,t){fd.call(n,t)?n[t].push(e):Ku(n,t,[e])}),pd="[object String]";function he(n){return typeof n=="string"||!ee(n)&&_l(n)&&Ll(n)==pd}var md=Math.max;function de(n,e,t,r){n=wl(n)?n:z(n),t=t?zs(t):0;var i=n.length;return t<0&&(t=md(i+t,0)),he(n)?t<=i&&n.indexOf(e,t)>-1:!!i&&Ol(n,e,t)>-1}function xa(n,e,t){var r=n==null?0:n.length;if(!r)return-1;var i=0;return Ol(n,e,i)}var gd="[object RegExp]";function yd(n){return _l(n)&&Ll(n)==gd}var Ia=ka&&ka.isRegExp,Xe=Ia?Wu(Ia):yd,Td="Expected a function";function Rd(n){if(typeof n!="function")throw new TypeError(Td);return function(){var e=arguments;switch(e.length){case 0:return!n.call(this);case 1:return!n.call(this,e[0]);case 2:return!n.call(this,e[0],e[1]);case 3:return!n.call(this,e[0],e[1],e[2])}return!n.apply(this,e)}}function Me(n,e){if(n==null)return{};var t=Yu(Xu(n),function(r){return[r]});return e=rn(e),ed(n,t,function(r,i){return e(r,i[0])})}function mi(n,e){var t=ee(n)?Ju:Qu;return t(n,Rd(rn(e)))}function vd(n,e){var t;return Hs(n,function(r,i,s){return t=e(r,i,s),!t}),!!t}function Dl(n,e,t){var r=ee(n)?Zu:vd;return r(n,rn(e))}function Ys(n){return n&&n.length?Pl(n):[]}function Ad(n,e){return n&&n.length?Pl(n,rn(e)):[]}function ae(n){return typeof n=="object"&&n!==null&&typeof n.$type=="string"}function Ue(n){return typeof n=="object"&&n!==null&&typeof n.$refText=="string"}function Ed(n){return typeof n=="object"&&n!==null&&typeof n.name=="string"&&typeof n.type=="string"&&typeof n.path=="string"}function Nr(n){return typeof n=="object"&&n!==null&&ae(n.container)&&Ue(n.reference)&&typeof n.message=="string"}class Fl{constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,t){return ae(e)&&this.isSubtype(e.$type,t)}isSubtype(e,t){if(e===t)return!0;let r=this.subtypes[e];r||(r=this.subtypes[e]={});const i=r[t];if(i!==void 0)return i;{const s=this.computeIsSubtype(e,t);return r[t]=s,s}}getAllSubTypes(e){const t=this.allSubtypes[e];if(t)return t;{const r=this.getAllTypes(),i=[];for(const s of r)this.isSubtype(s,e)&&i.push(s);return this.allSubtypes[e]=i,i}}}function Xn(n){return typeof n=="object"&&n!==null&&Array.isArray(n.content)}function Gl(n){return typeof n=="object"&&n!==null&&typeof n.tokenType=="object"}function Ul(n){return Xn(n)&&typeof n.fullText=="string"}class Q{constructor(e,t){this.startFn=e,this.nextFn=t}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){const e=this.iterator();let t=0,r=e.next();for(;!r.done;)t++,r=e.next();return t}toArray(){const e=[],t=this.iterator();let r;do r=t.next(),r.value!==void 0&&e.push(r.value);while(!r.done);return e}toSet(){return new Set(this)}toMap(e,t){const r=this.map(i=>[e?e(i):i,t?t(i):i]);return new Map(r)}toString(){return this.join()}concat(e){return new Q(()=>({first:this.startFn(),firstDone:!1,iterator:e[Symbol.iterator]()}),t=>{let r;if(!t.firstDone){do if(r=this.nextFn(t.first),!r.done)return r;while(!r.done);t.firstDone=!0}do if(r=t.iterator.next(),!r.done)return r;while(!r.done);return ve})}join(e=","){const t=this.iterator();let r="",i,s=!1;do i=t.next(),i.done||(s&&(r+=e),r+=$d(i.value)),s=!0;while(!i.done);return r}indexOf(e,t=0){const r=this.iterator();let i=0,s=r.next();for(;!s.done;){if(i>=t&&s.value===e)return i;s=r.next(),i++}return-1}every(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(!e(r.value))return!1;r=t.next()}return!0}some(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(e(r.value))return!0;r=t.next()}return!1}forEach(e){const t=this.iterator();let r=0,i=t.next();for(;!i.done;)e(i.value,r),i=t.next(),r++}map(e){return new Q(this.startFn,t=>{const{done:r,value:i}=this.nextFn(t);return r?ve:{done:!1,value:e(i)}})}filter(e){return new Q(this.startFn,t=>{let r;do if(r=this.nextFn(t),!r.done&&e(r.value))return r;while(!r.done);return ve})}nonNullable(){return this.filter(e=>e!=null)}reduce(e,t){const r=this.iterator();let i=t,s=r.next();for(;!s.done;)i===void 0?i=s.value:i=e(i,s.value),s=r.next();return i}reduceRight(e,t){return this.recursiveReduce(this.iterator(),e,t)}recursiveReduce(e,t,r){const i=e.next();if(i.done)return r;const s=this.recursiveReduce(e,t,r);return s===void 0?i.value:t(s,i.value)}find(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(e(r.value))return r.value;r=t.next()}}findIndex(e){const t=this.iterator();let r=0,i=t.next();for(;!i.done;){if(e(i.value))return r;i=t.next(),r++}return-1}includes(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(r.value===e)return!0;r=t.next()}return!1}flatMap(e){return new Q(()=>({this:this.startFn()}),t=>{do{if(t.iterator){const s=t.iterator.next();if(s.done)t.iterator=void 0;else return s}const{done:r,value:i}=this.nextFn(t.this);if(!r){const s=e(i);if(Wr(s))t.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}}while(t.iterator);return ve})}flat(e){if(e===void 0&&(e=1),e<=0)return this;const t=e>1?this.flat(e-1):this;return new Q(()=>({this:t.startFn()}),r=>{do{if(r.iterator){const a=r.iterator.next();if(a.done)r.iterator=void 0;else return a}const{done:i,value:s}=t.nextFn(r.this);if(!i)if(Wr(s))r.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}while(r.iterator);return ve})}head(){const t=this.iterator().next();if(!t.done)return t.value}tail(e=1){return new Q(()=>{const t=this.startFn();for(let r=0;r<e;r++)if(this.nextFn(t).done)return t;return t},this.nextFn)}limit(e){return new Q(()=>({size:0,state:this.startFn()}),t=>(t.size++,t.size>e?ve:this.nextFn(t.state)))}distinct(e){return new Q(()=>({set:new Set,internalState:this.startFn()}),t=>{let r;do if(r=this.nextFn(t.internalState),!r.done){const i=e?e(r.value):r.value;if(!t.set.has(i))return t.set.add(i),r}while(!r.done);return ve})}exclude(e,t){const r=new Set;for(const i of e){const s=t?t(i):i;r.add(s)}return this.filter(i=>{const s=t?t(i):i;return!r.has(s)})}}function $d(n){return typeof n=="string"?n:typeof n>"u"?"undefined":typeof n.toString=="function"?n.toString():Object.prototype.toString.call(n)}function Wr(n){return!!n&&typeof n[Symbol.iterator]=="function"}const kd=new Q(()=>{},()=>ve),ve=Object.freeze({done:!0,value:void 0});function Z(...n){if(n.length===1){const e=n[0];if(e instanceof Q)return e;if(Wr(e))return new Q(()=>e[Symbol.iterator](),t=>t.next());if(typeof e.length=="number")return new Q(()=>({index:0}),t=>t.index<e.length?{done:!1,value:e[t.index++]}:ve)}return n.length>1?new Q(()=>({collIndex:0,arrIndex:0}),e=>{do{if(e.iterator){const t=e.iterator.next();if(!t.done)return t;e.iterator=void 0}if(e.array){if(e.arrIndex<e.array.length)return{done:!1,value:e.array[e.arrIndex++]};e.array=void 0,e.arrIndex=0}if(e.collIndex<n.length){const t=n[e.collIndex++];Wr(t)?e.iterator=t[Symbol.iterator]():t&&typeof t.length=="number"&&(e.array=t)}}while(e.iterator||e.array||e.collIndex<n.length);return ve}):kd}class Xs extends Q{constructor(e,t,r){super(()=>({iterators:r!=null&&r.includeRoot?[[e][Symbol.iterator]()]:[t(e)[Symbol.iterator]()],pruned:!1}),i=>{for(i.pruned&&(i.iterators.pop(),i.pruned=!1);i.iterators.length>0;){const a=i.iterators[i.iterators.length-1].next();if(a.done)i.iterators.pop();else return i.iterators.push(t(a.value)[Symbol.iterator]()),a}return ve})}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),prune:()=>{e.state.pruned=!0},[Symbol.iterator]:()=>e};return e}}var os;(function(n){function e(s){return s.reduce((a,o)=>a+o,0)}n.sum=e;function t(s){return s.reduce((a,o)=>a*o,0)}n.product=t;function r(s){return s.reduce((a,o)=>Math.min(a,o))}n.min=r;function i(s){return s.reduce((a,o)=>Math.max(a,o))}n.max=i})(os||(os={}));function ls(n){return new Xs(n,e=>Xn(e)?e.content:[],{includeRoot:!0})}function Sd(n,e){for(;n.container;)if(n=n.container,n===e)return!0;return!1}function cs(n){return{start:{character:n.startColumn-1,line:n.startLine-1},end:{character:n.endColumn,line:n.endLine-1}}}function jr(n){if(!n)return;const{offset:e,end:t,range:r}=n;return{range:r,offset:e,end:t,length:t-e}}var He;(function(n){n[n.Before=0]="Before",n[n.After=1]="After",n[n.OverlapFront=2]="OverlapFront",n[n.OverlapBack=3]="OverlapBack",n[n.Inside=4]="Inside",n[n.Outside=5]="Outside"})(He||(He={}));function xd(n,e){if(n.end.line<e.start.line||n.end.line===e.start.line&&n.end.character<=e.start.character)return He.Before;if(n.start.line>e.end.line||n.start.line===e.end.line&&n.start.character>=e.end.character)return He.After;const t=n.start.line>e.start.line||n.start.line===e.start.line&&n.start.character>=e.start.character,r=n.end.line<e.end.line||n.end.line===e.end.line&&n.end.character<=e.end.character;return t&&r?He.Inside:t?He.OverlapBack:r?He.OverlapFront:He.Outside}function Id(n,e){return xd(n,e)>He.After}const Cd=/^[\w\p{L}]$/u;function Nd(n,e){if(n){const t=wd(n,!0);if(t&&Ca(t,e))return t;if(Ul(n)){const r=n.content.findIndex(i=>!i.hidden);for(let i=r-1;i>=0;i--){const s=n.content[i];if(Ca(s,e))return s}}}}function Ca(n,e){return Gl(n)&&e.includes(n.tokenType.name)}function wd(n,e=!0){for(;n.container;){const t=n.container;let r=t.content.indexOf(n);for(;r>0;){r--;const i=t.content[r];if(e||!i.hidden)return i}n=t}}class Bl extends Error{constructor(e,t){super(e?`${t} at ${e.range.start.line}:${e.range.start.character}`:t)}}function tr(n){throw new Error("Error! The input value was not handled.")}const lr="AbstractRule",cr="AbstractType",bi="Condition",Na="TypeDefinition",Oi="ValueLiteral",fn="AbstractElement";function _d(n){return M.isInstance(n,fn)}const ur="ArrayLiteral",dr="ArrayType",hn="BooleanLiteral";function Ld(n){return M.isInstance(n,hn)}const pn="Conjunction";function bd(n){return M.isInstance(n,pn)}const mn="Disjunction";function Od(n){return M.isInstance(n,mn)}const fr="Grammar",Pi="GrammarImport",gn="InferredType";function Vl(n){return M.isInstance(n,gn)}const yn="Interface";function Kl(n){return M.isInstance(n,yn)}const Mi="NamedArgument",Tn="Negation";function Pd(n){return M.isInstance(n,Tn)}const hr="NumberLiteral",pr="Parameter",Rn="ParameterReference";function Md(n){return M.isInstance(n,Rn)}const vn="ParserRule";function we(n){return M.isInstance(n,vn)}const mr="ReferenceType",wr="ReturnType";function Dd(n){return M.isInstance(n,wr)}const An="SimpleType";function Fd(n){return M.isInstance(n,An)}const gr="StringLiteral",Ct="TerminalRule";function Et(n){return M.isInstance(n,Ct)}const En="Type";function Wl(n){return M.isInstance(n,En)}const Di="TypeAttribute",yr="UnionType",$n="Action";function gi(n){return M.isInstance(n,$n)}const kn="Alternatives";function jl(n){return M.isInstance(n,kn)}const Sn="Assignment";function mt(n){return M.isInstance(n,Sn)}const xn="CharacterRange";function Gd(n){return M.isInstance(n,xn)}const In="CrossReference";function Js(n){return M.isInstance(n,In)}const Cn="EndOfFile";function Ud(n){return M.isInstance(n,Cn)}const Nn="Group";function Qs(n){return M.isInstance(n,Nn)}const wn="Keyword";function gt(n){return M.isInstance(n,wn)}const _n="NegatedToken";function Bd(n){return M.isInstance(n,_n)}const Ln="RegexToken";function Vd(n){return M.isInstance(n,Ln)}const bn="RuleCall";function yt(n){return M.isInstance(n,bn)}const On="TerminalAlternatives";function Kd(n){return M.isInstance(n,On)}const Pn="TerminalGroup";function Wd(n){return M.isInstance(n,Pn)}const Mn="TerminalRuleCall";function jd(n){return M.isInstance(n,Mn)}const Dn="UnorderedGroup";function Hl(n){return M.isInstance(n,Dn)}const Fn="UntilToken";function Hd(n){return M.isInstance(n,Fn)}const Gn="Wildcard";function zd(n){return M.isInstance(n,Gn)}class zl extends Fl{getAllTypes(){return[fn,lr,cr,$n,kn,ur,dr,Sn,hn,xn,bi,pn,In,mn,Cn,fr,Pi,Nn,gn,yn,wn,Mi,_n,Tn,hr,pr,Rn,vn,mr,Ln,wr,bn,An,gr,On,Pn,Ct,Mn,En,Di,Na,yr,Dn,Fn,Oi,Gn]}computeIsSubtype(e,t){switch(e){case $n:case kn:case Sn:case xn:case In:case Cn:case Nn:case wn:case _n:case Ln:case bn:case On:case Pn:case Mn:case Dn:case Fn:case Gn:return this.isSubtype(fn,t);case ur:case hr:case gr:return this.isSubtype(Oi,t);case dr:case mr:case An:case yr:return this.isSubtype(Na,t);case hn:return this.isSubtype(bi,t)||this.isSubtype(Oi,t);case pn:case mn:case Tn:case Rn:return this.isSubtype(bi,t);case gn:case yn:case En:return this.isSubtype(cr,t);case vn:return this.isSubtype(lr,t)||this.isSubtype(cr,t);case Ct:return this.isSubtype(lr,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return cr;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return lr;case"Grammar:usedGrammars":return fr;case"NamedArgument:parameter":case"ParameterReference:parameter":return pr;case"TerminalRuleCall:rule":return Ct;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case fn:return{name:fn,properties:[{name:"cardinality"},{name:"lookahead"}]};case ur:return{name:ur,properties:[{name:"elements",defaultValue:[]}]};case dr:return{name:dr,properties:[{name:"elementType"}]};case hn:return{name:hn,properties:[{name:"true",defaultValue:!1}]};case pn:return{name:pn,properties:[{name:"left"},{name:"right"}]};case mn:return{name:mn,properties:[{name:"left"},{name:"right"}]};case fr:return{name:fr,properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case Pi:return{name:Pi,properties:[{name:"path"}]};case gn:return{name:gn,properties:[{name:"name"}]};case yn:return{name:yn,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case Mi:return{name:Mi,properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case Tn:return{name:Tn,properties:[{name:"value"}]};case hr:return{name:hr,properties:[{name:"value"}]};case pr:return{name:pr,properties:[{name:"name"}]};case Rn:return{name:Rn,properties:[{name:"parameter"}]};case vn:return{name:vn,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case mr:return{name:mr,properties:[{name:"referenceType"}]};case wr:return{name:wr,properties:[{name:"name"}]};case An:return{name:An,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case gr:return{name:gr,properties:[{name:"value"}]};case Ct:return{name:Ct,properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case En:return{name:En,properties:[{name:"name"},{name:"type"}]};case Di:return{name:Di,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case yr:return{name:yr,properties:[{name:"types",defaultValue:[]}]};case $n:return{name:$n,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case kn:return{name:kn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Sn:return{name:Sn,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case xn:return{name:xn,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case In:return{name:In,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case Cn:return{name:Cn,properties:[{name:"cardinality"},{name:"lookahead"}]};case Nn:return{name:Nn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case wn:return{name:wn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case _n:return{name:_n,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case Ln:return{name:Ln,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case bn:return{name:bn,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case On:return{name:On,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Pn:return{name:Pn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Mn:return{name:Mn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case Dn:return{name:Dn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Fn:return{name:Fn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case Gn:return{name:Gn,properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}}const M=new zl;function qd(n){for(const[e,t]of Object.entries(n))e.startsWith("$")||(Array.isArray(t)?t.forEach((r,i)=>{ae(r)&&(r.$container=n,r.$containerProperty=e,r.$containerIndex=i)}):ae(t)&&(t.$container=n,t.$containerProperty=e))}function yi(n,e){let t=n;for(;t;){if(e(t))return t;t=t.$container}}function et(n){const t=us(n).$document;if(!t)throw new Error("AST node has no document.");return t}function us(n){for(;n.$container;)n=n.$container;return n}function Zs(n,e){if(!n)throw new Error("Node must be an AstNode.");const t=e==null?void 0:e.range;return new Q(()=>({keys:Object.keys(n),keyIndex:0,arrayIndex:0}),r=>{for(;r.keyIndex<r.keys.length;){const i=r.keys[r.keyIndex];if(!i.startsWith("$")){const s=n[i];if(ae(s)){if(r.keyIndex++,wa(s,t))return{done:!1,value:s}}else if(Array.isArray(s)){for(;r.arrayIndex<s.length;){const a=r.arrayIndex++,o=s[a];if(ae(o)&&wa(o,t))return{done:!1,value:o}}r.arrayIndex=0}}r.keyIndex++}return ve})}function nr(n,e){if(!n)throw new Error("Root node must be an AstNode.");return new Xs(n,t=>Zs(t,e))}function wt(n,e){if(!n)throw new Error("Root node must be an AstNode.");return new Xs(n,t=>Zs(t,e),{includeRoot:!0})}function wa(n,e){var t;if(!e)return!0;const r=(t=n.$cstNode)===null||t===void 0?void 0:t.range;return r?Id(r,e):!1}function ql(n){return new Q(()=>({keys:Object.keys(n),keyIndex:0,arrayIndex:0}),e=>{for(;e.keyIndex<e.keys.length;){const t=e.keys[e.keyIndex];if(!t.startsWith("$")){const r=n[t];if(Ue(r))return e.keyIndex++,{done:!1,value:{reference:r,container:n,property:t}};if(Array.isArray(r)){for(;e.arrayIndex<r.length;){const i=e.arrayIndex++,s=r[i];if(Ue(s))return{done:!1,value:{reference:s,container:n,property:t,index:i}}}e.arrayIndex=0}}e.keyIndex++}return ve})}function Yd(n,e){const t=n.getTypeMetaData(e.$type),r=e;for(const i of t.properties)i.defaultValue!==void 0&&r[i.name]===void 0&&(r[i.name]=Yl(i.defaultValue))}function Yl(n){return Array.isArray(n)?[...n.map(Yl)]:n}function w(n){return n.charCodeAt(0)}function Fi(n,e){Array.isArray(n)?n.forEach(function(t){e.push(t)}):e.push(n)}function cn(n,e){if(n[e]===!0)throw"duplicate flag "+e;n[e],n[e]=!0}function It(n){if(n===void 0)throw Error("Internal Error - Should never get here!");return!0}function Xd(){throw Error("Internal Error - Should never get here!")}function _a(n){return n.type==="Character"}const Hr=[];for(let n=w("0");n<=w("9");n++)Hr.push(n);const zr=[w("_")].concat(Hr);for(let n=w("a");n<=w("z");n++)zr.push(n);for(let n=w("A");n<=w("Z");n++)zr.push(n);const La=[w(" "),w("\f"),w(`
`),w("\r"),w("	"),w("\v"),w("	"),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w("\u2028"),w("\u2029"),w(" "),w(" "),w("　"),w("\uFEFF")],Jd=/[0-9a-fA-F]/,Tr=/[0-9]/,Qd=/[1-9]/;class Xl{constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");const t=this.disjunction();this.consumeChar("/");const r={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":cn(r,"global");break;case"i":cn(r,"ignoreCase");break;case"m":cn(r,"multiLine");break;case"u":cn(r,"unicode");break;case"y":cn(r,"sticky");break}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:r,value:t,loc:this.loc(0)}}disjunction(){const e=[],t=this.idx;for(e.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(t)}}alternative(){const e=[],t=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(t)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){const e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let t;switch(this.popChar()){case"=":t="Lookahead";break;case"!":t="NegativeLookahead";break}It(t);const r=this.disjunction();return this.consumeChar(")"),{type:t,value:r,loc:this.loc(e)}}return Xd()}quantifier(e=!1){let t;const r=this.idx;switch(this.popChar()){case"*":t={atLeast:0,atMost:1/0};break;case"+":t={atLeast:1,atMost:1/0};break;case"?":t={atLeast:0,atMost:1};break;case"{":const i=this.integerIncludingZero();switch(this.popChar()){case"}":t={atLeast:i,atMost:i};break;case",":let s;this.isDigit()?(s=this.integerIncludingZero(),t={atLeast:i,atMost:s}):t={atLeast:i,atMost:1/0},this.consumeChar("}");break}if(e===!0&&t===void 0)return;It(t);break}if(!(e===!0&&t===void 0)&&It(t))return this.peekChar(0)==="?"?(this.consumeChar("?"),t.greedy=!1):t.greedy=!0,t.type="Quantifier",t.loc=this.loc(r),t}atom(){let e;const t=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group();break}if(e===void 0&&this.isPatternCharacter()&&(e=this.patternCharacter()),It(e))return e.loc=this.loc(t),this.isQuantifier()&&(e.quantifier=this.quantifier()),e}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[w(`
`),w("\r"),w("\u2028"),w("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,t=!1;switch(this.popChar()){case"d":e=Hr;break;case"D":e=Hr,t=!0;break;case"s":e=La;break;case"S":e=La,t=!0;break;case"w":e=zr;break;case"W":e=zr,t=!0;break}if(It(e))return{type:"Set",value:e,complement:t}}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=w("\f");break;case"n":e=w(`
`);break;case"r":e=w("\r");break;case"t":e=w("	");break;case"v":e=w("\v");break}if(It(e))return{type:"Character",value:e}}controlLetterEscapeAtom(){this.consumeChar("c");const e=this.popChar();if(/[a-zA-Z]/.test(e)===!1)throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:w("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){const e=this.popChar();return{type:"Character",value:w(e)}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:const e=this.popChar();return{type:"Character",value:w(e)}}}characterClass(){const e=[];let t=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),t=!0);this.isClassAtom();){const r=this.classAtom();if(r.type,_a(r)&&this.isRangeDash()){this.consumeChar("-");const i=this.classAtom();if(i.type,_a(i)){if(i.value<r.value)throw Error("Range out of order in character class");e.push({from:r.value,to:i.value})}else Fi(r.value,e),e.push(w("-")),Fi(i.value,e)}else Fi(r.value,e)}return this.consumeChar("]"),{type:"Set",complement:t,value:e}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:w("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),e=!1;break;default:this.groupIdx++;break}const t=this.disjunction();this.consumeChar(")");const r={type:"Group",capturing:e,value:t};return e&&(r.idx=this.groupIdx),r}positiveInteger(){let e=this.popChar();if(Qd.test(e)===!1)throw Error("Expecting a positive integer");for(;Tr.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(Tr.test(e)===!1)throw Error("Expecting an integer");for(;Tr.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){const e=this.popChar();switch(e){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:w(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return Tr.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){const e=this.saveState();try{return this.quantifier(!0)!==void 0}catch{return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let t="";for(let i=0;i<e;i++){const s=this.popChar();if(Jd.test(s)===!1)throw Error("Expecting a HexDecimal digits");t+=s}return{type:"Character",value:parseInt(t,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){const e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(e!==void 0&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}}class Ti{visitChildren(e){for(const t in e){const r=e[t];e.hasOwnProperty(t)&&(r.type!==void 0?this.visit(r):Array.isArray(r)&&r.forEach(i=>{this.visit(i)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e);break}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}}const Zd=/\r?\n/gm,ef=new Xl;class tf extends Ti{constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){const t=String.fromCharCode(e.value);if(!this.multiline&&t===`
`&&(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const r=Ri(t);this.endRegexpStack.push(r),this.isStarting&&(this.startRegexp+=r)}}visitSet(e){if(!this.multiline){const t=this.regex.substring(e.loc.begin,e.loc.end),r=new RegExp(t);this.multiline=!!`
`.match(r)}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const t=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(t),this.isStarting&&(this.startRegexp+=t)}}visitChildren(e){e.type==="Group"&&e.quantifier||super.visitChildren(e)}}const Gi=new tf;function nf(n){try{return typeof n=="string"&&(n=new RegExp(n)),n=n.toString(),Gi.reset(n),Gi.visit(ef.pattern(n)),Gi.multiline}catch{return!1}}const rf=`\f
\r	\v              \u2028\u2029  　\uFEFF`.split("");function ds(n){const e=typeof n=="string"?new RegExp(n):n;return rf.some(t=>e.test(t))}function Ri(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function sf(n){return Array.prototype.map.call(n,e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:Ri(e)).join("")}function af(n,e){const t=of(n),r=e.match(t);return!!r&&r[0].length>0}function of(n){typeof n=="string"&&(n=new RegExp(n));const e=n,t=n.source;let r=0;function i(){let s="",a;function o(c){s+=t.substr(r,c),r+=c}function l(c){s+="(?:"+t.substr(r,c)+"|$)",r+=c}for(;r<t.length;)switch(t[r]){case"\\":switch(t[r+1]){case"c":l(3);break;case"x":l(4);break;case"u":e.unicode?t[r+2]==="{"?l(t.indexOf("}",r)-r+1):l(6):l(2);break;case"p":case"P":e.unicode?l(t.indexOf("}",r)-r+1):l(2);break;case"k":l(t.indexOf(">",r)-r+1);break;default:l(2);break}break;case"[":a=/\[(?:\\.|.)*?\]/g,a.lastIndex=r,a=a.exec(t)||[],l(a[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":o(1);break;case"{":a=/\{\d+,?\d*\}/g,a.lastIndex=r,a=a.exec(t),a?o(a[0].length):l(1);break;case"(":if(t[r+1]==="?")switch(t[r+2]){case":":s+="(?:",r+=3,s+=i()+"|$)";break;case"=":s+="(?=",r+=3,s+=i()+")";break;case"!":a=r,r+=3,i(),s+=t.substr(a,r-a);break;case"<":switch(t[r+3]){case"=":case"!":a=r,r+=4,i(),s+=t.substr(a,r-a);break;default:o(t.indexOf(">",r)-r+1),s+=i()+"|$)";break}break}else o(1),s+=i()+"|$)";break;case")":return++r,s;default:l(1);break}return s}return new RegExp(i(),n.flags)}function lf(n){return n.rules.find(e=>we(e)&&e.entry)}function cf(n){return n.rules.filter(e=>Et(e)&&e.hidden)}function Jl(n,e){const t=new Set,r=lf(n);if(!r)return new Set(n.rules);const i=[r].concat(cf(n));for(const a of i)Ql(a,t,e);const s=new Set;for(const a of n.rules)(t.has(a.name)||Et(a)&&a.hidden)&&s.add(a);return s}function Ql(n,e,t){e.add(n.name),nr(n).forEach(r=>{if(yt(r)||t){const i=r.rule.ref;i&&!e.has(i.name)&&Ql(i,e,t)}})}function uf(n){if(n.terminal)return n.terminal;if(n.type.ref){const e=ec(n.type.ref);return e==null?void 0:e.terminal}}function df(n){return n.hidden&&!ds(ra(n))}function ff(n,e){return!n||!e?[]:ea(n,e,n.astNode,!0)}function Zl(n,e,t){if(!n||!e)return;const r=ea(n,e,n.astNode,!0);if(r.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,r.length-1)):t=0,r[t]}function ea(n,e,t,r){if(!r){const i=yi(n.grammarSource,mt);if(i&&i.feature===e)return[n]}return Xn(n)&&n.astNode===t?n.content.flatMap(i=>ea(i,e,t,!1)):[]}function hf(n,e,t){if(!n)return;const r=pf(n,e,n==null?void 0:n.astNode);if(r.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,r.length-1)):t=0,r[t]}function pf(n,e,t){if(n.astNode!==t)return[];if(gt(n.grammarSource)&&n.grammarSource.value===e)return[n];const r=ls(n).iterator();let i;const s=[];do if(i=r.next(),!i.done){const a=i.value;a.astNode===t?gt(a.grammarSource)&&a.grammarSource.value===e&&s.push(a):r.prune()}while(!i.done);return s}function mf(n){var e;const t=n.astNode;for(;t===((e=n.container)===null||e===void 0?void 0:e.astNode);){const r=yi(n.grammarSource,mt);if(r)return r;n=n.container}}function ec(n){let e=n;return Vl(e)&&(gi(e.$container)?e=e.$container.$container:we(e.$container)?e=e.$container:tr(e.$container)),tc(n,e,new Map)}function tc(n,e,t){var r;function i(s,a){let o;return yi(s,mt)||(o=tc(a,a,t)),t.set(n,o),o}if(t.has(n))return t.get(n);t.set(n,void 0);for(const s of nr(e)){if(mt(s)&&s.feature.toLowerCase()==="name")return t.set(n,s),s;if(yt(s)&&we(s.rule.ref))return i(s,s.rule.ref);if(Fd(s)&&(!((r=s.typeRef)===null||r===void 0)&&r.ref))return i(s,s.typeRef.ref)}}function nc(n){return rc(n,new Set)}function rc(n,e){if(e.has(n))return!0;e.add(n);for(const t of nr(n))if(yt(t)){if(!t.rule.ref||we(t.rule.ref)&&!rc(t.rule.ref,e))return!1}else{if(mt(t))return!1;if(gi(t))return!1}return!!n.definition}function ta(n){if(n.inferredType)return n.inferredType.name;if(n.dataType)return n.dataType;if(n.returnType){const e=n.returnType.ref;if(e){if(we(e))return e.name;if(Kl(e)||Wl(e))return e.name}}}function na(n){var e;if(we(n))return nc(n)?n.name:(e=ta(n))!==null&&e!==void 0?e:n.name;if(Kl(n)||Wl(n)||Dd(n))return n.name;if(gi(n)){const t=gf(n);if(t)return t}else if(Vl(n))return n.name;throw new Error("Cannot get name of Unknown Type")}function gf(n){var e;if(n.inferredType)return n.inferredType.name;if(!((e=n.type)===null||e===void 0)&&e.ref)return na(n.type.ref)}function yf(n){var e,t,r;return Et(n)?(t=(e=n.type)===null||e===void 0?void 0:e.name)!==null&&t!==void 0?t:"string":(r=ta(n))!==null&&r!==void 0?r:n.name}function ra(n){const e={s:!1,i:!1,u:!1},t=sn(n.definition,e),r=Object.entries(e).filter(([,i])=>i).map(([i])=>i).join("");return new RegExp(t,r)}const ia=/[\s\S]/.source;function sn(n,e){if(Kd(n))return Tf(n);if(Wd(n))return Rf(n);if(Gd(n))return Ef(n);if(jd(n)){const t=n.rule.ref;if(!t)throw new Error("Missing rule reference.");return qe(sn(t.definition),{cardinality:n.cardinality,lookahead:n.lookahead})}else{if(Bd(n))return Af(n);if(Hd(n))return vf(n);if(Vd(n)){const t=n.regex.lastIndexOf("/"),r=n.regex.substring(1,t),i=n.regex.substring(t+1);return e&&(e.i=i.includes("i"),e.s=i.includes("s"),e.u=i.includes("u")),qe(r,{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1})}else{if(zd(n))return qe(ia,{cardinality:n.cardinality,lookahead:n.lookahead});throw new Error(`Invalid terminal element: ${n==null?void 0:n.$type}`)}}}function Tf(n){return qe(n.elements.map(e=>sn(e)).join("|"),{cardinality:n.cardinality,lookahead:n.lookahead})}function Rf(n){return qe(n.elements.map(e=>sn(e)).join(""),{cardinality:n.cardinality,lookahead:n.lookahead})}function vf(n){return qe(`${ia}*?${sn(n.terminal)}`,{cardinality:n.cardinality,lookahead:n.lookahead})}function Af(n){return qe(`(?!${sn(n.terminal)})${ia}*?`,{cardinality:n.cardinality,lookahead:n.lookahead})}function Ef(n){return n.right?qe(`[${Ui(n.left)}-${Ui(n.right)}]`,{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1}):qe(Ui(n.left),{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1})}function Ui(n){return Ri(n.value)}function qe(n,e){var t;return(e.wrap!==!1||e.lookahead)&&(n=`(${(t=e.lookahead)!==null&&t!==void 0?t:""}${n})`),e.cardinality?`${n}${e.cardinality}`:n}function $f(n){const e=[],t=n.Grammar;for(const r of t.rules)Et(r)&&df(r)&&nf(ra(r))&&e.push(r.name);return{multilineCommentRules:e,nameRegexp:Cd}}function fs(n){console&&console.error&&console.error(`Error: ${n}`)}function ic(n){console&&console.warn&&console.warn(`Warning: ${n}`)}function sc(n){const e=new Date().getTime(),t=n();return{time:new Date().getTime()-e,value:t}}function ac(n){function e(){}e.prototype=n;const t=new e;function r(){return typeof t.bar}return r(),r(),n}function kf(n){return Sf(n)?n.LABEL:n.name}function Sf(n){return he(n.LABEL)&&n.LABEL!==""}class Be{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),C(this.definition,t=>{t.accept(e)})}}class ce extends Be{constructor(e){super([]),this.idx=1,$e(this,Me(e,t=>t!==void 0))}set definition(e){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(e){e.visit(this)}}class an extends Be{constructor(e){super(e.definition),this.orgText="",$e(this,Me(e,t=>t!==void 0))}}class pe extends Be{constructor(e){super(e.definition),this.ignoreAmbiguities=!1,$e(this,Me(e,t=>t!==void 0))}}let te=class extends Be{constructor(e){super(e.definition),this.idx=1,$e(this,Me(e,t=>t!==void 0))}};class Se extends Be{constructor(e){super(e.definition),this.idx=1,$e(this,Me(e,t=>t!==void 0))}}class xe extends Be{constructor(e){super(e.definition),this.idx=1,$e(this,Me(e,t=>t!==void 0))}}class W extends Be{constructor(e){super(e.definition),this.idx=1,$e(this,Me(e,t=>t!==void 0))}}class me extends Be{constructor(e){super(e.definition),this.idx=1,$e(this,Me(e,t=>t!==void 0))}}class ge extends Be{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,$e(this,Me(e,t=>t!==void 0))}}class G{constructor(e){this.idx=1,$e(this,Me(e,t=>t!==void 0))}accept(e){e.visit(this)}}function xf(n){return S(n,_r)}function _r(n){function e(t){return S(t,_r)}if(n instanceof ce){const t={type:"NonTerminal",name:n.nonTerminalName,idx:n.idx};return he(n.label)&&(t.label=n.label),t}else{if(n instanceof pe)return{type:"Alternative",definition:e(n.definition)};if(n instanceof te)return{type:"Option",idx:n.idx,definition:e(n.definition)};if(n instanceof Se)return{type:"RepetitionMandatory",idx:n.idx,definition:e(n.definition)};if(n instanceof xe)return{type:"RepetitionMandatoryWithSeparator",idx:n.idx,separator:_r(new G({terminalType:n.separator})),definition:e(n.definition)};if(n instanceof me)return{type:"RepetitionWithSeparator",idx:n.idx,separator:_r(new G({terminalType:n.separator})),definition:e(n.definition)};if(n instanceof W)return{type:"Repetition",idx:n.idx,definition:e(n.definition)};if(n instanceof ge)return{type:"Alternation",idx:n.idx,definition:e(n.definition)};if(n instanceof G){const t={type:"Terminal",name:n.terminalType.name,label:kf(n.terminalType),idx:n.idx};he(n.label)&&(t.terminalLabel=n.label);const r=n.terminalType.PATTERN;return n.terminalType.PATTERN&&(t.pattern=Xe(r)?r.source:r),t}else{if(n instanceof an)return{type:"Rule",name:n.name,orgText:n.orgText,definition:e(n.definition)};throw Error("non exhaustive match")}}}class on{visit(e){const t=e;switch(t.constructor){case ce:return this.visitNonTerminal(t);case pe:return this.visitAlternative(t);case te:return this.visitOption(t);case Se:return this.visitRepetitionMandatory(t);case xe:return this.visitRepetitionMandatoryWithSeparator(t);case me:return this.visitRepetitionWithSeparator(t);case W:return this.visitRepetition(t);case ge:return this.visitAlternation(t);case G:return this.visitTerminal(t);case an:return this.visitRule(t);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}}function If(n){return n instanceof pe||n instanceof te||n instanceof W||n instanceof Se||n instanceof xe||n instanceof me||n instanceof G||n instanceof an}function qr(n,e=[]){return n instanceof te||n instanceof W||n instanceof me?!0:n instanceof ge?Dl(n.definition,r=>qr(r,e)):n instanceof ce&&de(e,n)?!1:n instanceof Be?(n instanceof ce&&e.push(n),Oe(n.definition,r=>qr(r,e))):!1}function Cf(n){return n instanceof ge}function Ge(n){if(n instanceof ce)return"SUBRULE";if(n instanceof te)return"OPTION";if(n instanceof ge)return"OR";if(n instanceof Se)return"AT_LEAST_ONE";if(n instanceof xe)return"AT_LEAST_ONE_SEP";if(n instanceof me)return"MANY_SEP";if(n instanceof W)return"MANY";if(n instanceof G)return"CONSUME";throw Error("non exhaustive match")}class vi{walk(e,t=[]){C(e.definition,(r,i)=>{const s=J(e.definition,i+1);if(r instanceof ce)this.walkProdRef(r,s,t);else if(r instanceof G)this.walkTerminal(r,s,t);else if(r instanceof pe)this.walkFlat(r,s,t);else if(r instanceof te)this.walkOption(r,s,t);else if(r instanceof Se)this.walkAtLeastOne(r,s,t);else if(r instanceof xe)this.walkAtLeastOneSep(r,s,t);else if(r instanceof me)this.walkManySep(r,s,t);else if(r instanceof W)this.walkMany(r,s,t);else if(r instanceof ge)this.walkOr(r,s,t);else throw Error("non exhaustive match")})}walkTerminal(e,t,r){}walkProdRef(e,t,r){}walkFlat(e,t,r){const i=t.concat(r);this.walk(e,i)}walkOption(e,t,r){const i=t.concat(r);this.walk(e,i)}walkAtLeastOne(e,t,r){const i=[new te({definition:e.definition})].concat(t,r);this.walk(e,i)}walkAtLeastOneSep(e,t,r){const i=ba(e,t,r);this.walk(e,i)}walkMany(e,t,r){const i=[new te({definition:e.definition})].concat(t,r);this.walk(e,i)}walkManySep(e,t,r){const i=ba(e,t,r);this.walk(e,i)}walkOr(e,t,r){const i=t.concat(r);C(e.definition,s=>{const a=new pe({definition:[s]});this.walk(a,i)})}}function ba(n,e,t){return[new te({definition:[new G({terminalType:n.separator})].concat(n.definition)})].concat(e,t)}function rr(n){if(n instanceof ce)return rr(n.referencedRule);if(n instanceof G)return _f(n);if(If(n))return Nf(n);if(Cf(n))return wf(n);throw Error("non exhaustive match")}function Nf(n){let e=[];const t=n.definition;let r=0,i=t.length>r,s,a=!0;for(;i&&a;)s=t[r],a=qr(s),e=e.concat(rr(s)),r=r+1,i=t.length>r;return Ys(e)}function wf(n){const e=S(n.definition,t=>rr(t));return Ys(Ne(e))}function _f(n){return[n.terminalType]}const oc="_~IN~_";class Lf extends vi{constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,t,r){}walkProdRef(e,t,r){const i=Of(e.referencedRule,e.idx)+this.topProd.name,s=t.concat(r),a=new pe({definition:s}),o=rr(a);this.follows[i]=o}}function bf(n){const e={};return C(n,t=>{const r=new Lf(t).startWalking();$e(e,r)}),e}function Of(n,e){return n.name+e+oc}let Lr={};const Pf=new Xl;function Ai(n){const e=n.toString();if(Lr.hasOwnProperty(e))return Lr[e];{const t=Pf.pattern(e);return Lr[e]=t,t}}function Mf(){Lr={}}const lc="Complement Sets are not supported for first char optimization",Yr=`Unable to use "first char" lexer optimizations:
`;function Df(n,e=!1){try{const t=Ai(n);return hs(t.value,{},t.flags.ignoreCase)}catch(t){if(t.message===lc)e&&ic(`${Yr}	Unable to optimize: < ${n.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let r="";e&&(r=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),fs(`${Yr}
	Failed parsing: < ${n.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+r)}}return[]}function hs(n,e,t){switch(n.type){case"Disjunction":for(let i=0;i<n.value.length;i++)hs(n.value[i],e,t);break;case"Alternative":const r=n.value;for(let i=0;i<r.length;i++){const s=r[i];switch(s.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}const a=s;switch(a.type){case"Character":Rr(a.value,e,t);break;case"Set":if(a.complement===!0)throw Error(lc);C(a.value,l=>{if(typeof l=="number")Rr(l,e,t);else{const c=l;if(t===!0)for(let u=c.from;u<=c.to;u++)Rr(u,e,t);else{for(let u=c.from;u<=c.to&&u<Bn;u++)Rr(u,e,t);if(c.to>=Bn){const u=c.from>=Bn?c.from:Bn,d=c.to,h=tt(u),f=tt(d);for(let m=h;m<=f;m++)e[m]=m}}}});break;case"Group":hs(a.value,e,t);break;default:throw Error("Non Exhaustive Match")}const o=a.quantifier!==void 0&&a.quantifier.atLeast===0;if(a.type==="Group"&&ps(a)===!1||a.type!=="Group"&&o===!1)break}break;default:throw Error("non exhaustive match!")}return z(e)}function Rr(n,e,t){const r=tt(n);e[r]=r,t===!0&&Ff(n,e)}function Ff(n,e){const t=String.fromCharCode(n),r=t.toUpperCase();if(r!==t){const i=tt(r.charCodeAt(0));e[i]=i}else{const i=t.toLowerCase();if(i!==t){const s=tt(i.charCodeAt(0));e[s]=s}}}function Oa(n,e){return Xt(n.value,t=>{if(typeof t=="number")return de(e,t);{const r=t;return Xt(e,i=>r.from<=i&&i<=r.to)!==void 0}})}function ps(n){const e=n.quantifier;return e&&e.atLeast===0?!0:n.value?ee(n.value)?Oe(n.value,ps):ps(n.value):!1}class Gf extends Ti{constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(this.found!==!0){switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}}visitCharacter(e){de(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?Oa(e,this.targetCharCodes)===void 0&&(this.found=!0):Oa(e,this.targetCharCodes)!==void 0&&(this.found=!0)}}function sa(n,e){if(e instanceof RegExp){const t=Ai(e),r=new Gf(n);return r.visit(t),r.found}else return Xt(e,t=>de(n,t.charCodeAt(0)))!==void 0}const Tt="PATTERN",Un="defaultMode",vr="modes";let cc=typeof new RegExp("(?:)").sticky=="boolean";function Uf(n,e){e=qs(e,{useSticky:cc,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:(E,R)=>R()});const t=e.tracer;t("initCharCodeToOptimizedIndexMap",()=>{ch()});let r;t("Reject Lexer.NA",()=>{r=mi(n,E=>E[Tt]===fe.NA)});let i=!1,s;t("Transform Patterns",()=>{i=!1,s=S(r,E=>{const R=E[Tt];if(Xe(R)){const I=R.source;return I.length===1&&I!=="^"&&I!=="$"&&I!=="."&&!R.ignoreCase?I:I.length===2&&I[0]==="\\"&&!de(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],I[1])?I[1]:e.useSticky?Ma(R):Pa(R)}else{if(At(R))return i=!0,{exec:R};if(typeof R=="object")return i=!0,R;if(typeof R=="string"){if(R.length===1)return R;{const I=R.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),F=new RegExp(I);return e.useSticky?Ma(F):Pa(F)}}else throw Error("non exhaustive match")}})});let a,o,l,c,u;t("misc mapping",()=>{a=S(r,E=>E.tokenTypeIdx),o=S(r,E=>{const R=E.GROUP;if(R!==fe.SKIPPED){if(he(R))return R;if(Ye(R))return!1;throw Error("non exhaustive match")}}),l=S(r,E=>{const R=E.LONGER_ALT;if(R)return ee(R)?S(R,F=>xa(r,F)):[xa(r,R)]}),c=S(r,E=>E.PUSH_MODE),u=S(r,E=>N(E,"POP_MODE"))});let d;t("Line Terminator Handling",()=>{const E=fc(e.lineTerminatorCharacters);d=S(r,R=>!1),e.positionTracking!=="onlyOffset"&&(d=S(r,R=>N(R,"LINE_BREAKS")?!!R.LINE_BREAKS:dc(R,E)===!1&&sa(E,R.PATTERN)))});let h,f,m,g;t("Misc Mapping #2",()=>{h=S(r,uc),f=S(s,ah),m=le(r,(E,R)=>{const I=R.GROUP;return he(I)&&I!==fe.SKIPPED&&(E[I]=[]),E},{}),g=S(s,(E,R)=>({pattern:s[R],longerAlt:l[R],canLineTerminator:d[R],isCustom:h[R],short:f[R],group:o[R],push:c[R],pop:u[R],tokenTypeIdx:a[R],tokenType:r[R]}))});let A=!0,y=[];return e.safeMode||t("First Char Optimization",()=>{y=le(r,(E,R,I)=>{if(typeof R.PATTERN=="string"){const F=R.PATTERN.charCodeAt(0),re=tt(F);Bi(E,re,g[I])}else if(ee(R.START_CHARS_HINT)){let F;C(R.START_CHARS_HINT,re=>{const _e=typeof re=="string"?re.charCodeAt(0):re,ye=tt(_e);F!==ye&&(F=ye,Bi(E,ye,g[I]))})}else if(Xe(R.PATTERN))if(R.PATTERN.unicode)A=!1,e.ensureOptimizations&&fs(`${Yr}	Unable to analyze < ${R.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{const F=Df(R.PATTERN,e.ensureOptimizations);D(F)&&(A=!1),C(F,re=>{Bi(E,re,g[I])})}else e.ensureOptimizations&&fs(`${Yr}	TokenType: <${R.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),A=!1;return E},[])}),{emptyGroups:m,patternIdxToConfig:g,charCodeToPatternIdxToConfig:y,hasCustom:i,canBeOptimized:A}}function Bf(n,e){let t=[];const r=Kf(n);t=t.concat(r.errors);const i=Wf(r.valid),s=i.valid;return t=t.concat(i.errors),t=t.concat(Vf(s)),t=t.concat(Qf(s)),t=t.concat(Zf(s,e)),t=t.concat(eh(s)),t}function Vf(n){let e=[];const t=ke(n,r=>Xe(r[Tt]));return e=e.concat(Hf(t)),e=e.concat(Yf(t)),e=e.concat(Xf(t)),e=e.concat(Jf(t)),e=e.concat(zf(t)),e}function Kf(n){const e=ke(n,i=>!N(i,Tt)),t=S(e,i=>({message:"Token Type: ->"+i.name+"<- missing static 'PATTERN' property",type:j.MISSING_PATTERN,tokenTypes:[i]})),r=pi(n,e);return{errors:t,valid:r}}function Wf(n){const e=ke(n,i=>{const s=i[Tt];return!Xe(s)&&!At(s)&&!N(s,"exec")&&!he(s)}),t=S(e,i=>({message:"Token Type: ->"+i.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:j.INVALID_PATTERN,tokenTypes:[i]})),r=pi(n,e);return{errors:t,valid:r}}const jf=/[^\\][$]/;function Hf(n){class e extends Ti{constructor(){super(...arguments),this.found=!1}visitEndAnchor(s){this.found=!0}}const t=ke(n,i=>{const s=i.PATTERN;try{const a=Ai(s),o=new e;return o.visit(a),o.found}catch{return jf.test(s.source)}});return S(t,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:j.EOI_ANCHOR_FOUND,tokenTypes:[i]}))}function zf(n){const e=ke(n,r=>r.PATTERN.test(""));return S(e,r=>({message:"Token Type: ->"+r.name+"<- static 'PATTERN' must not match an empty string",type:j.EMPTY_MATCH_PATTERN,tokenTypes:[r]}))}const qf=/[^\\[][\^]|^\^/;function Yf(n){class e extends Ti{constructor(){super(...arguments),this.found=!1}visitStartAnchor(s){this.found=!0}}const t=ke(n,i=>{const s=i.PATTERN;try{const a=Ai(s),o=new e;return o.visit(a),o.found}catch{return qf.test(s.source)}});return S(t,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:j.SOI_ANCHOR_FOUND,tokenTypes:[i]}))}function Xf(n){const e=ke(n,r=>{const i=r[Tt];return i instanceof RegExp&&(i.multiline||i.global)});return S(e,r=>({message:"Token Type: ->"+r.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:j.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[r]}))}function Jf(n){const e=[];let t=S(n,s=>le(n,(a,o)=>(s.PATTERN.source===o.PATTERN.source&&!de(e,o)&&o.PATTERN!==fe.NA&&(e.push(o),a.push(o)),a),[]));t=er(t);const r=ke(t,s=>s.length>1);return S(r,s=>{const a=S(s,l=>l.name);return{message:`The same RegExp pattern ->${Pe(s).PATTERN}<-has been used in all of the following Token Types: ${a.join(", ")} <-`,type:j.DUPLICATE_PATTERNS_FOUND,tokenTypes:s}})}function Qf(n){const e=ke(n,r=>{if(!N(r,"GROUP"))return!1;const i=r.GROUP;return i!==fe.SKIPPED&&i!==fe.NA&&!he(i)});return S(e,r=>({message:"Token Type: ->"+r.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:j.INVALID_GROUP_TYPE_FOUND,tokenTypes:[r]}))}function Zf(n,e){const t=ke(n,i=>i.PUSH_MODE!==void 0&&!de(e,i.PUSH_MODE));return S(t,i=>({message:`Token Type: ->${i.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${i.PUSH_MODE}<-which does not exist`,type:j.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[i]}))}function eh(n){const e=[],t=le(n,(r,i,s)=>{const a=i.PATTERN;return a===fe.NA||(he(a)?r.push({str:a,idx:s,tokenType:i}):Xe(a)&&nh(a)&&r.push({str:a.source,idx:s,tokenType:i})),r},[]);return C(n,(r,i)=>{C(t,({str:s,idx:a,tokenType:o})=>{if(i<a&&th(s,r.PATTERN)){const l=`Token: ->${o.name}<- can never be matched.
Because it appears AFTER the Token Type ->${r.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;e.push({message:l,type:j.UNREACHABLE_PATTERN,tokenTypes:[r,o]})}})}),e}function th(n,e){if(Xe(e)){const t=e.exec(n);return t!==null&&t.index===0}else{if(At(e))return e(n,0,[],{});if(N(e,"exec"))return e.exec(n,0,[],{});if(typeof e=="string")return e===n;throw Error("non exhaustive match")}}function nh(n){return Xt([".","\\","[","]","|","^","$","(",")","?","*","+","{"],t=>n.source.indexOf(t)!==-1)===void 0}function Pa(n){const e=n.ignoreCase?"i":"";return new RegExp(`^(?:${n.source})`,e)}function Ma(n){const e=n.ignoreCase?"iy":"y";return new RegExp(`${n.source}`,e)}function rh(n,e,t){const r=[];return N(n,Un)||r.push({message:"A MultiMode Lexer cannot be initialized without a <"+Un+`> property in its definition
`,type:j.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),N(n,vr)||r.push({message:"A MultiMode Lexer cannot be initialized without a <"+vr+`> property in its definition
`,type:j.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),N(n,vr)&&N(n,Un)&&!N(n.modes,n.defaultMode)&&r.push({message:`A MultiMode Lexer cannot be initialized with a ${Un}: <${n.defaultMode}>which does not exist
`,type:j.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),N(n,vr)&&C(n.modes,(i,s)=>{C(i,(a,o)=>{if(Ye(a))r.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${s}> at index: <${o}>
`,type:j.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if(N(a,"LONGER_ALT")){const l=ee(a.LONGER_ALT)?a.LONGER_ALT:[a.LONGER_ALT];C(l,c=>{!Ye(c)&&!de(i,c)&&r.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${c.name}> on token <${a.name}> outside of mode <${s}>
`,type:j.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),r}function ih(n,e,t){const r=[];let i=!1;const s=er(Ne(z(n.modes))),a=mi(s,l=>l[Tt]===fe.NA),o=fc(t);return e&&C(a,l=>{const c=dc(l,o);if(c!==!1){const d={message:lh(l,c),type:c.issue,tokenType:l};r.push(d)}else N(l,"LINE_BREAKS")?l.LINE_BREAKS===!0&&(i=!0):sa(o,l.PATTERN)&&(i=!0)}),e&&!i&&r.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:j.NO_LINE_BREAKS_FLAGS}),r}function sh(n){const e={},t=Yt(n);return C(t,r=>{const i=n[r];if(ee(i))e[r]=[];else throw Error("non exhaustive match")}),e}function uc(n){const e=n.PATTERN;if(Xe(e))return!1;if(At(e))return!0;if(N(e,"exec"))return!0;if(he(e))return!1;throw Error("non exhaustive match")}function ah(n){return he(n)&&n.length===1?n.charCodeAt(0):!1}const oh={test:function(n){const e=n.length;for(let t=this.lastIndex;t<e;t++){const r=n.charCodeAt(t);if(r===10)return this.lastIndex=t+1,!0;if(r===13)return n.charCodeAt(t+1)===10?this.lastIndex=t+2:this.lastIndex=t+1,!0}return!1},lastIndex:0};function dc(n,e){if(N(n,"LINE_BREAKS"))return!1;if(Xe(n.PATTERN)){try{sa(e,n.PATTERN)}catch(t){return{issue:j.IDENTIFY_TERMINATOR,errMsg:t.message}}return!1}else{if(he(n.PATTERN))return!1;if(uc(n))return{issue:j.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}}function lh(n,e){if(e.issue===j.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${n.name}> Token Type
	 Root cause: ${e.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(e.issue===j.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${n.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}function fc(n){return S(n,t=>he(t)?t.charCodeAt(0):t)}function Bi(n,e,t){n[e]===void 0?n[e]=[t]:n[e].push(t)}const Bn=256;let br=[];function tt(n){return n<Bn?n:br[n]}function ch(){if(D(br)){br=new Array(65536);for(let n=0;n<65536;n++)br[n]=n>255?255+~~(n/255):n}}function ir(n,e){const t=n.tokenTypeIdx;return t===e.tokenTypeIdx?!0:e.isParent===!0&&e.categoryMatchesMap[t]===!0}function Xr(n,e){return n.tokenTypeIdx===e.tokenTypeIdx}let Da=1;const hc={};function sr(n){const e=uh(n);dh(e),hh(e),fh(e),C(e,t=>{t.isParent=t.categoryMatches.length>0})}function uh(n){let e=ne(n),t=n,r=!0;for(;r;){t=er(Ne(S(t,s=>s.CATEGORIES)));const i=pi(t,e);e=e.concat(i),D(i)?r=!1:t=i}return e}function dh(n){C(n,e=>{mc(e)||(hc[Da]=e,e.tokenTypeIdx=Da++),Fa(e)&&!ee(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),Fa(e)||(e.CATEGORIES=[]),ph(e)||(e.categoryMatches=[]),mh(e)||(e.categoryMatchesMap={})})}function fh(n){C(n,e=>{e.categoryMatches=[],C(e.categoryMatchesMap,(t,r)=>{e.categoryMatches.push(hc[r].tokenTypeIdx)})})}function hh(n){C(n,e=>{pc([],e)})}function pc(n,e){C(n,t=>{e.categoryMatchesMap[t.tokenTypeIdx]=!0}),C(e.CATEGORIES,t=>{const r=n.concat(e);de(r,t)||pc(r,t)})}function mc(n){return N(n,"tokenTypeIdx")}function Fa(n){return N(n,"CATEGORIES")}function ph(n){return N(n,"categoryMatches")}function mh(n){return N(n,"categoryMatchesMap")}function gh(n){return N(n,"tokenTypeIdx")}const ms={buildUnableToPopLexerModeMessage(n){return`Unable to pop Lexer Mode after encountering Token ->${n.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(n,e,t,r,i){return`unexpected character: ->${n.charAt(e)}<- at offset: ${e}, skipped ${t} characters.`}};var j;(function(n){n[n.MISSING_PATTERN=0]="MISSING_PATTERN",n[n.INVALID_PATTERN=1]="INVALID_PATTERN",n[n.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",n[n.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",n[n.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",n[n.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",n[n.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",n[n.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",n[n.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",n[n.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",n[n.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",n[n.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",n[n.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",n[n.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",n[n.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",n[n.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",n[n.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",n[n.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(j||(j={}));const Vn={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:ms,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(Vn);class fe{constructor(e,t=Vn){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(i,s)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;const a=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${a}--> <${i}>`);const{time:o,value:l}=sc(s),c=o>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&c(`${a}<-- <${i}> time: ${o}ms`),this.traceInitIndent--,l}else return s()},typeof t=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=$e({},Vn,t);const r=this.config.traceInitPerf;r===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof r=="number"&&(this.traceInitMaxIdent=r,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let i,s=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===Vn.lineTerminatorsPattern)this.config.lineTerminatorsPattern=oh;else if(this.config.lineTerminatorCharacters===Vn.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(t.safeMode&&t.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),ee(e)?i={modes:{defaultMode:ne(e)},defaultMode:Un}:(s=!1,i=ne(e))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(rh(i,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(ih(i,this.trackStartLines,this.config.lineTerminatorCharacters))})),i.modes=i.modes?i.modes:{},C(i.modes,(o,l)=>{i.modes[l]=mi(o,c=>Ye(c))});const a=Yt(i.modes);if(C(i.modes,(o,l)=>{this.TRACE_INIT(`Mode: <${l}> processing`,()=>{if(this.modes.push(l),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(Bf(o,a))}),D(this.lexerDefinitionErrors)){sr(o);let c;this.TRACE_INIT("analyzeTokenTypes",()=>{c=Uf(o,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:t.positionTracking,ensureOptimizations:t.ensureOptimizations,safeMode:t.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[l]=c.patternIdxToConfig,this.charCodeToPatternIdxToConfig[l]=c.charCodeToPatternIdxToConfig,this.emptyGroups=$e({},this.emptyGroups,c.emptyGroups),this.hasCustom=c.hasCustom||this.hasCustom,this.canModeBeOptimized[l]=c.canBeOptimized}})}),this.defaultMode=i.defaultMode,!D(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){const l=S(this.lexerDefinitionErrors,c=>c.message).join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+l)}C(this.lexerDefinitionWarning,o=>{ic(o.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(cc?(this.chopInput=Sa,this.match=this.matchWithTest):(this.updateLastIndex=Y,this.match=this.matchWithExec),s&&(this.handleModes=Y),this.trackStartLines===!1&&(this.computeNewColumn=Sa),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=Y),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{const o=le(this.canModeBeOptimized,(l,c,u)=>(c===!1&&l.push(u),l),[]);if(t.ensureOptimizations&&!D(o))throw Error(`Lexer Modes: < ${o.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{Mf()}),this.TRACE_INIT("toFastProperties",()=>{ac(this)})})}tokenize(e,t=this.defaultMode){if(!D(this.lexerDefinitionErrors)){const i=S(this.lexerDefinitionErrors,s=>s.message).join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+i)}return this.tokenizeInternal(e,t)}tokenizeInternal(e,t){let r,i,s,a,o,l,c,u,d,h,f,m,g,A,y;const E=e,R=E.length;let I=0,F=0;const re=this.hasCustom?0:Math.floor(e.length/10),_e=new Array(re),ye=[];let Fe=this.trackStartLines?1:void 0,Ie=this.trackStartLines?1:void 0;const k=sh(this.emptyGroups),T=this.trackStartLines,$=this.config.lineTerminatorsPattern;let x=0,b=[],L=[];const _=[],Te=[];Object.freeze(Te);let q;function K(){return b}function dt(ie){const Ce=tt(ie),xt=L[Ce];return xt===void 0?Te:xt}const Pu=ie=>{if(_.length===1&&ie.tokenType.PUSH_MODE===void 0){const Ce=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(ie);ye.push({offset:ie.startOffset,line:ie.startLine,column:ie.startColumn,length:ie.image.length,message:Ce})}else{_.pop();const Ce=Jt(_);b=this.patternIdxToConfig[Ce],L=this.charCodeToPatternIdxToConfig[Ce],x=b.length;const xt=this.canModeBeOptimized[Ce]&&this.config.safeMode===!1;L&&xt?q=dt:q=K}};function va(ie){_.push(ie),L=this.charCodeToPatternIdxToConfig[ie],b=this.patternIdxToConfig[ie],x=b.length,x=b.length;const Ce=this.canModeBeOptimized[ie]&&this.config.safeMode===!1;L&&Ce?q=dt:q=K}va.call(this,t);let Le;const Aa=this.config.recoveryEnabled;for(;I<R;){l=null;const ie=E.charCodeAt(I),Ce=q(ie),xt=Ce.length;for(r=0;r<xt;r++){Le=Ce[r];const Re=Le.pattern;c=null;const Ve=Le.short;if(Ve!==!1?ie===Ve&&(l=Re):Le.isCustom===!0?(y=Re.exec(E,I,_e,k),y!==null?(l=y[0],y.payload!==void 0&&(c=y.payload)):l=null):(this.updateLastIndex(Re,I),l=this.match(Re,e,I)),l!==null){if(o=Le.longerAlt,o!==void 0){const Qe=o.length;for(s=0;s<Qe;s++){const Ke=b[o[s]],ft=Ke.pattern;if(u=null,Ke.isCustom===!0?(y=ft.exec(E,I,_e,k),y!==null?(a=y[0],y.payload!==void 0&&(u=y.payload)):a=null):(this.updateLastIndex(ft,I),a=this.match(ft,e,I)),a&&a.length>l.length){l=a,c=u,Le=Ke;break}}}break}}if(l!==null){if(d=l.length,h=Le.group,h!==void 0&&(f=Le.tokenTypeIdx,m=this.createTokenInstance(l,I,f,Le.tokenType,Fe,Ie,d),this.handlePayload(m,c),h===!1?F=this.addToken(_e,F,m):k[h].push(m)),e=this.chopInput(e,d),I=I+d,Ie=this.computeNewColumn(Ie,d),T===!0&&Le.canLineTerminator===!0){let Re=0,Ve,Qe;$.lastIndex=0;do Ve=$.test(l),Ve===!0&&(Qe=$.lastIndex-1,Re++);while(Ve===!0);Re!==0&&(Fe=Fe+Re,Ie=d-Qe,this.updateTokenEndLineColumnLocation(m,h,Qe,Re,Fe,Ie,d))}this.handleModes(Le,Pu,va,m)}else{const Re=I,Ve=Fe,Qe=Ie;let Ke=Aa===!1;for(;Ke===!1&&I<R;)for(e=this.chopInput(e,1),I++,i=0;i<x;i++){const ft=b[i],Li=ft.pattern,Ea=ft.short;if(Ea!==!1?E.charCodeAt(I)===Ea&&(Ke=!0):ft.isCustom===!0?Ke=Li.exec(E,I,_e,k)!==null:(this.updateLastIndex(Li,I),Ke=Li.exec(e)!==null),Ke===!0)break}if(g=I-Re,Ie=this.computeNewColumn(Ie,g),A=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(E,Re,g,Ve,Qe),ye.push({offset:Re,line:Ve,column:Qe,length:g,message:A}),Aa===!1)break}}return this.hasCustom||(_e.length=F),{tokens:_e,groups:k,errors:ye}}handleModes(e,t,r,i){if(e.pop===!0){const s=e.push;t(i),s!==void 0&&r.call(this,s)}else e.push!==void 0&&r.call(this,e.push)}chopInput(e,t){return e.substring(t)}updateLastIndex(e,t){e.lastIndex=t}updateTokenEndLineColumnLocation(e,t,r,i,s,a,o){let l,c;t!==void 0&&(l=r===o-1,c=l?-1:0,i===1&&l===!0||(e.endLine=s+c,e.endColumn=a-1+-c))}computeNewColumn(e,t){return e+t}createOffsetOnlyToken(e,t,r,i){return{image:e,startOffset:t,tokenTypeIdx:r,tokenType:i}}createStartOnlyToken(e,t,r,i,s,a){return{image:e,startOffset:t,startLine:s,startColumn:a,tokenTypeIdx:r,tokenType:i}}createFullToken(e,t,r,i,s,a,o){return{image:e,startOffset:t,endOffset:t+o-1,startLine:s,endLine:s,startColumn:a,endColumn:a+o-1,tokenTypeIdx:r,tokenType:i}}addTokenUsingPush(e,t,r){return e.push(r),t}addTokenUsingMemberAccess(e,t,r){return e[t]=r,t++,t}handlePayloadNoCustom(e,t){}handlePayloadWithCustom(e,t){t!==null&&(e.payload=t)}matchWithTest(e,t,r){return e.test(t)===!0?t.substring(r,e.lastIndex):null}matchWithExec(e,t){const r=e.exec(t);return r!==null?r[0]:null}}fe.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.";fe.NA=/NOT_APPLICABLE/;function _t(n){return gc(n)?n.LABEL:n.name}function gc(n){return he(n.LABEL)&&n.LABEL!==""}const yh="parent",Ga="categories",Ua="label",Ba="group",Va="push_mode",Ka="pop_mode",Wa="longer_alt",ja="line_breaks",Ha="start_chars_hint";function yc(n){return Th(n)}function Th(n){const e=n.pattern,t={};if(t.name=n.name,Ye(e)||(t.PATTERN=e),N(n,yh))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return N(n,Ga)&&(t.CATEGORIES=n[Ga]),sr([t]),N(n,Ua)&&(t.LABEL=n[Ua]),N(n,Ba)&&(t.GROUP=n[Ba]),N(n,Ka)&&(t.POP_MODE=n[Ka]),N(n,Va)&&(t.PUSH_MODE=n[Va]),N(n,Wa)&&(t.LONGER_ALT=n[Wa]),N(n,ja)&&(t.LINE_BREAKS=n[ja]),N(n,Ha)&&(t.START_CHARS_HINT=n[Ha]),t}const nt=yc({name:"EOF",pattern:fe.NA});sr([nt]);function aa(n,e,t,r,i,s,a,o){return{image:e,startOffset:t,endOffset:r,startLine:i,endLine:s,startColumn:a,endColumn:o,tokenTypeIdx:n.tokenTypeIdx,tokenType:n}}function Tc(n,e){return ir(n,e)}const Nt={buildMismatchTokenMessage({expected:n,actual:e,previous:t,ruleName:r}){return`Expecting ${gc(n)?`--> ${_t(n)} <--`:`token of type --> ${n.name} <--`} but found --> '${e.image}' <--`},buildNotAllInputParsedMessage({firstRedundant:n,ruleName:e}){return"Redundant input, expecting EOF but found: "+n.image},buildNoViableAltMessage({expectedPathsPerAlt:n,actual:e,previous:t,customUserDescription:r,ruleName:i}){const s="Expecting: ",o=`
but found: '`+Pe(e).image+"'";if(r)return s+r+o;{const l=le(n,(h,f)=>h.concat(f),[]),c=S(l,h=>`[${S(h,f=>_t(f)).join(", ")}]`),d=`one of these possible Token sequences:
${S(c,(h,f)=>`  ${f+1}. ${h}`).join(`
`)}`;return s+d+o}},buildEarlyExitMessage({expectedIterationPaths:n,actual:e,customUserDescription:t,ruleName:r}){const i="Expecting: ",a=`
but found: '`+Pe(e).image+"'";if(t)return i+t+a;{const l=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${S(n,c=>`[${S(c,u=>_t(u)).join(",")}]`).join(" ,")}>`;return i+l+a}}};Object.freeze(Nt);const Rh={buildRuleNotFoundError(n,e){return"Invalid grammar, reference to a rule which is not defined: ->"+e.nonTerminalName+`<-
inside top level rule: ->`+n.name+"<-"}},pt={buildDuplicateFoundError(n,e){function t(u){return u instanceof G?u.terminalType.name:u instanceof ce?u.nonTerminalName:""}const r=n.name,i=Pe(e),s=i.idx,a=Ge(i),o=t(i),l=s>0;let c=`->${a}${l?s:""}<- ${o?`with argument: ->${o}<-`:""}
                  appears more than once (${e.length} times) in the top level rule: ->${r}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return c=c.replace(/[ \t]+/g," "),c=c.replace(/\s\s+/g,`
`),c},buildNamespaceConflictError(n){return`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${n.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`},buildAlternationPrefixAmbiguityError(n){const e=S(n.prefixPath,i=>_t(i)).join(", "),t=n.alternation.idx===0?"":n.alternation.idx;return`Ambiguous alternatives: <${n.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(n){const e=S(n.prefixPath,i=>_t(i)).join(", "),t=n.alternation.idx===0?"":n.alternation.idx;let r=`Ambiguous Alternatives Detected: <${n.ambiguityIndices.join(" ,")}> in <OR${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return r=r+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,r},buildEmptyRepetitionError(n){let e=Ge(n.repetition);return n.repetition.idx!==0&&(e+=n.repetition.idx),`The repetition <${e}> within Rule <${n.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError(n){return"deprecated"},buildEmptyAlternationError(n){return`Ambiguous empty alternative: <${n.emptyChoiceIdx+1}> in <OR${n.alternation.idx}> inside <${n.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`},buildTooManyAlternativesError(n){return`An Alternation cannot have more than 256 alternatives:
<OR${n.alternation.idx}> inside <${n.topLevelRule.name}> Rule.
 has ${n.alternation.definition.length+1} alternatives.`},buildLeftRecursionError(n){const e=n.topLevelRule.name,t=S(n.leftRecursionPath,s=>s.name),r=`${e} --> ${t.concat([e]).join(" --> ")}`;return`Left Recursion found in grammar.
rule: <${e}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${r}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError(n){return"deprecated"},buildDuplicateRuleNameError(n){let e;return n.topLevelRule instanceof an?e=n.topLevelRule.name:e=n.topLevelRule,`Duplicate definition, rule: ->${e}<- is already defined in the grammar: ->${n.grammarName}<-`}};function vh(n,e){const t=new Ah(n,e);return t.resolveRefs(),t.errors}class Ah extends on{constructor(e,t){super(),this.nameToTopRule=e,this.errMsgProvider=t,this.errors=[]}resolveRefs(){C(z(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){const t=this.nameToTopRule[e.nonTerminalName];if(t)e.referencedRule=t;else{const r=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:r,type:ue.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}}class Eh extends vi{constructor(e,t){super(),this.topProd=e,this.path=t,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=ne(this.path.ruleStack).reverse(),this.occurrenceStack=ne(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,t=[]){this.found||super.walk(e,t)}walkProdRef(e,t,r){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){const i=t.concat(r);this.updateExpectedNext(),this.walk(e.referencedRule,i)}}updateExpectedNext(){D(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}}class $h extends Eh{constructor(e,t){super(e,t),this.path=t,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,t,r){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){const i=t.concat(r),s=new pe({definition:i});this.possibleTokTypes=rr(s),this.found=!0}}}class Ei extends vi{constructor(e,t){super(),this.topRule=e,this.occurrence=t,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}}class kh extends Ei{walkMany(e,t,r){if(e.idx===this.occurrence){const i=Pe(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkMany(e,t,r)}}class za extends Ei{walkManySep(e,t,r){if(e.idx===this.occurrence){const i=Pe(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkManySep(e,t,r)}}class Sh extends Ei{walkAtLeastOne(e,t,r){if(e.idx===this.occurrence){const i=Pe(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOne(e,t,r)}}class qa extends Ei{walkAtLeastOneSep(e,t,r){if(e.idx===this.occurrence){const i=Pe(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOneSep(e,t,r)}}function gs(n,e,t=[]){t=ne(t);let r=[],i=0;function s(o){return o.concat(J(n,i+1))}function a(o){const l=gs(s(o),e,t);return r.concat(l)}for(;t.length<e&&i<n.length;){const o=n[i];if(o instanceof pe)return a(o.definition);if(o instanceof ce)return a(o.definition);if(o instanceof te)r=a(o.definition);else if(o instanceof Se){const l=o.definition.concat([new W({definition:o.definition})]);return a(l)}else if(o instanceof xe){const l=[new pe({definition:o.definition}),new W({definition:[new G({terminalType:o.separator})].concat(o.definition)})];return a(l)}else if(o instanceof me){const l=o.definition.concat([new W({definition:[new G({terminalType:o.separator})].concat(o.definition)})]);r=a(l)}else if(o instanceof W){const l=o.definition.concat([new W({definition:o.definition})]);r=a(l)}else{if(o instanceof ge)return C(o.definition,l=>{D(l.definition)===!1&&(r=a(l.definition))}),r;if(o instanceof G)t.push(o.terminalType);else throw Error("non exhaustive match")}i++}return r.push({partialPath:t,suffixDef:J(n,i)}),r}function Rc(n,e,t,r){const i="EXIT_NONE_TERMINAL",s=[i],a="EXIT_ALTERNATIVE";let o=!1;const l=e.length,c=l-r-1,u=[],d=[];for(d.push({idx:-1,def:n,ruleStack:[],occurrenceStack:[]});!D(d);){const h=d.pop();if(h===a){o&&Jt(d).idx<=c&&d.pop();continue}const f=h.def,m=h.idx,g=h.ruleStack,A=h.occurrenceStack;if(D(f))continue;const y=f[0];if(y===i){const E={idx:m,def:J(f),ruleStack:Yn(g),occurrenceStack:Yn(A)};d.push(E)}else if(y instanceof G)if(m<l-1){const E=m+1,R=e[E];if(t(R,y.terminalType)){const I={idx:E,def:J(f),ruleStack:g,occurrenceStack:A};d.push(I)}}else if(m===l-1)u.push({nextTokenType:y.terminalType,nextTokenOccurrence:y.idx,ruleStack:g,occurrenceStack:A}),o=!0;else throw Error("non exhaustive match");else if(y instanceof ce){const E=ne(g);E.push(y.nonTerminalName);const R=ne(A);R.push(y.idx);const I={idx:m,def:y.definition.concat(s,J(f)),ruleStack:E,occurrenceStack:R};d.push(I)}else if(y instanceof te){const E={idx:m,def:J(f),ruleStack:g,occurrenceStack:A};d.push(E),d.push(a);const R={idx:m,def:y.definition.concat(J(f)),ruleStack:g,occurrenceStack:A};d.push(R)}else if(y instanceof Se){const E=new W({definition:y.definition,idx:y.idx}),R=y.definition.concat([E],J(f)),I={idx:m,def:R,ruleStack:g,occurrenceStack:A};d.push(I)}else if(y instanceof xe){const E=new G({terminalType:y.separator}),R=new W({definition:[E].concat(y.definition),idx:y.idx}),I=y.definition.concat([R],J(f)),F={idx:m,def:I,ruleStack:g,occurrenceStack:A};d.push(F)}else if(y instanceof me){const E={idx:m,def:J(f),ruleStack:g,occurrenceStack:A};d.push(E),d.push(a);const R=new G({terminalType:y.separator}),I=new W({definition:[R].concat(y.definition),idx:y.idx}),F=y.definition.concat([I],J(f)),re={idx:m,def:F,ruleStack:g,occurrenceStack:A};d.push(re)}else if(y instanceof W){const E={idx:m,def:J(f),ruleStack:g,occurrenceStack:A};d.push(E),d.push(a);const R=new W({definition:y.definition,idx:y.idx}),I=y.definition.concat([R],J(f)),F={idx:m,def:I,ruleStack:g,occurrenceStack:A};d.push(F)}else if(y instanceof ge)for(let E=y.definition.length-1;E>=0;E--){const R=y.definition[E],I={idx:m,def:R.definition.concat(J(f)),ruleStack:g,occurrenceStack:A};d.push(I),d.push(a)}else if(y instanceof pe)d.push({idx:m,def:y.definition.concat(J(f)),ruleStack:g,occurrenceStack:A});else if(y instanceof an)d.push(xh(y,m,g,A));else throw Error("non exhaustive match")}return u}function xh(n,e,t,r){const i=ne(t);i.push(n.name);const s=ne(r);return s.push(1),{idx:e,def:n.definition,ruleStack:i,occurrenceStack:s}}var B;(function(n){n[n.OPTION=0]="OPTION",n[n.REPETITION=1]="REPETITION",n[n.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",n[n.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",n[n.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",n[n.ALTERNATION=5]="ALTERNATION"})(B||(B={}));function oa(n){if(n instanceof te||n==="Option")return B.OPTION;if(n instanceof W||n==="Repetition")return B.REPETITION;if(n instanceof Se||n==="RepetitionMandatory")return B.REPETITION_MANDATORY;if(n instanceof xe||n==="RepetitionMandatoryWithSeparator")return B.REPETITION_MANDATORY_WITH_SEPARATOR;if(n instanceof me||n==="RepetitionWithSeparator")return B.REPETITION_WITH_SEPARATOR;if(n instanceof ge||n==="Alternation")return B.ALTERNATION;throw Error("non exhaustive match")}function Ya(n){const{occurrence:e,rule:t,prodType:r,maxLookahead:i}=n,s=oa(r);return s===B.ALTERNATION?$i(e,t,i):ki(e,t,s,i)}function Ih(n,e,t,r,i,s){const a=$i(n,e,t),o=Ec(a)?Xr:ir;return s(a,r,o,i)}function Ch(n,e,t,r,i,s){const a=ki(n,e,i,t),o=Ec(a)?Xr:ir;return s(a[0],o,r)}function Nh(n,e,t,r){const i=n.length,s=Oe(n,a=>Oe(a,o=>o.length===1));if(e)return function(a){const o=S(a,l=>l.GATE);for(let l=0;l<i;l++){const c=n[l],u=c.length,d=o[l];if(!(d!==void 0&&d.call(this)===!1))e:for(let h=0;h<u;h++){const f=c[h],m=f.length;for(let g=0;g<m;g++){const A=this.LA(g+1);if(t(A,f[g])===!1)continue e}return l}}};if(s&&!r){const a=S(n,l=>Ne(l)),o=le(a,(l,c,u)=>(C(c,d=>{N(l,d.tokenTypeIdx)||(l[d.tokenTypeIdx]=u),C(d.categoryMatches,h=>{N(l,h)||(l[h]=u)})}),l),{});return function(){const l=this.LA(1);return o[l.tokenTypeIdx]}}else return function(){for(let a=0;a<i;a++){const o=n[a],l=o.length;e:for(let c=0;c<l;c++){const u=o[c],d=u.length;for(let h=0;h<d;h++){const f=this.LA(h+1);if(t(f,u[h])===!1)continue e}return a}}}}function wh(n,e,t){const r=Oe(n,s=>s.length===1),i=n.length;if(r&&!t){const s=Ne(n);if(s.length===1&&D(s[0].categoryMatches)){const o=s[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===o}}else{const a=le(s,(o,l,c)=>(o[l.tokenTypeIdx]=!0,C(l.categoryMatches,u=>{o[u]=!0}),o),[]);return function(){const o=this.LA(1);return a[o.tokenTypeIdx]===!0}}}else return function(){e:for(let s=0;s<i;s++){const a=n[s],o=a.length;for(let l=0;l<o;l++){const c=this.LA(l+1);if(e(c,a[l])===!1)continue e}return!0}return!1}}class _h extends vi{constructor(e,t,r){super(),this.topProd=e,this.targetOccurrence=t,this.targetProdType=r}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,t,r,i){return e.idx===this.targetOccurrence&&this.targetProdType===t?(this.restDef=r.concat(i),!0):!1}walkOption(e,t,r){this.checkIsTarget(e,B.OPTION,t,r)||super.walkOption(e,t,r)}walkAtLeastOne(e,t,r){this.checkIsTarget(e,B.REPETITION_MANDATORY,t,r)||super.walkOption(e,t,r)}walkAtLeastOneSep(e,t,r){this.checkIsTarget(e,B.REPETITION_MANDATORY_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}walkMany(e,t,r){this.checkIsTarget(e,B.REPETITION,t,r)||super.walkOption(e,t,r)}walkManySep(e,t,r){this.checkIsTarget(e,B.REPETITION_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}}class vc extends on{constructor(e,t,r){super(),this.targetOccurrence=e,this.targetProdType=t,this.targetRef=r,this.result=[]}checkIsTarget(e,t){e.idx===this.targetOccurrence&&this.targetProdType===t&&(this.targetRef===void 0||e===this.targetRef)&&(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,B.OPTION)}visitRepetition(e){this.checkIsTarget(e,B.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,B.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,B.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,B.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,B.ALTERNATION)}}function Xa(n){const e=new Array(n);for(let t=0;t<n;t++)e[t]=[];return e}function Vi(n){let e=[""];for(let t=0;t<n.length;t++){const r=n[t],i=[];for(let s=0;s<e.length;s++){const a=e[s];i.push(a+"_"+r.tokenTypeIdx);for(let o=0;o<r.categoryMatches.length;o++){const l="_"+r.categoryMatches[o];i.push(a+l)}}e=i}return e}function Lh(n,e,t){for(let r=0;r<n.length;r++){if(r===t)continue;const i=n[r];for(let s=0;s<e.length;s++){const a=e[s];if(i[a]===!0)return!1}}return!0}function Ac(n,e){const t=S(n,a=>gs([a],1)),r=Xa(t.length),i=S(t,a=>{const o={};return C(a,l=>{const c=Vi(l.partialPath);C(c,u=>{o[u]=!0})}),o});let s=t;for(let a=1;a<=e;a++){const o=s;s=Xa(o.length);for(let l=0;l<o.length;l++){const c=o[l];for(let u=0;u<c.length;u++){const d=c[u].partialPath,h=c[u].suffixDef,f=Vi(d);if(Lh(i,f,l)||D(h)||d.length===e){const g=r[l];if(ys(g,d)===!1){g.push(d);for(let A=0;A<f.length;A++){const y=f[A];i[l][y]=!0}}}else{const g=gs(h,a+1,d);s[l]=s[l].concat(g),C(g,A=>{const y=Vi(A.partialPath);C(y,E=>{i[l][E]=!0})})}}}}return r}function $i(n,e,t,r){const i=new vc(n,B.ALTERNATION,r);return e.accept(i),Ac(i.result,t)}function ki(n,e,t,r){const i=new vc(n,t);e.accept(i);const s=i.result,o=new _h(e,n,t).startWalking(),l=new pe({definition:s}),c=new pe({definition:o});return Ac([l,c],r)}function ys(n,e){e:for(let t=0;t<n.length;t++){const r=n[t];if(r.length===e.length){for(let i=0;i<r.length;i++){const s=e[i],a=r[i];if((s===a||a.categoryMatchesMap[s.tokenTypeIdx]!==void 0)===!1)continue e}return!0}}return!1}function bh(n,e){return n.length<e.length&&Oe(n,(t,r)=>{const i=e[r];return t===i||i.categoryMatchesMap[t.tokenTypeIdx]})}function Ec(n){return Oe(n,e=>Oe(e,t=>Oe(t,r=>D(r.categoryMatches))))}function Oh(n){const e=n.lookaheadStrategy.validate({rules:n.rules,tokenTypes:n.tokenTypes,grammarName:n.grammarName});return S(e,t=>Object.assign({type:ue.CUSTOM_LOOKAHEAD_VALIDATION},t))}function Ph(n,e,t,r){const i=Ee(n,l=>Mh(l,t)),s=qh(n,e,t),a=Ee(n,l=>Wh(l,t)),o=Ee(n,l=>Gh(l,n,r,t));return i.concat(s,a,o)}function Mh(n,e){const t=new Fh;n.accept(t);const r=t.allProductions,i=hd(r,Dh),s=Me(i,o=>o.length>1);return S(z(s),o=>{const l=Pe(o),c=e.buildDuplicateFoundError(n,o),u=Ge(l),d={message:c,type:ue.DUPLICATE_PRODUCTIONS,ruleName:n.name,dslName:u,occurrence:l.idx},h=$c(l);return h&&(d.parameter=h),d})}function Dh(n){return`${Ge(n)}_#_${n.idx}_#_${$c(n)}`}function $c(n){return n instanceof G?n.terminalType.name:n instanceof ce?n.nonTerminalName:""}class Fh extends on{constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}}function Gh(n,e,t,r){const i=[];if(le(e,(a,o)=>o.name===n.name?a+1:a,0)>1){const a=r.buildDuplicateRuleNameError({topLevelRule:n,grammarName:t});i.push({message:a,type:ue.DUPLICATE_RULE_NAME,ruleName:n.name})}return i}function Uh(n,e,t){const r=[];let i;return de(e,n)||(i=`Invalid rule override, rule: ->${n}<- cannot be overridden in the grammar: ->${t}<-as it is not defined in any of the super grammars `,r.push({message:i,type:ue.INVALID_RULE_OVERRIDE,ruleName:n})),r}function kc(n,e,t,r=[]){const i=[],s=Or(e.definition);if(D(s))return[];{const a=n.name;de(s,n)&&i.push({message:t.buildLeftRecursionError({topLevelRule:n,leftRecursionPath:r}),type:ue.LEFT_RECURSION,ruleName:a});const l=pi(s,r.concat([n])),c=Ee(l,u=>{const d=ne(r);return d.push(u),kc(n,u,t,d)});return i.concat(c)}}function Or(n){let e=[];if(D(n))return e;const t=Pe(n);if(t instanceof ce)e.push(t.referencedRule);else if(t instanceof pe||t instanceof te||t instanceof Se||t instanceof xe||t instanceof me||t instanceof W)e=e.concat(Or(t.definition));else if(t instanceof ge)e=Ne(S(t.definition,s=>Or(s.definition)));else if(!(t instanceof G))throw Error("non exhaustive match");const r=qr(t),i=n.length>1;if(r&&i){const s=J(n);return e.concat(Or(s))}else return e}class la extends on{constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}}function Bh(n,e){const t=new la;n.accept(t);const r=t.alternations;return Ee(r,s=>{const a=Yn(s.definition);return Ee(a,(o,l)=>{const c=Rc([o],[],ir,1);return D(c)?[{message:e.buildEmptyAlternationError({topLevelRule:n,alternation:s,emptyChoiceIdx:l}),type:ue.NONE_LAST_EMPTY_ALT,ruleName:n.name,occurrence:s.idx,alternative:l+1}]:[]})})}function Vh(n,e,t){const r=new la;n.accept(r);let i=r.alternations;return i=mi(i,a=>a.ignoreAmbiguities===!0),Ee(i,a=>{const o=a.idx,l=a.maxLookahead||e,c=$i(o,n,l,a),u=Hh(c,a,n,t),d=zh(c,a,n,t);return u.concat(d)})}class Kh extends on{constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}}function Wh(n,e){const t=new la;n.accept(t);const r=t.alternations;return Ee(r,s=>s.definition.length>255?[{message:e.buildTooManyAlternativesError({topLevelRule:n,alternation:s}),type:ue.TOO_MANY_ALTS,ruleName:n.name,occurrence:s.idx}]:[])}function jh(n,e,t){const r=[];return C(n,i=>{const s=new Kh;i.accept(s);const a=s.allProductions;C(a,o=>{const l=oa(o),c=o.maxLookahead||e,u=o.idx,h=ki(u,i,l,c)[0];if(D(Ne(h))){const f=t.buildEmptyRepetitionError({topLevelRule:i,repetition:o});r.push({message:f,type:ue.NO_NON_EMPTY_LOOKAHEAD,ruleName:i.name})}})}),r}function Hh(n,e,t,r){const i=[],s=le(n,(o,l,c)=>(e.definition[c].ignoreAmbiguities===!0||C(l,u=>{const d=[c];C(n,(h,f)=>{c!==f&&ys(h,u)&&e.definition[f].ignoreAmbiguities!==!0&&d.push(f)}),d.length>1&&!ys(i,u)&&(i.push(u),o.push({alts:d,path:u}))}),o),[]);return S(s,o=>{const l=S(o.alts,u=>u+1);return{message:r.buildAlternationAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:l,prefixPath:o.path}),type:ue.AMBIGUOUS_ALTS,ruleName:t.name,occurrence:e.idx,alternatives:o.alts}})}function zh(n,e,t,r){const i=le(n,(a,o,l)=>{const c=S(o,u=>({idx:l,path:u}));return a.concat(c)},[]);return er(Ee(i,a=>{if(e.definition[a.idx].ignoreAmbiguities===!0)return[];const l=a.idx,c=a.path,u=ke(i,h=>e.definition[h.idx].ignoreAmbiguities!==!0&&h.idx<l&&bh(h.path,c));return S(u,h=>{const f=[h.idx+1,l+1],m=e.idx===0?"":e.idx;return{message:r.buildAlternationPrefixAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:f,prefixPath:h.path}),type:ue.AMBIGUOUS_PREFIX_ALTS,ruleName:t.name,occurrence:m,alternatives:f}})}))}function qh(n,e,t){const r=[],i=S(e,s=>s.name);return C(n,s=>{const a=s.name;if(de(i,a)){const o=t.buildNamespaceConflictError(s);r.push({message:o,type:ue.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:a})}}),r}function Yh(n){const e=qs(n,{errMsgProvider:Rh}),t={};return C(n.rules,r=>{t[r.name]=r}),vh(t,e.errMsgProvider)}function Xh(n){return n=qs(n,{errMsgProvider:pt}),Ph(n.rules,n.tokenTypes,n.errMsgProvider,n.grammarName)}const Sc="MismatchedTokenException",xc="NoViableAltException",Ic="EarlyExitException",Cc="NotAllInputParsedException",Nc=[Sc,xc,Ic,Cc];Object.freeze(Nc);function Jr(n){return de(Nc,n.name)}class Si extends Error{constructor(e,t){super(e),this.token=t,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}}class wc extends Si{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=Sc}}class Jh extends Si{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=xc}}class Qh extends Si{constructor(e,t){super(e,t),this.name=Cc}}class Zh extends Si{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=Ic}}const Ki={},_c="InRuleRecoveryException";class ep extends Error{constructor(e){super(e),this.name=_c}}class tp{initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=N(e,"recoveryEnabled")?e.recoveryEnabled:Je.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=np)}getTokenToInsert(e){const t=aa(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return t.isInsertedInRecovery=!0,t}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,t,r,i){const s=this.findReSyncTokenType(),a=this.exportLexerState(),o=[];let l=!1;const c=this.LA(1);let u=this.LA(1);const d=()=>{const h=this.LA(0),f=this.errorMessageProvider.buildMismatchTokenMessage({expected:i,actual:c,previous:h,ruleName:this.getCurrRuleFullName()}),m=new wc(f,c,this.LA(0));m.resyncedTokens=Yn(o),this.SAVE_ERROR(m)};for(;!l;)if(this.tokenMatcher(u,i)){d();return}else if(r.call(this)){d(),e.apply(this,t);return}else this.tokenMatcher(u,s)?l=!0:(u=this.SKIP_TOKEN(),this.addToResyncTokens(u,o));this.importLexerState(a)}shouldInRepetitionRecoveryBeTried(e,t,r){return!(r===!1||this.tokenMatcher(this.LA(1),e)||this.isBackTracking()||this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,t)))}getFollowsForInRuleRecovery(e,t){const r=this.getCurrentGrammarPath(e,t);return this.getNextPossibleTokenTypes(r)}tryInRuleRecovery(e,t){if(this.canRecoverWithSingleTokenInsertion(e,t))return this.getTokenToInsert(e);if(this.canRecoverWithSingleTokenDeletion(e)){const r=this.SKIP_TOKEN();return this.consumeToken(),r}throw new ep("sad sad panda")}canPerformInRuleRecovery(e,t){return this.canRecoverWithSingleTokenInsertion(e,t)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,t){if(!this.canTokenTypeBeInsertedInRecovery(e)||D(t))return!1;const r=this.LA(1);return Xt(t,s=>this.tokenMatcher(r,s))!==void 0}canRecoverWithSingleTokenDeletion(e){return this.canTokenTypeBeDeletedInRecovery(e)?this.tokenMatcher(this.LA(2),e):!1}isInCurrentRuleReSyncSet(e){const t=this.getCurrFollowKey(),r=this.getFollowSetFromFollowKey(t);return de(r,e)}findReSyncTokenType(){const e=this.flattenFollowSet();let t=this.LA(1),r=2;for(;;){const i=Xt(e,s=>Tc(t,s));if(i!==void 0)return i;t=this.LA(r),r++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return Ki;const e=this.getLastExplicitRuleShortName(),t=this.getLastExplicitRuleOccurrenceIndex(),r=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:t,inRule:this.shortRuleNameToFullName(r)}}buildFullFollowKeyStack(){const e=this.RULE_STACK,t=this.RULE_OCCURRENCE_STACK;return S(e,(r,i)=>i===0?Ki:{ruleName:this.shortRuleNameToFullName(r),idxInCallingRule:t[i],inRule:this.shortRuleNameToFullName(e[i-1])})}flattenFollowSet(){const e=S(this.buildFullFollowKeyStack(),t=>this.getFollowSetFromFollowKey(t));return Ne(e)}getFollowSetFromFollowKey(e){if(e===Ki)return[nt];const t=e.ruleName+e.idxInCallingRule+oc+e.inRule;return this.resyncFollows[t]}addToResyncTokens(e,t){return this.tokenMatcher(e,nt)||t.push(e),t}reSyncTo(e){const t=[];let r=this.LA(1);for(;this.tokenMatcher(r,e)===!1;)r=this.SKIP_TOKEN(),this.addToResyncTokens(r,t);return Yn(t)}attemptInRepetitionRecovery(e,t,r,i,s,a,o){}getCurrentGrammarPath(e,t){const r=this.getHumanReadableRuleStack(),i=ne(this.RULE_OCCURRENCE_STACK);return{ruleStack:r,occurrenceStack:i,lastTok:e,lastTokOccurrence:t}}getHumanReadableRuleStack(){return S(this.RULE_STACK,e=>this.shortRuleNameToFullName(e))}}function np(n,e,t,r,i,s,a){const o=this.getKeyForAutomaticLookahead(r,i);let l=this.firstAfterRepMap[o];if(l===void 0){const h=this.getCurrRuleFullName(),f=this.getGAstProductions()[h];l=new s(f,i).startWalking(),this.firstAfterRepMap[o]=l}let c=l.token,u=l.occurrence;const d=l.isEndOfRule;this.RULE_STACK.length===1&&d&&c===void 0&&(c=nt,u=1),!(c===void 0||u===void 0)&&this.shouldInRepetitionRecoveryBeTried(c,u,a)&&this.tryInRepetitionRecovery(n,e,t,c)}const rp=4,st=8,Lc=1<<st,bc=2<<st,Ts=3<<st,Rs=4<<st,vs=5<<st,Pr=6<<st;function Wi(n,e,t){return t|e|n}class ca{constructor(e){var t;this.maxLookahead=(t=e==null?void 0:e.maxLookahead)!==null&&t!==void 0?t:Je.maxLookahead}validate(e){const t=this.validateNoLeftRecursion(e.rules);if(D(t)){const r=this.validateEmptyOrAlternatives(e.rules),i=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),s=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);return[...t,...r,...i,...s]}return t}validateNoLeftRecursion(e){return Ee(e,t=>kc(t,t,pt))}validateEmptyOrAlternatives(e){return Ee(e,t=>Bh(t,pt))}validateAmbiguousAlternationAlternatives(e,t){return Ee(e,r=>Vh(r,t,pt))}validateSomeNonEmptyLookaheadPath(e,t){return jh(e,t,pt)}buildLookaheadForAlternation(e){return Ih(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,Nh)}buildLookaheadForOptional(e){return Ch(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,oa(e.prodType),wh)}}class ip{initLooksAhead(e){this.dynamicTokensEnabled=N(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:Je.dynamicTokensEnabled,this.maxLookahead=N(e,"maxLookahead")?e.maxLookahead:Je.maxLookahead,this.lookaheadStrategy=N(e,"lookaheadStrategy")?e.lookaheadStrategy:new ca({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){C(e,t=>{this.TRACE_INIT(`${t.name} Rule Lookahead`,()=>{const{alternation:r,repetition:i,option:s,repetitionMandatory:a,repetitionMandatoryWithSeparator:o,repetitionWithSeparator:l}=ap(t);C(r,c=>{const u=c.idx===0?"":c.idx;this.TRACE_INIT(`${Ge(c)}${u}`,()=>{const d=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:c.idx,rule:t,maxLookahead:c.maxLookahead||this.maxLookahead,hasPredicates:c.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),h=Wi(this.fullRuleNameToShort[t.name],Lc,c.idx);this.setLaFuncCache(h,d)})}),C(i,c=>{this.computeLookaheadFunc(t,c.idx,Ts,"Repetition",c.maxLookahead,Ge(c))}),C(s,c=>{this.computeLookaheadFunc(t,c.idx,bc,"Option",c.maxLookahead,Ge(c))}),C(a,c=>{this.computeLookaheadFunc(t,c.idx,Rs,"RepetitionMandatory",c.maxLookahead,Ge(c))}),C(o,c=>{this.computeLookaheadFunc(t,c.idx,Pr,"RepetitionMandatoryWithSeparator",c.maxLookahead,Ge(c))}),C(l,c=>{this.computeLookaheadFunc(t,c.idx,vs,"RepetitionWithSeparator",c.maxLookahead,Ge(c))})})})}computeLookaheadFunc(e,t,r,i,s,a){this.TRACE_INIT(`${a}${t===0?"":t}`,()=>{const o=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:t,rule:e,maxLookahead:s||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:i}),l=Wi(this.fullRuleNameToShort[e.name],r,t);this.setLaFuncCache(l,o)})}getKeyForAutomaticLookahead(e,t){const r=this.getLastExplicitRuleShortName();return Wi(r,e,t)}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,t){this.lookAheadFuncsCache.set(e,t)}}class sp extends on{constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}}const Ar=new sp;function ap(n){Ar.reset(),n.accept(Ar);const e=Ar.dslMethods;return Ar.reset(),e}function Ja(n,e){isNaN(n.startOffset)===!0?(n.startOffset=e.startOffset,n.endOffset=e.endOffset):n.endOffset<e.endOffset&&(n.endOffset=e.endOffset)}function Qa(n,e){isNaN(n.startOffset)===!0?(n.startOffset=e.startOffset,n.startColumn=e.startColumn,n.startLine=e.startLine,n.endOffset=e.endOffset,n.endColumn=e.endColumn,n.endLine=e.endLine):n.endOffset<e.endOffset&&(n.endOffset=e.endOffset,n.endColumn=e.endColumn,n.endLine=e.endLine)}function op(n,e,t){n.children[t]===void 0?n.children[t]=[e]:n.children[t].push(e)}function lp(n,e,t){n.children[e]===void 0?n.children[e]=[t]:n.children[e].push(t)}const cp="name";function Oc(n,e){Object.defineProperty(n,cp,{enumerable:!1,configurable:!0,writable:!1,value:e})}function up(n,e){const t=Yt(n),r=t.length;for(let i=0;i<r;i++){const s=t[i],a=n[s],o=a.length;for(let l=0;l<o;l++){const c=a[l];c.tokenTypeIdx===void 0&&this[c.name](c.children,e)}}}function dp(n,e){const t=function(){};Oc(t,n+"BaseSemantics");const r={visit:function(i,s){if(ee(i)&&(i=i[0]),!Ye(i))return this[i.name](i.children,s)},validateVisitor:function(){const i=hp(this,e);if(!D(i)){const s=S(i,a=>a.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${s.join(`

`).replace(/\n/g,`
	`)}`)}}};return t.prototype=r,t.prototype.constructor=t,t._RULE_NAMES=e,t}function fp(n,e,t){const r=function(){};Oc(r,n+"BaseSemanticsWithDefaults");const i=Object.create(t.prototype);return C(e,s=>{i[s]=up}),r.prototype=i,r.prototype.constructor=r,r}var As;(function(n){n[n.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",n[n.MISSING_METHOD=1]="MISSING_METHOD"})(As||(As={}));function hp(n,e){return pp(n,e)}function pp(n,e){const t=ke(e,i=>At(n[i])===!1),r=S(t,i=>({msg:`Missing visitor method: <${i}> on ${n.constructor.name} CST Visitor.`,type:As.MISSING_METHOD,methodName:i}));return er(r)}class mp{initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=N(e,"nodeLocationTracking")?e.nodeLocationTracking:Je.nodeLocationTracking,!this.outputCst)this.cstInvocationStateUpdate=Y,this.cstFinallyStateUpdate=Y,this.cstPostTerminal=Y,this.cstPostNonTerminal=Y,this.cstPostRule=Y;else if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Qa,this.setNodeLocationFromNode=Qa,this.cstPostRule=Y,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=Y,this.setNodeLocationFromNode=Y,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Ja,this.setNodeLocationFromNode=Ja,this.cstPostRule=Y,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=Y,this.setNodeLocationFromNode=Y,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=Y,this.setNodeLocationFromNode=Y,this.cstPostRule=Y,this.setInitialNodeLocation=Y;else throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`)}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){const t=this.LA(1);e.location={startOffset:t.startOffset,startLine:t.startLine,startColumn:t.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){const t={name:e,children:Object.create(null)};this.setInitialNodeLocation(t),this.CST_STACK.push(t)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){const t=this.LA(0),r=e.location;r.startOffset<=t.startOffset?(r.endOffset=t.endOffset,r.endLine=t.endLine,r.endColumn=t.endColumn):(r.startOffset=NaN,r.startLine=NaN,r.startColumn=NaN)}cstPostRuleOnlyOffset(e){const t=this.LA(0),r=e.location;r.startOffset<=t.startOffset?r.endOffset=t.endOffset:r.startOffset=NaN}cstPostTerminal(e,t){const r=this.CST_STACK[this.CST_STACK.length-1];op(r,t,e),this.setNodeLocationFromToken(r.location,t)}cstPostNonTerminal(e,t){const r=this.CST_STACK[this.CST_STACK.length-1];lp(r,t,e),this.setNodeLocationFromNode(r.location,e.location)}getBaseCstVisitorConstructor(){if(Ye(this.baseCstVisitorConstructor)){const e=dp(this.className,Yt(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if(Ye(this.baseCstVisitorWithDefaultsConstructor)){const e=fp(this.className,Yt(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){const e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}}class gp{initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):Zr}LA(e){const t=this.currIdx+e;return t<0||this.tokVectorLength<=t?Zr:this.tokVector[t]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}}class yp{ACTION(e){return e.call(this)}consume(e,t,r){return this.consumeInternal(t,e,r)}subrule(e,t,r){return this.subruleInternal(t,e,r)}option(e,t){return this.optionInternal(t,e)}or(e,t){return this.orInternal(t,e)}many(e,t){return this.manyInternal(e,t)}atLeastOne(e,t){return this.atLeastOneInternal(e,t)}CONSUME(e,t){return this.consumeInternal(e,0,t)}CONSUME1(e,t){return this.consumeInternal(e,1,t)}CONSUME2(e,t){return this.consumeInternal(e,2,t)}CONSUME3(e,t){return this.consumeInternal(e,3,t)}CONSUME4(e,t){return this.consumeInternal(e,4,t)}CONSUME5(e,t){return this.consumeInternal(e,5,t)}CONSUME6(e,t){return this.consumeInternal(e,6,t)}CONSUME7(e,t){return this.consumeInternal(e,7,t)}CONSUME8(e,t){return this.consumeInternal(e,8,t)}CONSUME9(e,t){return this.consumeInternal(e,9,t)}SUBRULE(e,t){return this.subruleInternal(e,0,t)}SUBRULE1(e,t){return this.subruleInternal(e,1,t)}SUBRULE2(e,t){return this.subruleInternal(e,2,t)}SUBRULE3(e,t){return this.subruleInternal(e,3,t)}SUBRULE4(e,t){return this.subruleInternal(e,4,t)}SUBRULE5(e,t){return this.subruleInternal(e,5,t)}SUBRULE6(e,t){return this.subruleInternal(e,6,t)}SUBRULE7(e,t){return this.subruleInternal(e,7,t)}SUBRULE8(e,t){return this.subruleInternal(e,8,t)}SUBRULE9(e,t){return this.subruleInternal(e,9,t)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,t,r=ei){if(de(this.definedRulesNames,e)){const a={message:pt.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),type:ue.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(a)}this.definedRulesNames.push(e);const i=this.defineRule(e,t,r);return this[e]=i,i}OVERRIDE_RULE(e,t,r=ei){const i=Uh(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(i);const s=this.defineRule(e,t,r);return this[e]=s,s}BACKTRACK(e,t){return function(){this.isBackTrackingStack.push(1);const r=this.saveRecogState();try{return e.apply(this,t),!0}catch(i){if(Jr(i))return!1;throw i}finally{this.reloadRecogState(r),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return xf(z(this.gastProductionsCache))}}class Tp{initRecognizerEngine(e,t){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=Xr,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},N(t,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if(ee(e)){if(D(e))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof e[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if(ee(e))this.tokensMap=le(e,(s,a)=>(s[a.name]=a,s),{});else if(N(e,"modes")&&Oe(Ne(z(e.modes)),gh)){const s=Ne(z(e.modes)),a=Ys(s);this.tokensMap=le(a,(o,l)=>(o[l.name]=l,o),{})}else if(ju(e))this.tokensMap=ne(e);else throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=nt;const r=N(e,"modes")?Ne(z(e.modes)):z(e),i=Oe(r,s=>D(s.categoryMatches));this.tokenMatcher=i?Xr:ir,sr(z(this.tokensMap))}defineRule(e,t,r){if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);const i=N(r,"resyncEnabled")?r.resyncEnabled:ei.resyncEnabled,s=N(r,"recoveryValueFunc")?r.recoveryValueFunc:ei.recoveryValueFunc,a=this.ruleShortNameIdx<<rp+st;this.ruleShortNameIdx++,this.shortRuleNameToFull[a]=e,this.fullRuleNameToShort[e]=a;let o;return this.outputCst===!0?o=function(...u){try{this.ruleInvocationStateUpdate(a,e,this.subruleIdx),t.apply(this,u);const d=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(d),d}catch(d){return this.invokeRuleCatch(d,i,s)}finally{this.ruleFinallyStateUpdate()}}:o=function(...u){try{return this.ruleInvocationStateUpdate(a,e,this.subruleIdx),t.apply(this,u)}catch(d){return this.invokeRuleCatch(d,i,s)}finally{this.ruleFinallyStateUpdate()}},Object.assign(o,{ruleName:e,originalGrammarAction:t})}invokeRuleCatch(e,t,r){const i=this.RULE_STACK.length===1,s=t&&!this.isBackTracking()&&this.recoveryEnabled;if(Jr(e)){const a=e;if(s){const o=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(o))if(a.resyncedTokens=this.reSyncTo(o),this.outputCst){const l=this.CST_STACK[this.CST_STACK.length-1];return l.recoveredNode=!0,l}else return r(e);else{if(this.outputCst){const l=this.CST_STACK[this.CST_STACK.length-1];l.recoveredNode=!0,a.partialCstResult=l}throw a}}else{if(i)return this.moveToTerminatedState(),r(e);throw a}}else throw e}optionInternal(e,t){const r=this.getKeyForAutomaticLookahead(bc,t);return this.optionInternalLogic(e,t,r)}optionInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof e!="function"){s=e.DEF;const a=e.GATE;if(a!==void 0){const o=i;i=()=>a.call(this)&&o.call(this)}}else s=e;if(i.call(this)===!0)return s.call(this)}atLeastOneInternal(e,t){const r=this.getKeyForAutomaticLookahead(Rs,e);return this.atLeastOneInternalLogic(e,t,r)}atLeastOneInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof t!="function"){s=t.DEF;const a=t.GATE;if(a!==void 0){const o=i;i=()=>a.call(this)&&o.call(this)}}else s=t;if(i.call(this)===!0){let a=this.doSingleRepetition(s);for(;i.call(this)===!0&&a===!0;)a=this.doSingleRepetition(s)}else throw this.raiseEarlyExitException(e,B.REPETITION_MANDATORY,t.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,t],i,Rs,e,Sh)}atLeastOneSepFirstInternal(e,t){const r=this.getKeyForAutomaticLookahead(Pr,e);this.atLeastOneSepFirstInternalLogic(e,t,r)}atLeastOneSepFirstInternalLogic(e,t,r){const i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(r).call(this)===!0){i.call(this);const o=()=>this.tokenMatcher(this.LA(1),s);for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,o,i,qa],o,Pr,e,qa)}else throw this.raiseEarlyExitException(e,B.REPETITION_MANDATORY_WITH_SEPARATOR,t.ERR_MSG)}manyInternal(e,t){const r=this.getKeyForAutomaticLookahead(Ts,e);return this.manyInternalLogic(e,t,r)}manyInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof t!="function"){s=t.DEF;const o=t.GATE;if(o!==void 0){const l=i;i=()=>o.call(this)&&l.call(this)}}else s=t;let a=!0;for(;i.call(this)===!0&&a===!0;)a=this.doSingleRepetition(s);this.attemptInRepetitionRecovery(this.manyInternal,[e,t],i,Ts,e,kh,a)}manySepFirstInternal(e,t){const r=this.getKeyForAutomaticLookahead(vs,e);this.manySepFirstInternalLogic(e,t,r)}manySepFirstInternalLogic(e,t,r){const i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(r).call(this)===!0){i.call(this);const o=()=>this.tokenMatcher(this.LA(1),s);for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,o,i,za],o,vs,e,za)}}repetitionSepSecondInternal(e,t,r,i,s){for(;r();)this.CONSUME(t),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,t,r,i,s],r,Pr,e,s)}doSingleRepetition(e){const t=this.getLexerPosition();return e.call(this),this.getLexerPosition()>t}orInternal(e,t){const r=this.getKeyForAutomaticLookahead(Lc,t),i=ee(e)?e:e.DEF,a=this.getLaFuncFromCache(r).call(this,i);if(a!==void 0)return i[a].ALT.call(this);this.raiseNoAltException(t,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){const e=this.LA(1),t=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new Qh(t,e))}}subruleInternal(e,t,r){let i;try{const s=r!==void 0?r.ARGS:void 0;return this.subruleIdx=t,i=e.apply(this,s),this.cstPostNonTerminal(i,r!==void 0&&r.LABEL!==void 0?r.LABEL:e.ruleName),i}catch(s){throw this.subruleInternalError(s,r,e.ruleName)}}subruleInternalError(e,t,r){throw Jr(e)&&e.partialCstResult!==void 0&&(this.cstPostNonTerminal(e.partialCstResult,t!==void 0&&t.LABEL!==void 0?t.LABEL:r),delete e.partialCstResult),e}consumeInternal(e,t,r){let i;try{const s=this.LA(1);this.tokenMatcher(s,e)===!0?(this.consumeToken(),i=s):this.consumeInternalError(e,s,r)}catch(s){i=this.consumeInternalRecovery(e,t,s)}return this.cstPostTerminal(r!==void 0&&r.LABEL!==void 0?r.LABEL:e.name,i),i}consumeInternalError(e,t,r){let i;const s=this.LA(0);throw r!==void 0&&r.ERR_MSG?i=r.ERR_MSG:i=this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:t,previous:s,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new wc(i,t,s))}consumeInternalRecovery(e,t,r){if(this.recoveryEnabled&&r.name==="MismatchedTokenException"&&!this.isBackTracking()){const i=this.getFollowsForInRuleRecovery(e,t);try{return this.tryInRuleRecovery(e,i)}catch(s){throw s.name===_c?r:s}}else throw r}saveRecogState(){const e=this.errors,t=ne(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:t,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,t,r){this.RULE_OCCURRENCE_STACK.push(r),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(t)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){const e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),nt)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}}class Rp{initErrorHandler(e){this._errors=[],this.errorMessageProvider=N(e,"errorMessageProvider")?e.errorMessageProvider:Je.errorMessageProvider}SAVE_ERROR(e){if(Jr(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:ne(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return ne(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,t,r){const i=this.getCurrRuleFullName(),s=this.getGAstProductions()[i],o=ki(e,s,t,this.maxLookahead)[0],l=[];for(let u=1;u<=this.maxLookahead;u++)l.push(this.LA(u));const c=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:o,actual:l,previous:this.LA(0),customUserDescription:r,ruleName:i});throw this.SAVE_ERROR(new Zh(c,this.LA(1),this.LA(0)))}raiseNoAltException(e,t){const r=this.getCurrRuleFullName(),i=this.getGAstProductions()[r],s=$i(e,i,this.maxLookahead),a=[];for(let c=1;c<=this.maxLookahead;c++)a.push(this.LA(c));const o=this.LA(0),l=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:s,actual:a,previous:o,customUserDescription:t,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new Jh(l,this.LA(1),o))}}class vp{initContentAssist(){}computeContentAssist(e,t){const r=this.gastProductionsCache[e];if(Ye(r))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return Rc([r],t,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){const t=Pe(e.ruleStack),i=this.getGAstProductions()[t];return new $h(i,e).startWalking()}}const xi={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(xi);const Za=!0,eo=Math.pow(2,st)-1,Pc=yc({name:"RECORDING_PHASE_TOKEN",pattern:fe.NA});sr([Pc]);const Mc=aa(Pc,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(Mc);const Ap={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}};class Ep{initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let e=0;e<10;e++){const t=e>0?e:"";this[`CONSUME${t}`]=function(r,i){return this.consumeInternalRecord(r,e,i)},this[`SUBRULE${t}`]=function(r,i){return this.subruleInternalRecord(r,e,i)},this[`OPTION${t}`]=function(r){return this.optionInternalRecord(r,e)},this[`OR${t}`]=function(r){return this.orInternalRecord(r,e)},this[`MANY${t}`]=function(r){this.manyInternalRecord(e,r)},this[`MANY_SEP${t}`]=function(r){this.manySepFirstInternalRecord(e,r)},this[`AT_LEAST_ONE${t}`]=function(r){this.atLeastOneInternalRecord(e,r)},this[`AT_LEAST_ONE_SEP${t}`]=function(r){this.atLeastOneSepFirstInternalRecord(e,r)}}this.consume=function(e,t,r){return this.consumeInternalRecord(t,e,r)},this.subrule=function(e,t,r){return this.subruleInternalRecord(t,e,r)},this.option=function(e,t){return this.optionInternalRecord(t,e)},this.or=function(e,t){return this.orInternalRecord(t,e)},this.many=function(e,t){this.manyInternalRecord(e,t)},this.atLeastOne=function(e,t){this.atLeastOneInternalRecord(e,t)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{const e=this;for(let t=0;t<10;t++){const r=t>0?t:"";delete e[`CONSUME${r}`],delete e[`SUBRULE${r}`],delete e[`OPTION${r}`],delete e[`OR${r}`],delete e[`MANY${r}`],delete e[`MANY_SEP${r}`],delete e[`AT_LEAST_ONE${r}`],delete e[`AT_LEAST_ONE_SEP${r}`]}delete e.consume,delete e.subrule,delete e.option,delete e.or,delete e.many,delete e.atLeastOne,delete e.ACTION,delete e.BACKTRACK,delete e.LA})}ACTION_RECORD(e){}BACKTRACK_RECORD(e,t){return()=>!0}LA_RECORD(e){return Zr}topLevelRuleRecord(e,t){try{const r=new an({definition:[],name:e});return r.name=e,this.recordingProdStack.push(r),t.call(this),this.recordingProdStack.pop(),r}catch(r){if(r.KNOWN_RECORDER_ERROR!==!0)try{r.message=r.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch{throw r}throw r}}optionInternalRecord(e,t){return un.call(this,te,e,t)}atLeastOneInternalRecord(e,t){un.call(this,Se,t,e)}atLeastOneSepFirstInternalRecord(e,t){un.call(this,xe,t,e,Za)}manyInternalRecord(e,t){un.call(this,W,t,e)}manySepFirstInternalRecord(e,t){un.call(this,me,t,e,Za)}orInternalRecord(e,t){return $p.call(this,e,t)}subruleInternalRecord(e,t,r){if(Qr(t),!e||N(e,"ruleName")===!1){const o=new Error(`<SUBRULE${to(t)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw o.KNOWN_RECORDER_ERROR=!0,o}const i=Jt(this.recordingProdStack),s=e.ruleName,a=new ce({idx:t,nonTerminalName:s,label:r==null?void 0:r.LABEL,referencedRule:void 0});return i.definition.push(a),this.outputCst?Ap:xi}consumeInternalRecord(e,t,r){if(Qr(t),!mc(e)){const a=new Error(`<CONSUME${to(t)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw a.KNOWN_RECORDER_ERROR=!0,a}const i=Jt(this.recordingProdStack),s=new G({idx:t,terminalType:e,label:r==null?void 0:r.LABEL});return i.definition.push(s),Mc}}function un(n,e,t,r=!1){Qr(t);const i=Jt(this.recordingProdStack),s=At(e)?e:e.DEF,a=new n({definition:[],idx:t});return r&&(a.separator=e.SEP),N(e,"MAX_LOOKAHEAD")&&(a.maxLookahead=e.MAX_LOOKAHEAD),this.recordingProdStack.push(a),s.call(this),i.definition.push(a),this.recordingProdStack.pop(),xi}function $p(n,e){Qr(e);const t=Jt(this.recordingProdStack),r=ee(n)===!1,i=r===!1?n:n.DEF,s=new ge({definition:[],idx:e,ignoreAmbiguities:r&&n.IGNORE_AMBIGUITIES===!0});N(n,"MAX_LOOKAHEAD")&&(s.maxLookahead=n.MAX_LOOKAHEAD);const a=Dl(i,o=>At(o.GATE));return s.hasPredicates=a,t.definition.push(s),C(i,o=>{const l=new pe({definition:[]});s.definition.push(l),N(o,"IGNORE_AMBIGUITIES")?l.ignoreAmbiguities=o.IGNORE_AMBIGUITIES:N(o,"GATE")&&(l.ignoreAmbiguities=!0),this.recordingProdStack.push(l),o.ALT.call(this),this.recordingProdStack.pop()}),xi}function to(n){return n===0?"":`${n}`}function Qr(n){if(n<0||n>eo){const e=new Error(`Invalid DSL Method idx value: <${n}>
	Idx value must be a none negative value smaller than ${eo+1}`);throw e.KNOWN_RECORDER_ERROR=!0,e}}class kp{initPerformanceTracer(e){if(N(e,"traceInitPerf")){const t=e.traceInitPerf,r=typeof t=="number";this.traceInitMaxIdent=r?t:1/0,this.traceInitPerf=r?t>0:t}else this.traceInitMaxIdent=0,this.traceInitPerf=Je.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,t){if(this.traceInitPerf===!0){this.traceInitIndent++;const r=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${r}--> <${e}>`);const{time:i,value:s}=sc(t),a=i>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&a(`${r}<-- <${e}> time: ${i}ms`),this.traceInitIndent--,s}else return t()}}function Sp(n,e){e.forEach(t=>{const r=t.prototype;Object.getOwnPropertyNames(r).forEach(i=>{if(i==="constructor")return;const s=Object.getOwnPropertyDescriptor(r,i);s&&(s.get||s.set)?Object.defineProperty(n.prototype,i,s):n.prototype[i]=t.prototype[i]})})}const Zr=aa(nt,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(Zr);const Je=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:Nt,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),ei=Object.freeze({recoveryValueFunc:()=>{},resyncEnabled:!0});var ue;(function(n){n[n.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",n[n.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",n[n.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",n[n.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",n[n.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",n[n.LEFT_RECURSION=5]="LEFT_RECURSION",n[n.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",n[n.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",n[n.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",n[n.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",n[n.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",n[n.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",n[n.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",n[n.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(ue||(ue={}));function no(n=void 0){return function(){return n}}class ar{static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let e;this.selfAnalysisDone=!0;const t=this.className;this.TRACE_INIT("toFastProps",()=>{ac(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),C(this.definedRulesNames,i=>{const a=this[i].originalGrammarAction;let o;this.TRACE_INIT(`${i} Rule`,()=>{o=this.topLevelRuleRecord(i,a)}),this.gastProductionsCache[i]=o})}finally{this.disableRecording()}});let r=[];if(this.TRACE_INIT("Grammar Resolving",()=>{r=Yh({rules:z(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(r)}),this.TRACE_INIT("Grammar Validations",()=>{if(D(r)&&this.skipValidations===!1){const i=Xh({rules:z(this.gastProductionsCache),tokenTypes:z(this.tokensMap),errMsgProvider:pt,grammarName:t}),s=Oh({lookaheadStrategy:this.lookaheadStrategy,rules:z(this.gastProductionsCache),tokenTypes:z(this.tokensMap),grammarName:t});this.definitionErrors=this.definitionErrors.concat(i,s)}}),D(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{const i=bf(z(this.gastProductionsCache));this.resyncFollows=i}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var i,s;(s=(i=this.lookaheadStrategy).initialize)===null||s===void 0||s.call(i,{rules:z(this.gastProductionsCache)}),this.preComputeLookaheadFunctions(z(this.gastProductionsCache))})),!ar.DEFER_DEFINITION_ERRORS_HANDLING&&!D(this.definitionErrors))throw e=S(this.definitionErrors,i=>i.message),new Error(`Parser Definition Errors detected:
 ${e.join(`
-------------------------------
`)}`)})}constructor(e,t){this.definitionErrors=[],this.selfAnalysisDone=!1;const r=this;if(r.initErrorHandler(t),r.initLexerAdapter(),r.initLooksAhead(t),r.initRecognizerEngine(e,t),r.initRecoverable(t),r.initTreeBuilder(t),r.initContentAssist(),r.initGastRecorder(t),r.initPerformanceTracer(t),N(t,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=N(t,"skipValidations")?t.skipValidations:Je.skipValidations}}ar.DEFER_DEFINITION_ERRORS_HANDLING=!1;Sp(ar,[tp,ip,mp,gp,Tp,yp,Rp,vp,Ep,kp]);class xp extends ar{constructor(e,t=Je){const r=ne(t);r.outputCst=!1,super(e,r)}}function Qt(n,e,t){return`${n.name}_${e}_${t}`}const rt=1,Ip=2,Dc=4,Fc=5,or=7,Cp=8,Np=9,wp=10,_p=11,Gc=12;class ua{constructor(e){this.target=e}isEpsilon(){return!1}}class da extends ua{constructor(e,t){super(e),this.tokenType=t}}class Uc extends ua{constructor(e){super(e)}isEpsilon(){return!0}}class fa extends ua{constructor(e,t,r){super(e),this.rule=t,this.followState=r}isEpsilon(){return!0}}function Lp(n){const e={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};bp(e,n);const t=n.length;for(let r=0;r<t;r++){const i=n[r],s=$t(e,i,i);s!==void 0&&Wp(e,i,s)}return e}function bp(n,e){const t=e.length;for(let r=0;r<t;r++){const i=e[r],s=X(n,i,void 0,{type:Ip}),a=X(n,i,void 0,{type:or});s.stop=a,n.ruleToStartState.set(i,s),n.ruleToStopState.set(i,a)}}function Bc(n,e,t){return t instanceof G?ha(n,e,t.terminalType,t):t instanceof ce?Kp(n,e,t):t instanceof ge?Fp(n,e,t):t instanceof te?Gp(n,e,t):t instanceof W?Op(n,e,t):t instanceof me?Pp(n,e,t):t instanceof Se?Mp(n,e,t):t instanceof xe?Dp(n,e,t):$t(n,e,t)}function Op(n,e,t){const r=X(n,e,t,{type:Fc});at(n,r);const i=ln(n,e,r,t,$t(n,e,t));return Kc(n,e,t,i)}function Pp(n,e,t){const r=X(n,e,t,{type:Fc});at(n,r);const i=ln(n,e,r,t,$t(n,e,t)),s=ha(n,e,t.separator,t);return Kc(n,e,t,i,s)}function Mp(n,e,t){const r=X(n,e,t,{type:Dc});at(n,r);const i=ln(n,e,r,t,$t(n,e,t));return Vc(n,e,t,i)}function Dp(n,e,t){const r=X(n,e,t,{type:Dc});at(n,r);const i=ln(n,e,r,t,$t(n,e,t)),s=ha(n,e,t.separator,t);return Vc(n,e,t,i,s)}function Fp(n,e,t){const r=X(n,e,t,{type:rt});at(n,r);const i=S(t.definition,a=>Bc(n,e,a));return ln(n,e,r,t,...i)}function Gp(n,e,t){const r=X(n,e,t,{type:rt});at(n,r);const i=ln(n,e,r,t,$t(n,e,t));return Up(n,e,t,i)}function $t(n,e,t){const r=ke(S(t.definition,i=>Bc(n,e,i)),i=>i!==void 0);return r.length===1?r[0]:r.length===0?void 0:Vp(n,r)}function Vc(n,e,t,r,i){const s=r.left,a=r.right,o=X(n,e,t,{type:_p});at(n,o);const l=X(n,e,t,{type:Gc});return s.loopback=o,l.loopback=o,n.decisionMap[Qt(e,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",t.idx)]=o,H(a,o),i===void 0?(H(o,s),H(o,l)):(H(o,l),H(o,i.left),H(i.right,s)),{left:s,right:l}}function Kc(n,e,t,r,i){const s=r.left,a=r.right,o=X(n,e,t,{type:wp});at(n,o);const l=X(n,e,t,{type:Gc}),c=X(n,e,t,{type:Np});return o.loopback=c,l.loopback=c,H(o,s),H(o,l),H(a,c),i!==void 0?(H(c,l),H(c,i.left),H(i.right,s)):H(c,o),n.decisionMap[Qt(e,i?"RepetitionWithSeparator":"Repetition",t.idx)]=o,{left:o,right:l}}function Up(n,e,t,r){const i=r.left,s=r.right;return H(i,s),n.decisionMap[Qt(e,"Option",t.idx)]=i,r}function at(n,e){return n.decisionStates.push(e),e.decision=n.decisionStates.length-1,e.decision}function ln(n,e,t,r,...i){const s=X(n,e,r,{type:Cp,start:t});t.end=s;for(const o of i)o!==void 0?(H(t,o.left),H(o.right,s)):H(t,s);const a={left:t,right:s};return n.decisionMap[Qt(e,Bp(r),r.idx)]=t,a}function Bp(n){if(n instanceof ge)return"Alternation";if(n instanceof te)return"Option";if(n instanceof W)return"Repetition";if(n instanceof me)return"RepetitionWithSeparator";if(n instanceof Se)return"RepetitionMandatory";if(n instanceof xe)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}function Vp(n,e){const t=e.length;for(let s=0;s<t-1;s++){const a=e[s];let o;a.left.transitions.length===1&&(o=a.left.transitions[0]);const l=o instanceof fa,c=o,u=e[s+1].left;a.left.type===rt&&a.right.type===rt&&o!==void 0&&(l&&c.followState===a.right||o.target===a.right)?(l?c.followState=u:o.target=u,jp(n,a.right)):H(a.right,u)}const r=e[0],i=e[t-1];return{left:r.left,right:i.right}}function ha(n,e,t,r){const i=X(n,e,r,{type:rt}),s=X(n,e,r,{type:rt});return pa(i,new da(s,t)),{left:i,right:s}}function Kp(n,e,t){const r=t.referencedRule,i=n.ruleToStartState.get(r),s=X(n,e,t,{type:rt}),a=X(n,e,t,{type:rt}),o=new fa(i,r,a);return pa(s,o),{left:s,right:a}}function Wp(n,e,t){const r=n.ruleToStartState.get(e);H(r,t.left);const i=n.ruleToStopState.get(e);return H(t.right,i),{left:r,right:i}}function H(n,e){const t=new Uc(e);pa(n,t)}function X(n,e,t,r){const i=Object.assign({atn:n,production:t,epsilonOnlyTransitions:!1,rule:e,transitions:[],nextTokenWithinRule:[],stateNumber:n.states.length},r);return n.states.push(i),i}function pa(n,e){n.transitions.length===0&&(n.epsilonOnlyTransitions=e.isEpsilon()),n.transitions.push(e)}function jp(n,e){n.states.splice(n.states.indexOf(e),1)}const ti={};class Es{constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){const t=Wc(e);t in this.map||(this.map[t]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return S(this.configs,e=>e.alt)}get key(){let e="";for(const t in this.map)e+=t+":";return e}}function Wc(n,e=!0){return`${e?`a${n.alt}`:""}s${n.state.stateNumber}:${n.stack.map(t=>t.stateNumber.toString()).join("_")}`}function Hp(n,e){const t={};return r=>{const i=r.toString();let s=t[i];return s!==void 0||(s={atnStartState:n,decision:e,states:{}},t[i]=s),s}}class jc{constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,t){this.predicates[e]=t}toString(){let e="";const t=this.predicates.length;for(let r=0;r<t;r++)e+=this.predicates[r]===!0?"1":"0";return e}}const ro=new jc;class zp extends ca{constructor(e){var t;super(),this.logging=(t=e==null?void 0:e.logging)!==null&&t!==void 0?t:r=>console.log(r)}initialize(e){this.atn=Lp(e.rules),this.dfas=qp(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){const{prodOccurrence:t,rule:r,hasPredicates:i,dynamicTokensEnabled:s}=e,a=this.dfas,o=this.logging,l=Qt(r,"Alternation",t),u=this.atn.decisionMap[l].decision,d=S(Ya({maxLookahead:1,occurrence:t,prodType:"Alternation",rule:r}),h=>S(h,f=>f[0]));if(io(d,!1)&&!s){const h=le(d,(f,m,g)=>(C(m,A=>{A&&(f[A.tokenTypeIdx]=g,C(A.categoryMatches,y=>{f[y]=g}))}),f),{});return i?function(f){var m;const g=this.LA(1),A=h[g.tokenTypeIdx];if(f!==void 0&&A!==void 0){const y=(m=f[A])===null||m===void 0?void 0:m.GATE;if(y!==void 0&&y.call(this)===!1)return}return A}:function(){const f=this.LA(1);return h[f.tokenTypeIdx]}}else return i?function(h){const f=new jc,m=h===void 0?0:h.length;for(let A=0;A<m;A++){const y=h==null?void 0:h[A].GATE;f.set(A,y===void 0||y.call(this))}const g=ji.call(this,a,u,f,o);return typeof g=="number"?g:void 0}:function(){const h=ji.call(this,a,u,ro,o);return typeof h=="number"?h:void 0}}buildLookaheadForOptional(e){const{prodOccurrence:t,rule:r,prodType:i,dynamicTokensEnabled:s}=e,a=this.dfas,o=this.logging,l=Qt(r,i,t),u=this.atn.decisionMap[l].decision,d=S(Ya({maxLookahead:1,occurrence:t,prodType:i,rule:r}),h=>S(h,f=>f[0]));if(io(d)&&d[0][0]&&!s){const h=d[0],f=Ne(h);if(f.length===1&&D(f[0].categoryMatches)){const g=f[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===g}}else{const m=le(f,(g,A)=>(A!==void 0&&(g[A.tokenTypeIdx]=!0,C(A.categoryMatches,y=>{g[y]=!0})),g),{});return function(){const g=this.LA(1);return m[g.tokenTypeIdx]===!0}}}return function(){const h=ji.call(this,a,u,ro,o);return typeof h=="object"?!1:h===0}}}function io(n,e=!0){const t=new Set;for(const r of n){const i=new Set;for(const s of r){if(s===void 0){if(e)break;return!1}const a=[s.tokenTypeIdx].concat(s.categoryMatches);for(const o of a)if(t.has(o)){if(!i.has(o))return!1}else t.add(o),i.add(o)}}return!0}function qp(n){const e=n.decisionStates.length,t=Array(e);for(let r=0;r<e;r++)t[r]=Hp(n.decisionStates[r],r);return t}function ji(n,e,t,r){const i=n[e](t);let s=i.start;if(s===void 0){const o=sm(i.atnStartState);s=zc(i,Hc(o)),i.start=s}return Yp.apply(this,[i,s,t,r])}function Yp(n,e,t,r){let i=e,s=1;const a=[];let o=this.LA(s++);for(;;){let l=tm(i,o);if(l===void 0&&(l=Xp.apply(this,[n,i,o,s,t,r])),l===ti)return em(a,i,o);if(l.isAcceptState===!0)return l.prediction;i=l,a.push(o),o=this.LA(s++)}}function Xp(n,e,t,r,i,s){const a=nm(e.configs,t,i);if(a.size===0)return so(n,e,t,ti),ti;let o=Hc(a);const l=im(a,i);if(l!==void 0)o.isAcceptState=!0,o.prediction=l,o.configs.uniqueAlt=l;else if(cm(a)){const c=td(a.alts);o.isAcceptState=!0,o.prediction=c,o.configs.uniqueAlt=c,Jp.apply(this,[n,r,a.alts,s])}return o=so(n,e,t,o),o}function Jp(n,e,t,r){const i=[];for(let c=1;c<=e;c++)i.push(this.LA(c).tokenType);const s=n.atnStartState,a=s.rule,o=s.production,l=Qp({topLevelRule:a,ambiguityIndices:t,production:o,prefixPath:i});r(l)}function Qp(n){const e=S(n.prefixPath,i=>_t(i)).join(", "),t=n.production.idx===0?"":n.production.idx;let r=`Ambiguous Alternatives Detected: <${n.ambiguityIndices.join(", ")}> in <${Zp(n.production)}${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return r=r+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,r}function Zp(n){if(n instanceof ce)return"SUBRULE";if(n instanceof te)return"OPTION";if(n instanceof ge)return"OR";if(n instanceof Se)return"AT_LEAST_ONE";if(n instanceof xe)return"AT_LEAST_ONE_SEP";if(n instanceof me)return"MANY_SEP";if(n instanceof W)return"MANY";if(n instanceof G)return"CONSUME";throw Error("non exhaustive match")}function em(n,e,t){const r=Ee(e.configs.elements,s=>s.state.transitions),i=Ad(r.filter(s=>s instanceof da).map(s=>s.tokenType),s=>s.tokenTypeIdx);return{actualToken:t,possibleTokenTypes:i,tokenPath:n}}function tm(n,e){return n.edges[e.tokenTypeIdx]}function nm(n,e,t){const r=new Es,i=[];for(const a of n.elements){if(t.is(a.alt)===!1)continue;if(a.state.type===or){i.push(a);continue}const o=a.state.transitions.length;for(let l=0;l<o;l++){const c=a.state.transitions[l],u=rm(c,e);u!==void 0&&r.add({state:u,alt:a.alt,stack:a.stack})}}let s;if(i.length===0&&r.size===1&&(s=r),s===void 0){s=new Es;for(const a of r.elements)ni(a,s)}if(i.length>0&&!om(s))for(const a of i)s.add(a);return s}function rm(n,e){if(n instanceof da&&Tc(e,n.tokenType))return n.target}function im(n,e){let t;for(const r of n.elements)if(e.is(r.alt)===!0){if(t===void 0)t=r.alt;else if(t!==r.alt)return}return t}function Hc(n){return{configs:n,edges:{},isAcceptState:!1,prediction:-1}}function so(n,e,t,r){return r=zc(n,r),e.edges[t.tokenTypeIdx]=r,r}function zc(n,e){if(e===ti)return e;const t=e.configs.key,r=n.states[t];return r!==void 0?r:(e.configs.finalize(),n.states[t]=e,e)}function sm(n){const e=new Es,t=n.transitions.length;for(let r=0;r<t;r++){const s={state:n.transitions[r].target,alt:r,stack:[]};ni(s,e)}return e}function ni(n,e){const t=n.state;if(t.type===or){if(n.stack.length>0){const i=[...n.stack],a={state:i.pop(),alt:n.alt,stack:i};ni(a,e)}else e.add(n);return}t.epsilonOnlyTransitions||e.add(n);const r=t.transitions.length;for(let i=0;i<r;i++){const s=t.transitions[i],a=am(n,s);a!==void 0&&ni(a,e)}}function am(n,e){if(e instanceof Uc)return{state:e.target,alt:n.alt,stack:n.stack};if(e instanceof fa){const t=[...n.stack,e.followState];return{state:e.target,alt:n.alt,stack:t}}}function om(n){for(const e of n.elements)if(e.state.type===or)return!0;return!1}function lm(n){for(const e of n.elements)if(e.state.type!==or)return!1;return!0}function cm(n){if(lm(n))return!0;const e=um(n.elements);return dm(e)&&!fm(e)}function um(n){const e=new Map;for(const t of n){const r=Wc(t,!1);let i=e.get(r);i===void 0&&(i={},e.set(r,i)),i[t.alt]=!0}return e}function dm(n){for(const e of Array.from(n.values()))if(Object.keys(e).length>1)return!0;return!1}function fm(n){for(const e of Array.from(n.values()))if(Object.keys(e).length===1)return!0;return!1}var ao;(function(n){function e(t){return typeof t=="string"}n.is=e})(ao||(ao={}));var $s;(function(n){function e(t){return typeof t=="string"}n.is=e})($s||($s={}));var oo;(function(n){n.MIN_VALUE=-2147483648,n.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&n.MIN_VALUE<=t&&t<=n.MAX_VALUE}n.is=e})(oo||(oo={}));var ri;(function(n){n.MIN_VALUE=0,n.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&n.MIN_VALUE<=t&&t<=n.MAX_VALUE}n.is=e})(ri||(ri={}));var P;(function(n){function e(r,i){return r===Number.MAX_VALUE&&(r=ri.MAX_VALUE),i===Number.MAX_VALUE&&(i=ri.MAX_VALUE),{line:r,character:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&p.uinteger(i.line)&&p.uinteger(i.character)}n.is=t})(P||(P={}));var O;(function(n){function e(r,i,s,a){if(p.uinteger(r)&&p.uinteger(i)&&p.uinteger(s)&&p.uinteger(a))return{start:P.create(r,i),end:P.create(s,a)};if(P.is(r)&&P.is(i))return{start:r,end:i};throw new Error(`Range#create called with invalid arguments[${r}, ${i}, ${s}, ${a}]`)}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&P.is(i.start)&&P.is(i.end)}n.is=t})(O||(O={}));var ii;(function(n){function e(r,i){return{uri:r,range:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&O.is(i.range)&&(p.string(i.uri)||p.undefined(i.uri))}n.is=t})(ii||(ii={}));var lo;(function(n){function e(r,i,s,a){return{targetUri:r,targetRange:i,targetSelectionRange:s,originSelectionRange:a}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&O.is(i.targetRange)&&p.string(i.targetUri)&&O.is(i.targetSelectionRange)&&(O.is(i.originSelectionRange)||p.undefined(i.originSelectionRange))}n.is=t})(lo||(lo={}));var ks;(function(n){function e(r,i,s,a){return{red:r,green:i,blue:s,alpha:a}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.numberRange(i.red,0,1)&&p.numberRange(i.green,0,1)&&p.numberRange(i.blue,0,1)&&p.numberRange(i.alpha,0,1)}n.is=t})(ks||(ks={}));var co;(function(n){function e(r,i){return{range:r,color:i}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&O.is(i.range)&&ks.is(i.color)}n.is=t})(co||(co={}));var uo;(function(n){function e(r,i,s){return{label:r,textEdit:i,additionalTextEdits:s}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.string(i.label)&&(p.undefined(i.textEdit)||en.is(i))&&(p.undefined(i.additionalTextEdits)||p.typedArray(i.additionalTextEdits,en.is))}n.is=t})(uo||(uo={}));var fo;(function(n){n.Comment="comment",n.Imports="imports",n.Region="region"})(fo||(fo={}));var ho;(function(n){function e(r,i,s,a,o,l){const c={startLine:r,endLine:i};return p.defined(s)&&(c.startCharacter=s),p.defined(a)&&(c.endCharacter=a),p.defined(o)&&(c.kind=o),p.defined(l)&&(c.collapsedText=l),c}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.uinteger(i.startLine)&&p.uinteger(i.startLine)&&(p.undefined(i.startCharacter)||p.uinteger(i.startCharacter))&&(p.undefined(i.endCharacter)||p.uinteger(i.endCharacter))&&(p.undefined(i.kind)||p.string(i.kind))}n.is=t})(ho||(ho={}));var Ss;(function(n){function e(r,i){return{location:r,message:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&ii.is(i.location)&&p.string(i.message)}n.is=t})(Ss||(Ss={}));var po;(function(n){n.Error=1,n.Warning=2,n.Information=3,n.Hint=4})(po||(po={}));var mo;(function(n){n.Unnecessary=1,n.Deprecated=2})(mo||(mo={}));var go;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&p.string(r.href)}n.is=e})(go||(go={}));var si;(function(n){function e(r,i,s,a,o,l){let c={range:r,message:i};return p.defined(s)&&(c.severity=s),p.defined(a)&&(c.code=a),p.defined(o)&&(c.source=o),p.defined(l)&&(c.relatedInformation=l),c}n.create=e;function t(r){var i;let s=r;return p.defined(s)&&O.is(s.range)&&p.string(s.message)&&(p.number(s.severity)||p.undefined(s.severity))&&(p.integer(s.code)||p.string(s.code)||p.undefined(s.code))&&(p.undefined(s.codeDescription)||p.string((i=s.codeDescription)===null||i===void 0?void 0:i.href))&&(p.string(s.source)||p.undefined(s.source))&&(p.undefined(s.relatedInformation)||p.typedArray(s.relatedInformation,Ss.is))}n.is=t})(si||(si={}));var Zt;(function(n){function e(r,i,...s){let a={title:r,command:i};return p.defined(s)&&s.length>0&&(a.arguments=s),a}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.title)&&p.string(i.command)}n.is=t})(Zt||(Zt={}));var en;(function(n){function e(s,a){return{range:s,newText:a}}n.replace=e;function t(s,a){return{range:{start:s,end:s},newText:a}}n.insert=t;function r(s){return{range:s,newText:""}}n.del=r;function i(s){const a=s;return p.objectLiteral(a)&&p.string(a.newText)&&O.is(a.range)}n.is=i})(en||(en={}));var xs;(function(n){function e(r,i,s){const a={label:r};return i!==void 0&&(a.needsConfirmation=i),s!==void 0&&(a.description=s),a}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.string(i.label)&&(p.boolean(i.needsConfirmation)||i.needsConfirmation===void 0)&&(p.string(i.description)||i.description===void 0)}n.is=t})(xs||(xs={}));var tn;(function(n){function e(t){const r=t;return p.string(r)}n.is=e})(tn||(tn={}));var yo;(function(n){function e(s,a,o){return{range:s,newText:a,annotationId:o}}n.replace=e;function t(s,a,o){return{range:{start:s,end:s},newText:a,annotationId:o}}n.insert=t;function r(s,a){return{range:s,newText:"",annotationId:a}}n.del=r;function i(s){const a=s;return en.is(a)&&(xs.is(a.annotationId)||tn.is(a.annotationId))}n.is=i})(yo||(yo={}));var Is;(function(n){function e(r,i){return{textDocument:r,edits:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&Ls.is(i.textDocument)&&Array.isArray(i.edits)}n.is=t})(Is||(Is={}));var Cs;(function(n){function e(r,i,s){let a={kind:"create",uri:r};return i!==void 0&&(i.overwrite!==void 0||i.ignoreIfExists!==void 0)&&(a.options=i),s!==void 0&&(a.annotationId=s),a}n.create=e;function t(r){let i=r;return i&&i.kind==="create"&&p.string(i.uri)&&(i.options===void 0||(i.options.overwrite===void 0||p.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||p.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||tn.is(i.annotationId))}n.is=t})(Cs||(Cs={}));var Ns;(function(n){function e(r,i,s,a){let o={kind:"rename",oldUri:r,newUri:i};return s!==void 0&&(s.overwrite!==void 0||s.ignoreIfExists!==void 0)&&(o.options=s),a!==void 0&&(o.annotationId=a),o}n.create=e;function t(r){let i=r;return i&&i.kind==="rename"&&p.string(i.oldUri)&&p.string(i.newUri)&&(i.options===void 0||(i.options.overwrite===void 0||p.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||p.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||tn.is(i.annotationId))}n.is=t})(Ns||(Ns={}));var ws;(function(n){function e(r,i,s){let a={kind:"delete",uri:r};return i!==void 0&&(i.recursive!==void 0||i.ignoreIfNotExists!==void 0)&&(a.options=i),s!==void 0&&(a.annotationId=s),a}n.create=e;function t(r){let i=r;return i&&i.kind==="delete"&&p.string(i.uri)&&(i.options===void 0||(i.options.recursive===void 0||p.boolean(i.options.recursive))&&(i.options.ignoreIfNotExists===void 0||p.boolean(i.options.ignoreIfNotExists)))&&(i.annotationId===void 0||tn.is(i.annotationId))}n.is=t})(ws||(ws={}));var _s;(function(n){function e(t){let r=t;return r&&(r.changes!==void 0||r.documentChanges!==void 0)&&(r.documentChanges===void 0||r.documentChanges.every(i=>p.string(i.kind)?Cs.is(i)||Ns.is(i)||ws.is(i):Is.is(i)))}n.is=e})(_s||(_s={}));var To;(function(n){function e(r){return{uri:r}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)}n.is=t})(To||(To={}));var Ro;(function(n){function e(r,i){return{uri:r,version:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&p.integer(i.version)}n.is=t})(Ro||(Ro={}));var Ls;(function(n){function e(r,i){return{uri:r,version:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&(i.version===null||p.integer(i.version))}n.is=t})(Ls||(Ls={}));var vo;(function(n){function e(r,i,s,a){return{uri:r,languageId:i,version:s,text:a}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&p.string(i.languageId)&&p.integer(i.version)&&p.string(i.text)}n.is=t})(vo||(vo={}));var bs;(function(n){n.PlainText="plaintext",n.Markdown="markdown";function e(t){const r=t;return r===n.PlainText||r===n.Markdown}n.is=e})(bs||(bs={}));var Jn;(function(n){function e(t){const r=t;return p.objectLiteral(t)&&bs.is(r.kind)&&p.string(r.value)}n.is=e})(Jn||(Jn={}));var Ao;(function(n){n.Text=1,n.Method=2,n.Function=3,n.Constructor=4,n.Field=5,n.Variable=6,n.Class=7,n.Interface=8,n.Module=9,n.Property=10,n.Unit=11,n.Value=12,n.Enum=13,n.Keyword=14,n.Snippet=15,n.Color=16,n.File=17,n.Reference=18,n.Folder=19,n.EnumMember=20,n.Constant=21,n.Struct=22,n.Event=23,n.Operator=24,n.TypeParameter=25})(Ao||(Ao={}));var Eo;(function(n){n.PlainText=1,n.Snippet=2})(Eo||(Eo={}));var $o;(function(n){n.Deprecated=1})($o||($o={}));var ko;(function(n){function e(r,i,s){return{newText:r,insert:i,replace:s}}n.create=e;function t(r){const i=r;return i&&p.string(i.newText)&&O.is(i.insert)&&O.is(i.replace)}n.is=t})(ko||(ko={}));var So;(function(n){n.asIs=1,n.adjustIndentation=2})(So||(So={}));var xo;(function(n){function e(t){const r=t;return r&&(p.string(r.detail)||r.detail===void 0)&&(p.string(r.description)||r.description===void 0)}n.is=e})(xo||(xo={}));var Io;(function(n){function e(t){return{label:t}}n.create=e})(Io||(Io={}));var Co;(function(n){function e(t,r){return{items:t||[],isIncomplete:!!r}}n.create=e})(Co||(Co={}));var ai;(function(n){function e(r){return r.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}n.fromPlainText=e;function t(r){const i=r;return p.string(i)||p.objectLiteral(i)&&p.string(i.language)&&p.string(i.value)}n.is=t})(ai||(ai={}));var No;(function(n){function e(t){let r=t;return!!r&&p.objectLiteral(r)&&(Jn.is(r.contents)||ai.is(r.contents)||p.typedArray(r.contents,ai.is))&&(t.range===void 0||O.is(t.range))}n.is=e})(No||(No={}));var wo;(function(n){function e(t,r){return r?{label:t,documentation:r}:{label:t}}n.create=e})(wo||(wo={}));var _o;(function(n){function e(t,r,...i){let s={label:t};return p.defined(r)&&(s.documentation=r),p.defined(i)?s.parameters=i:s.parameters=[],s}n.create=e})(_o||(_o={}));var Lo;(function(n){n.Text=1,n.Read=2,n.Write=3})(Lo||(Lo={}));var bo;(function(n){function e(t,r){let i={range:t};return p.number(r)&&(i.kind=r),i}n.create=e})(bo||(bo={}));var Oo;(function(n){n.File=1,n.Module=2,n.Namespace=3,n.Package=4,n.Class=5,n.Method=6,n.Property=7,n.Field=8,n.Constructor=9,n.Enum=10,n.Interface=11,n.Function=12,n.Variable=13,n.Constant=14,n.String=15,n.Number=16,n.Boolean=17,n.Array=18,n.Object=19,n.Key=20,n.Null=21,n.EnumMember=22,n.Struct=23,n.Event=24,n.Operator=25,n.TypeParameter=26})(Oo||(Oo={}));var Po;(function(n){n.Deprecated=1})(Po||(Po={}));var Mo;(function(n){function e(t,r,i,s,a){let o={name:t,kind:r,location:{uri:s,range:i}};return a&&(o.containerName=a),o}n.create=e})(Mo||(Mo={}));var Do;(function(n){function e(t,r,i,s){return s!==void 0?{name:t,kind:r,location:{uri:i,range:s}}:{name:t,kind:r,location:{uri:i}}}n.create=e})(Do||(Do={}));var Fo;(function(n){function e(r,i,s,a,o,l){let c={name:r,detail:i,kind:s,range:a,selectionRange:o};return l!==void 0&&(c.children=l),c}n.create=e;function t(r){let i=r;return i&&p.string(i.name)&&p.number(i.kind)&&O.is(i.range)&&O.is(i.selectionRange)&&(i.detail===void 0||p.string(i.detail))&&(i.deprecated===void 0||p.boolean(i.deprecated))&&(i.children===void 0||Array.isArray(i.children))&&(i.tags===void 0||Array.isArray(i.tags))}n.is=t})(Fo||(Fo={}));var Go;(function(n){n.Empty="",n.QuickFix="quickfix",n.Refactor="refactor",n.RefactorExtract="refactor.extract",n.RefactorInline="refactor.inline",n.RefactorRewrite="refactor.rewrite",n.Source="source",n.SourceOrganizeImports="source.organizeImports",n.SourceFixAll="source.fixAll"})(Go||(Go={}));var oi;(function(n){n.Invoked=1,n.Automatic=2})(oi||(oi={}));var Uo;(function(n){function e(r,i,s){let a={diagnostics:r};return i!=null&&(a.only=i),s!=null&&(a.triggerKind=s),a}n.create=e;function t(r){let i=r;return p.defined(i)&&p.typedArray(i.diagnostics,si.is)&&(i.only===void 0||p.typedArray(i.only,p.string))&&(i.triggerKind===void 0||i.triggerKind===oi.Invoked||i.triggerKind===oi.Automatic)}n.is=t})(Uo||(Uo={}));var Bo;(function(n){function e(r,i,s){let a={title:r},o=!0;return typeof i=="string"?(o=!1,a.kind=i):Zt.is(i)?a.command=i:a.edit=i,o&&s!==void 0&&(a.kind=s),a}n.create=e;function t(r){let i=r;return i&&p.string(i.title)&&(i.diagnostics===void 0||p.typedArray(i.diagnostics,si.is))&&(i.kind===void 0||p.string(i.kind))&&(i.edit!==void 0||i.command!==void 0)&&(i.command===void 0||Zt.is(i.command))&&(i.isPreferred===void 0||p.boolean(i.isPreferred))&&(i.edit===void 0||_s.is(i.edit))}n.is=t})(Bo||(Bo={}));var Vo;(function(n){function e(r,i){let s={range:r};return p.defined(i)&&(s.data=i),s}n.create=e;function t(r){let i=r;return p.defined(i)&&O.is(i.range)&&(p.undefined(i.command)||Zt.is(i.command))}n.is=t})(Vo||(Vo={}));var Ko;(function(n){function e(r,i){return{tabSize:r,insertSpaces:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.uinteger(i.tabSize)&&p.boolean(i.insertSpaces)}n.is=t})(Ko||(Ko={}));var Wo;(function(n){function e(r,i,s){return{range:r,target:i,data:s}}n.create=e;function t(r){let i=r;return p.defined(i)&&O.is(i.range)&&(p.undefined(i.target)||p.string(i.target))}n.is=t})(Wo||(Wo={}));var jo;(function(n){function e(r,i){return{range:r,parent:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&O.is(i.range)&&(i.parent===void 0||n.is(i.parent))}n.is=t})(jo||(jo={}));var Ho;(function(n){n.namespace="namespace",n.type="type",n.class="class",n.enum="enum",n.interface="interface",n.struct="struct",n.typeParameter="typeParameter",n.parameter="parameter",n.variable="variable",n.property="property",n.enumMember="enumMember",n.event="event",n.function="function",n.method="method",n.macro="macro",n.keyword="keyword",n.modifier="modifier",n.comment="comment",n.string="string",n.number="number",n.regexp="regexp",n.operator="operator",n.decorator="decorator"})(Ho||(Ho={}));var zo;(function(n){n.declaration="declaration",n.definition="definition",n.readonly="readonly",n.static="static",n.deprecated="deprecated",n.abstract="abstract",n.async="async",n.modification="modification",n.documentation="documentation",n.defaultLibrary="defaultLibrary"})(zo||(zo={}));var qo;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&(r.resultId===void 0||typeof r.resultId=="string")&&Array.isArray(r.data)&&(r.data.length===0||typeof r.data[0]=="number")}n.is=e})(qo||(qo={}));var Yo;(function(n){function e(r,i){return{range:r,text:i}}n.create=e;function t(r){const i=r;return i!=null&&O.is(i.range)&&p.string(i.text)}n.is=t})(Yo||(Yo={}));var Xo;(function(n){function e(r,i,s){return{range:r,variableName:i,caseSensitiveLookup:s}}n.create=e;function t(r){const i=r;return i!=null&&O.is(i.range)&&p.boolean(i.caseSensitiveLookup)&&(p.string(i.variableName)||i.variableName===void 0)}n.is=t})(Xo||(Xo={}));var Jo;(function(n){function e(r,i){return{range:r,expression:i}}n.create=e;function t(r){const i=r;return i!=null&&O.is(i.range)&&(p.string(i.expression)||i.expression===void 0)}n.is=t})(Jo||(Jo={}));var Qo;(function(n){function e(r,i){return{frameId:r,stoppedLocation:i}}n.create=e;function t(r){const i=r;return p.defined(i)&&O.is(r.stoppedLocation)}n.is=t})(Qo||(Qo={}));var Os;(function(n){n.Type=1,n.Parameter=2;function e(t){return t===1||t===2}n.is=e})(Os||(Os={}));var Ps;(function(n){function e(r){return{value:r}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&(i.tooltip===void 0||p.string(i.tooltip)||Jn.is(i.tooltip))&&(i.location===void 0||ii.is(i.location))&&(i.command===void 0||Zt.is(i.command))}n.is=t})(Ps||(Ps={}));var Zo;(function(n){function e(r,i,s){const a={position:r,label:i};return s!==void 0&&(a.kind=s),a}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&P.is(i.position)&&(p.string(i.label)||p.typedArray(i.label,Ps.is))&&(i.kind===void 0||Os.is(i.kind))&&i.textEdits===void 0||p.typedArray(i.textEdits,en.is)&&(i.tooltip===void 0||p.string(i.tooltip)||Jn.is(i.tooltip))&&(i.paddingLeft===void 0||p.boolean(i.paddingLeft))&&(i.paddingRight===void 0||p.boolean(i.paddingRight))}n.is=t})(Zo||(Zo={}));var el;(function(n){function e(t){return{kind:"snippet",value:t}}n.createSnippet=e})(el||(el={}));var tl;(function(n){function e(t,r,i,s){return{insertText:t,filterText:r,range:i,command:s}}n.create=e})(tl||(tl={}));var nl;(function(n){function e(t){return{items:t}}n.create=e})(nl||(nl={}));var rl;(function(n){n.Invoked=0,n.Automatic=1})(rl||(rl={}));var il;(function(n){function e(t,r){return{range:t,text:r}}n.create=e})(il||(il={}));var sl;(function(n){function e(t,r){return{triggerKind:t,selectedCompletionInfo:r}}n.create=e})(sl||(sl={}));var al;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&$s.is(r.uri)&&p.string(r.name)}n.is=e})(al||(al={}));var ol;(function(n){function e(s,a,o,l){return new hm(s,a,o,l)}n.create=e;function t(s){let a=s;return!!(p.defined(a)&&p.string(a.uri)&&(p.undefined(a.languageId)||p.string(a.languageId))&&p.uinteger(a.lineCount)&&p.func(a.getText)&&p.func(a.positionAt)&&p.func(a.offsetAt))}n.is=t;function r(s,a){let o=s.getText(),l=i(a,(u,d)=>{let h=u.range.start.line-d.range.start.line;return h===0?u.range.start.character-d.range.start.character:h}),c=o.length;for(let u=l.length-1;u>=0;u--){let d=l[u],h=s.offsetAt(d.range.start),f=s.offsetAt(d.range.end);if(f<=c)o=o.substring(0,h)+d.newText+o.substring(f,o.length);else throw new Error("Overlapping edit");c=h}return o}n.applyEdits=r;function i(s,a){if(s.length<=1)return s;const o=s.length/2|0,l=s.slice(0,o),c=s.slice(o);i(l,a),i(c,a);let u=0,d=0,h=0;for(;u<l.length&&d<c.length;)a(l[u],c[d])<=0?s[h++]=l[u++]:s[h++]=c[d++];for(;u<l.length;)s[h++]=l[u++];for(;d<c.length;)s[h++]=c[d++];return s}})(ol||(ol={}));let hm=class{constructor(e,t,r,i){this._uri=e,this._languageId=t,this._version=r,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],t=this._content,r=!0;for(let i=0;i<t.length;i++){r&&(e.push(i),r=!1);let s=t.charAt(i);r=s==="\r"||s===`
`,s==="\r"&&i+1<t.length&&t.charAt(i+1)===`
`&&i++}r&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),r=0,i=t.length;if(i===0)return P.create(0,e);for(;r<i;){let a=Math.floor((r+i)/2);t[a]>e?i=a:r=a+1}let s=r-1;return P.create(s,e-t[s])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let r=t[e.line],i=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(r+e.character,i),r)}get lineCount(){return this.getLineOffsets().length}};var p;(function(n){const e=Object.prototype.toString;function t(f){return typeof f<"u"}n.defined=t;function r(f){return typeof f>"u"}n.undefined=r;function i(f){return f===!0||f===!1}n.boolean=i;function s(f){return e.call(f)==="[object String]"}n.string=s;function a(f){return e.call(f)==="[object Number]"}n.number=a;function o(f,m,g){return e.call(f)==="[object Number]"&&m<=f&&f<=g}n.numberRange=o;function l(f){return e.call(f)==="[object Number]"&&-2147483648<=f&&f<=2147483647}n.integer=l;function c(f){return e.call(f)==="[object Number]"&&0<=f&&f<=2147483647}n.uinteger=c;function u(f){return e.call(f)==="[object Function]"}n.func=u;function d(f){return f!==null&&typeof f=="object"}n.objectLiteral=d;function h(f,m){return Array.isArray(f)&&f.every(m)}n.typedArray=h})(p||(p={}));class pm{constructor(){this.nodeStack=[]}get current(){var e;return(e=this.nodeStack[this.nodeStack.length-1])!==null&&e!==void 0?e:this.rootNode}buildRootNode(e){return this.rootNode=new Yc(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){const t=new ma;return t.grammarSource=e,t.root=this.rootNode,this.current.content.push(t),this.nodeStack.push(t),t}buildLeafNode(e,t){const r=new Ms(e.startOffset,e.image.length,cs(e),e.tokenType,!t);return r.grammarSource=t,r.root=this.rootNode,this.current.content.push(r),r}removeNode(e){const t=e.container;if(t){const r=t.content.indexOf(e);r>=0&&t.content.splice(r,1)}}addHiddenNodes(e){const t=[];for(const s of e){const a=new Ms(s.startOffset,s.image.length,cs(s),s.tokenType,!0);a.root=this.rootNode,t.push(a)}let r=this.current,i=!1;if(r.content.length>0){r.content.push(...t);return}for(;r.container;){const s=r.container.content.indexOf(r);if(s>0){r.container.content.splice(s,0,...t),i=!0;break}r=r.container}i||this.rootNode.content.unshift(...t)}construct(e){const t=this.current;typeof e.$type=="string"&&(this.current.astNode=e),e.$cstNode=t;const r=this.nodeStack.pop();(r==null?void 0:r.content.length)===0&&this.removeNode(r)}}class qc{get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,t;const r=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)=="string"?this._astNode:(t=this.container)===null||t===void 0?void 0:t.astNode;if(!r)throw new Error("This node has no associated AST element");return r}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}}class Ms extends qc{get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,t,r,i,s=!1){super(),this._hidden=s,this._offset=e,this._tokenType=i,this._length=t,this._range=r}}class ma extends qc{constructor(){super(...arguments),this.content=new ga(this)}get children(){return this.content}get offset(){var e,t;return(t=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&t!==void 0?t:0}get length(){return this.end-this.offset}get end(){var e,t;return(t=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&t!==void 0?t:0}get range(){const e=this.firstNonHiddenNode,t=this.lastNonHiddenNode;if(e&&t){if(this._rangeCache===void 0){const{range:r}=e,{range:i}=t;this._rangeCache={start:r.start,end:i.end.line<r.start.line?r.start:i.end}}return this._rangeCache}else return{start:P.create(0,0),end:P.create(0,0)}}get firstNonHiddenNode(){for(const e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){const t=this.content[e];if(!t.hidden)return t}return this.content[this.content.length-1]}}class ga extends Array{constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,ga.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,t,...r){return this.addParents(r),super.splice(e,t,...r)}addParents(e){for(const t of e)t.container=this.parent}}class Yc extends ma{get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=e??""}}const Ds=Symbol("Datatype");function Hi(n){return n.$type===Ds}const ll="​",Xc=n=>n.endsWith(ll)?n:n+ll;class Jc{constructor(e){this._unorderedGroups=new Map,this.allRules=new Map,this.lexer=e.parser.Lexer;const t=this.lexer.definition,r=e.LanguageMetaData.mode==="production";this.wrapper=new Rm(t,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:r,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,t){this.wrapper.wrapOr(e,t)}optional(e,t){this.wrapper.wrapOption(e,t)}many(e,t){this.wrapper.wrapMany(e,t)}atLeastOne(e,t){this.wrapper.wrapAtLeastOne(e,t)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}}class mm extends Jc{get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new pm,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,t){const r=this.computeRuleType(e),i=this.wrapper.DEFINE_RULE(Xc(e.name),this.startImplementation(r,t).bind(this));return this.allRules.set(e.name,i),e.entry&&(this.mainRule=i),i}computeRuleType(e){if(!e.fragment){if(nc(e))return Ds;{const t=ta(e);return t??e.name}}}parse(e,t={}){this.nodeBuilder.buildRootNode(e);const r=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=r.tokens;const i=t.rule?this.allRules.get(t.rule):this.mainRule;if(!i)throw new Error(t.rule?`No rule found with name '${t.rule}'`:"No main rule available.");const s=i.call(this.wrapper,{});return this.nodeBuilder.addHiddenNodes(r.hidden),this.unorderedGroups.clear(),this.lexerResult=void 0,{value:s,lexerErrors:r.errors,lexerReport:r.report,parserErrors:this.wrapper.errors}}startImplementation(e,t){return r=>{const i=!this.isRecording()&&e!==void 0;if(i){const a={$type:e};this.stack.push(a),e===Ds&&(a.value="")}let s;try{s=t(r)}catch{s=void 0}return s===void 0&&i&&(s=this.construct()),s}}extractHiddenTokens(e){const t=this.lexerResult.hidden;if(!t.length)return[];const r=e.startOffset;for(let i=0;i<t.length;i++)if(t[i].startOffset>r)return t.splice(0,i);return t.splice(0,t.length)}consume(e,t,r){const i=this.wrapper.wrapConsume(e,t);if(!this.isRecording()&&this.isValidToken(i)){const s=this.extractHiddenTokens(i);this.nodeBuilder.addHiddenNodes(s);const a=this.nodeBuilder.buildLeafNode(i,r),{assignment:o,isCrossRef:l}=this.getAssignment(r),c=this.current;if(o){const u=gt(r)?i.image:this.converter.convert(i.image,a);this.assign(o.operator,o.feature,u,a,l)}else if(Hi(c)){let u=i.image;gt(r)||(u=this.converter.convert(u,a).toString()),c.value+=u}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset=="number"&&!isNaN(e.endOffset)}subrule(e,t,r,i,s){let a;!this.isRecording()&&!r&&(a=this.nodeBuilder.buildCompositeNode(i));const o=this.wrapper.wrapSubrule(e,t,s);!this.isRecording()&&a&&a.length>0&&this.performSubruleAssignment(o,i,a)}performSubruleAssignment(e,t,r){const{assignment:i,isCrossRef:s}=this.getAssignment(t);if(i)this.assign(i.operator,i.feature,e,r,s);else if(!i){const a=this.current;if(Hi(a))a.value+=e.toString();else if(typeof e=="object"&&e){const l=this.assignWithoutOverride(e,a);this.stack.pop(),this.stack.push(l)}}}action(e,t){if(!this.isRecording()){let r=this.current;if(t.feature&&t.operator){r=this.construct(),this.nodeBuilder.removeNode(r.$cstNode),this.nodeBuilder.buildCompositeNode(t).content.push(r.$cstNode);const s={$type:e};this.stack.push(s),this.assign(t.operator,t.feature,r,r.$cstNode,!1)}else r.$type=e}}construct(){if(this.isRecording())return;const e=this.current;return qd(e),this.nodeBuilder.construct(e),this.stack.pop(),Hi(e)?this.converter.convert(e.value,e.$cstNode):(Yd(this.astReflection,e),e)}getAssignment(e){if(!this.assignmentMap.has(e)){const t=yi(e,mt);this.assignmentMap.set(e,{assignment:t,isCrossRef:t?Js(t.terminal):!1})}return this.assignmentMap.get(e)}assign(e,t,r,i,s){const a=this.current;let o;switch(s&&typeof r=="string"?o=this.linker.buildReference(a,t,i,r):o=r,e){case"=":{a[t]=o;break}case"?=":{a[t]=!0;break}case"+=":Array.isArray(a[t])||(a[t]=[]),a[t].push(o)}}assignWithoutOverride(e,t){for(const[i,s]of Object.entries(t)){const a=e[i];a===void 0?e[i]=s:Array.isArray(a)&&Array.isArray(s)&&(s.push(...a),e[i]=s)}const r=e.$cstNode;return r&&(r.astNode=void 0,e.$cstNode=void 0),e}get definitionErrors(){return this.wrapper.definitionErrors}}class gm{buildMismatchTokenMessage(e){return Nt.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return Nt.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return Nt.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return Nt.buildEarlyExitMessage(e)}}class Qc extends gm{buildMismatchTokenMessage({expected:e,actual:t}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${t.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}}class ym extends Jc{constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();const t=this.lexer.tokenize(e,{mode:"partial"});return this.tokens=t.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,t){const r=this.wrapper.DEFINE_RULE(Xc(e.name),this.startImplementation(t).bind(this));return this.allRules.set(e.name,r),e.entry&&(this.mainRule=r),r}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return t=>{const r=this.keepStackSize();try{e(t)}finally{this.resetStackSize(r)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){const e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,t,r){this.wrapper.wrapConsume(e,t),this.isRecording()||(this.lastElementStack=[...this.elementStack,r],this.nextTokenIndex=this.currIdx+1)}subrule(e,t,r,i,s){this.before(i),this.wrapper.wrapSubrule(e,t,s),this.after(i)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){const t=this.elementStack.lastIndexOf(e);t>=0&&this.elementStack.splice(t)}}get currIdx(){return this.wrapper.currIdx}}const Tm={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new Qc};class Rm extends xp{constructor(e,t){const r=t&&"maxLookahead"in t;super(e,Object.assign(Object.assign(Object.assign({},Tm),{lookaheadStrategy:r?new ca({maxLookahead:t.maxLookahead}):new zp({logging:t.skipValidations?()=>{}:void 0})}),t))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,t){return this.RULE(e,t)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,t){return this.consume(e,t)}wrapSubrule(e,t,r){return this.subrule(e,t,{ARGS:[r]})}wrapOr(e,t){this.or(e,t)}wrapOption(e,t){this.option(e,t)}wrapMany(e,t){this.many(e,t)}wrapAtLeastOne(e,t){this.atLeastOne(e,t)}}function Zc(n,e,t){return vm({parser:e,tokens:t,ruleNames:new Map},n),e}function vm(n,e){const t=Jl(e,!1),r=Z(e.rules).filter(we).filter(i=>t.has(i));for(const i of r){const s=Object.assign(Object.assign({},n),{consume:1,optional:1,subrule:1,many:1,or:1});n.parser.rule(i,Rt(s,i.definition))}}function Rt(n,e,t=!1){let r;if(gt(e))r=Im(n,e);else if(gi(e))r=Am(n,e);else if(mt(e))r=Rt(n,e.terminal);else if(Js(e))r=eu(n,e);else if(yt(e))r=Em(n,e);else if(jl(e))r=km(n,e);else if(Hl(e))r=Sm(n,e);else if(Qs(e))r=xm(n,e);else if(Ud(e)){const i=n.consume++;r=()=>n.parser.consume(i,nt,e)}else throw new Bl(e.$cstNode,`Unexpected element type: ${e.$type}`);return tu(n,t?void 0:li(e),r,e.cardinality)}function Am(n,e){const t=na(e);return()=>n.parser.action(t,e)}function Em(n,e){const t=e.rule.ref;if(we(t)){const r=n.subrule++,i=t.fragment,s=e.arguments.length>0?$m(t,e.arguments):()=>({});return a=>n.parser.subrule(r,nu(n,t),i,e,s(a))}else if(Et(t)){const r=n.consume++,i=Fs(n,t.name);return()=>n.parser.consume(r,i,e)}else if(t)tr();else throw new Bl(e.$cstNode,`Undefined rule: ${e.rule.$refText}`)}function $m(n,e){const t=e.map(r=>ze(r.value));return r=>{const i={};for(let s=0;s<t.length;s++){const a=n.parameters[s],o=t[s];i[a.name]=o(r)}return i}}function ze(n){if(Od(n)){const e=ze(n.left),t=ze(n.right);return r=>e(r)||t(r)}else if(bd(n)){const e=ze(n.left),t=ze(n.right);return r=>e(r)&&t(r)}else if(Pd(n)){const e=ze(n.value);return t=>!e(t)}else if(Md(n)){const e=n.parameter.ref.name;return t=>t!==void 0&&t[e]===!0}else if(Ld(n)){const e=!!n.true;return()=>e}tr()}function km(n,e){if(e.elements.length===1)return Rt(n,e.elements[0]);{const t=[];for(const i of e.elements){const s={ALT:Rt(n,i,!0)},a=li(i);a&&(s.GATE=ze(a)),t.push(s)}const r=n.or++;return i=>n.parser.alternatives(r,t.map(s=>{const a={ALT:()=>s.ALT(i)},o=s.GATE;return o&&(a.GATE=()=>o(i)),a}))}}function Sm(n,e){if(e.elements.length===1)return Rt(n,e.elements[0]);const t=[];for(const o of e.elements){const l={ALT:Rt(n,o,!0)},c=li(o);c&&(l.GATE=ze(c)),t.push(l)}const r=n.or++,i=(o,l)=>{const c=l.getRuleStack().join("-");return`uGroup_${o}_${c}`},s=o=>n.parser.alternatives(r,t.map((l,c)=>{const u={ALT:()=>!0},d=n.parser;u.ALT=()=>{if(l.ALT(o),!d.isRecording()){const f=i(r,d);d.unorderedGroups.get(f)||d.unorderedGroups.set(f,[]);const m=d.unorderedGroups.get(f);typeof(m==null?void 0:m[c])>"u"&&(m[c]=!0)}};const h=l.GATE;return h?u.GATE=()=>h(o):u.GATE=()=>{const f=d.unorderedGroups.get(i(r,d));return!(f!=null&&f[c])},u})),a=tu(n,li(e),s,"*");return o=>{a(o),n.parser.isRecording()||n.parser.unorderedGroups.delete(i(r,n.parser))}}function xm(n,e){const t=e.elements.map(r=>Rt(n,r));return r=>t.forEach(i=>i(r))}function li(n){if(Qs(n))return n.guardCondition}function eu(n,e,t=e.terminal){if(t)if(yt(t)&&we(t.rule.ref)){const r=t.rule.ref,i=n.subrule++;return s=>n.parser.subrule(i,nu(n,r),!1,e,s)}else if(yt(t)&&Et(t.rule.ref)){const r=n.consume++,i=Fs(n,t.rule.ref.name);return()=>n.parser.consume(r,i,e)}else if(gt(t)){const r=n.consume++,i=Fs(n,t.value);return()=>n.parser.consume(r,i,e)}else throw new Error("Could not build cross reference parser");else{if(!e.type.ref)throw new Error("Could not resolve reference to type: "+e.type.$refText);const r=ec(e.type.ref),i=r==null?void 0:r.terminal;if(!i)throw new Error("Could not find name assignment for type: "+na(e.type.ref));return eu(n,e,i)}}function Im(n,e){const t=n.consume++,r=n.tokens[e.value];if(!r)throw new Error("Could not find token for keyword: "+e.value);return()=>n.parser.consume(t,r,e)}function tu(n,e,t,r){const i=e&&ze(e);if(!r)if(i){const s=n.or++;return a=>n.parser.alternatives(s,[{ALT:()=>t(a),GATE:()=>i(a)},{ALT:no(),GATE:()=>!i(a)}])}else return t;if(r==="*"){const s=n.many++;return a=>n.parser.many(s,{DEF:()=>t(a),GATE:i?()=>i(a):void 0})}else if(r==="+"){const s=n.many++;if(i){const a=n.or++;return o=>n.parser.alternatives(a,[{ALT:()=>n.parser.atLeastOne(s,{DEF:()=>t(o)}),GATE:()=>i(o)},{ALT:no(),GATE:()=>!i(o)}])}else return a=>n.parser.atLeastOne(s,{DEF:()=>t(a)})}else if(r==="?"){const s=n.optional++;return a=>n.parser.optional(s,{DEF:()=>t(a),GATE:i?()=>i(a):void 0})}else tr()}function nu(n,e){const t=Cm(n,e),r=n.parser.getRule(t);if(!r)throw new Error(`Rule "${t}" not found."`);return r}function Cm(n,e){if(we(e))return e.name;if(n.ruleNames.has(e))return n.ruleNames.get(e);{let t=e,r=t.$container,i=e.$type;for(;!we(r);)(Qs(r)||jl(r)||Hl(r))&&(i=r.elements.indexOf(t).toString()+":"+i),t=r,r=r.$container;return i=r.name+":"+i,n.ruleNames.set(e,i),i}}function Fs(n,e){const t=n.tokens[e];if(!t)throw new Error(`Token "${e}" not found."`);return t}function Nm(n){const e=n.Grammar,t=n.parser.Lexer,r=new ym(n);return Zc(e,r,t.definition),r.finalize(),r}function wm(n){const e=_m(n);return e.finalize(),e}function _m(n){const e=n.Grammar,t=n.parser.Lexer,r=new mm(n);return Zc(e,r,t.definition)}class ru{constructor(){this.diagnostics=[]}buildTokens(e,t){const r=Z(Jl(e,!1)),i=this.buildTerminalTokens(r),s=this.buildKeywordTokens(r,i,t);return i.forEach(a=>{const o=a.PATTERN;typeof o=="object"&&o&&"test"in o&&ds(o)?s.unshift(a):s.push(a)}),s}flushLexingReport(e){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){const e=[...this.diagnostics];return this.diagnostics=[],e}buildTerminalTokens(e){return e.filter(Et).filter(t=>!t.fragment).map(t=>this.buildTerminalToken(t)).toArray()}buildTerminalToken(e){const t=ra(e),r=this.requiresCustomPattern(t)?this.regexPatternFunction(t):t,i={name:e.name,PATTERN:r};return typeof r=="function"&&(i.LINE_BREAKS=!0),e.hidden&&(i.GROUP=ds(t)?fe.SKIPPED:"hidden"),i}requiresCustomPattern(e){return e.flags.includes("u")||e.flags.includes("s")?!0:!!(e.source.includes("?<=")||e.source.includes("?<!"))}regexPatternFunction(e){const t=new RegExp(e,e.flags+"y");return(r,i)=>(t.lastIndex=i,t.exec(r))}buildKeywordTokens(e,t,r){return e.filter(we).flatMap(i=>nr(i).filter(gt)).distinct(i=>i.value).toArray().sort((i,s)=>s.value.length-i.value.length).map(i=>this.buildKeywordToken(i,t,!!(r!=null&&r.caseInsensitive)))}buildKeywordToken(e,t,r){const i=this.buildKeywordPattern(e,r),s={name:e.value,PATTERN:i,LONGER_ALT:this.findLongerAlt(e,t)};return typeof i=="function"&&(s.LINE_BREAKS=!0),s}buildKeywordPattern(e,t){return t?new RegExp(sf(e.value)):e.value}findLongerAlt(e,t){return t.reduce((r,i)=>{const s=i==null?void 0:i.PATTERN;return s!=null&&s.source&&af("^"+s.source+"$",e.value)&&r.push(i),r},[])}}class iu{convert(e,t){let r=t.grammarSource;if(Js(r)&&(r=uf(r)),yt(r)){const i=r.rule.ref;if(!i)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(i,e,t)}return e}runConverter(e,t,r){var i;switch(e.name.toUpperCase()){case"INT":return We.convertInt(t);case"STRING":return We.convertString(t);case"ID":return We.convertID(t)}switch((i=yf(e))===null||i===void 0?void 0:i.toLowerCase()){case"number":return We.convertNumber(t);case"boolean":return We.convertBoolean(t);case"bigint":return We.convertBigint(t);case"date":return We.convertDate(t);default:return t}}}var We;(function(n){function e(c){let u="";for(let d=1;d<c.length-1;d++){const h=c.charAt(d);if(h==="\\"){const f=c.charAt(++d);u+=t(f)}else u+=h}return u}n.convertString=e;function t(c){switch(c){case"b":return"\b";case"f":return"\f";case"n":return`
`;case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return c}}function r(c){return c.charAt(0)==="^"?c.substring(1):c}n.convertID=r;function i(c){return parseInt(c)}n.convertInt=i;function s(c){return BigInt(c)}n.convertBigint=s;function a(c){return new Date(c)}n.convertDate=a;function o(c){return Number(c)}n.convertNumber=o;function l(c){return c.toLowerCase()==="true"}n.convertBoolean=l})(We||(We={}));var Qn={},Ii={};Object.defineProperty(Ii,"__esModule",{value:!0});let Gs;function Us(){if(Gs===void 0)throw new Error("No runtime abstraction layer installed");return Gs}(function(n){function e(t){if(t===void 0)throw new Error("No runtime abstraction layer provided");Gs=t}n.install=e})(Us||(Us={}));Ii.default=Us;var se={};Object.defineProperty(se,"__esModule",{value:!0});se.stringArray=se.array=se.func=se.error=se.number=se.string=se.boolean=void 0;function Lm(n){return n===!0||n===!1}se.boolean=Lm;function su(n){return typeof n=="string"||n instanceof String}se.string=su;function bm(n){return typeof n=="number"||n instanceof Number}se.number=bm;function Om(n){return n instanceof Error}se.error=Om;function Pm(n){return typeof n=="function"}se.func=Pm;function au(n){return Array.isArray(n)}se.array=au;function Mm(n){return au(n)&&n.every(e=>su(e))}se.stringArray=Mm;var nn={};Object.defineProperty(nn,"__esModule",{value:!0});var ou=nn.Emitter=nn.Event=void 0;const Dm=Ii;var cl;(function(n){const e={dispose(){}};n.None=function(){return e}})(cl||(nn.Event=cl={}));class Fm{add(e,t=null,r){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(r)&&r.push({dispose:()=>this.remove(e,t)})}remove(e,t=null){if(!this._callbacks)return;let r=!1;for(let i=0,s=this._callbacks.length;i<s;i++)if(this._callbacks[i]===e)if(this._contexts[i]===t){this._callbacks.splice(i,1),this._contexts.splice(i,1);return}else r=!0;if(r)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];const t=[],r=this._callbacks.slice(0),i=this._contexts.slice(0);for(let s=0,a=r.length;s<a;s++)try{t.push(r[s].apply(i[s],e))}catch(o){(0,Dm.default)().console.error(o)}return t}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}}class Ci{constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,r)=>{this._callbacks||(this._callbacks=new Fm),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t);const i={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,t),i.dispose=Ci._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(r)&&r.push(i),i}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}ou=nn.Emitter=Ci;Ci._noop=function(){};var V;Object.defineProperty(Qn,"__esModule",{value:!0});var ya=Qn.CancellationTokenSource=V=Qn.CancellationToken=void 0;const Gm=Ii,Um=se,Bs=nn;var ci;(function(n){n.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Bs.Event.None}),n.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Bs.Event.None});function e(t){const r=t;return r&&(r===n.None||r===n.Cancelled||Um.boolean(r.isCancellationRequested)&&!!r.onCancellationRequested)}n.is=e})(ci||(V=Qn.CancellationToken=ci={}));const Bm=Object.freeze(function(n,e){const t=(0,Gm.default)().timer.setTimeout(n.bind(e),0);return{dispose(){t.dispose()}}});class ul{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Bm:(this._emitter||(this._emitter=new Bs.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}class Vm{get token(){return this._token||(this._token=new ul),this._token}cancel(){this._token?this._token.cancel():this._token=ci.Cancelled}dispose(){this._token?this._token instanceof ul&&this._token.dispose():this._token=ci.None}}ya=Qn.CancellationTokenSource=Vm;function Km(){return new Promise(n=>{typeof setImmediate>"u"?setTimeout(n,0):setImmediate(n)})}let Mr=0,Wm=10;function jm(){return Mr=performance.now(),new ya}const ui=Symbol("OperationCancelled");function Ni(n){return n===ui}async function Ae(n){if(n===V.None)return;const e=performance.now();if(e-Mr>=Wm&&(Mr=e,await Km(),Mr=performance.now()),n.isCancellationRequested)throw ui}class Ta{constructor(){this.promise=new Promise((e,t)=>{this.resolve=r=>(e(r),this),this.reject=r=>(t(r),this)})}}class Zn{constructor(e,t,r,i){this._uri=e,this._languageId=t,this._version=r,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){for(const r of e)if(Zn.isIncremental(r)){const i=cu(r.range),s=this.offsetAt(i.start),a=this.offsetAt(i.end);this._content=this._content.substring(0,s)+r.text+this._content.substring(a,this._content.length);const o=Math.max(i.start.line,0),l=Math.max(i.end.line,0);let c=this._lineOffsets;const u=dl(r.text,!1,s);if(l-o===u.length)for(let h=0,f=u.length;h<f;h++)c[h+o+1]=u[h];else u.length<1e4?c.splice(o+1,l-o,...u):this._lineOffsets=c=c.slice(0,o+1).concat(u,c.slice(l+1));const d=r.text.length-(a-s);if(d!==0)for(let h=o+1+u.length,f=c.length;h<f;h++)c[h]=c[h]+d}else if(Zn.isFull(r))this._content=r.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=t}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=dl(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let r=0,i=t.length;if(i===0)return{line:0,character:e};for(;r<i;){const a=Math.floor((r+i)/2);t[a]>e?i=a:r=a+1}const s=r-1;return e=this.ensureBeforeEOL(e,t[s]),{line:s,character:e-t[s]}}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;const r=t[e.line];if(e.character<=0)return r;const i=e.line+1<t.length?t[e.line+1]:this._content.length,s=Math.min(r+e.character,i);return this.ensureBeforeEOL(s,r)}ensureBeforeEOL(e,t){for(;e>t&&lu(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range!==void 0&&(t.rangeLength===void 0||typeof t.rangeLength=="number")}static isFull(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range===void 0&&t.rangeLength===void 0}}var Vs;(function(n){function e(i,s,a,o){return new Zn(i,s,a,o)}n.create=e;function t(i,s,a){if(i instanceof Zn)return i.update(s,a),i;throw new Error("TextDocument.update: document must be created by TextDocument.create")}n.update=t;function r(i,s){const a=i.getText(),o=Ks(s.map(Hm),(u,d)=>{const h=u.range.start.line-d.range.start.line;return h===0?u.range.start.character-d.range.start.character:h});let l=0;const c=[];for(const u of o){const d=i.offsetAt(u.range.start);if(d<l)throw new Error("Overlapping edit");d>l&&c.push(a.substring(l,d)),u.newText.length&&c.push(u.newText),l=i.offsetAt(u.range.end)}return c.push(a.substr(l)),c.join("")}n.applyEdits=r})(Vs||(Vs={}));function Ks(n,e){if(n.length<=1)return n;const t=n.length/2|0,r=n.slice(0,t),i=n.slice(t);Ks(r,e),Ks(i,e);let s=0,a=0,o=0;for(;s<r.length&&a<i.length;)e(r[s],i[a])<=0?n[o++]=r[s++]:n[o++]=i[a++];for(;s<r.length;)n[o++]=r[s++];for(;a<i.length;)n[o++]=i[a++];return n}function dl(n,e,t=0){const r=e?[t]:[];for(let i=0;i<n.length;i++){const s=n.charCodeAt(i);lu(s)&&(s===13&&i+1<n.length&&n.charCodeAt(i+1)===10&&i++,r.push(t+i+1))}return r}function lu(n){return n===13||n===10}function cu(n){const e=n.start,t=n.end;return e.line>t.line||e.line===t.line&&e.character>t.character?{start:t,end:e}:n}function Hm(n){const e=cu(n.range);return e!==n.range?{newText:n.newText,range:e}:n}var uu;(()=>{var n={470:i=>{function s(l){if(typeof l!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(l))}function a(l,c){for(var u,d="",h=0,f=-1,m=0,g=0;g<=l.length;++g){if(g<l.length)u=l.charCodeAt(g);else{if(u===47)break;u=47}if(u===47){if(!(f===g-1||m===1))if(f!==g-1&&m===2){if(d.length<2||h!==2||d.charCodeAt(d.length-1)!==46||d.charCodeAt(d.length-2)!==46){if(d.length>2){var A=d.lastIndexOf("/");if(A!==d.length-1){A===-1?(d="",h=0):h=(d=d.slice(0,A)).length-1-d.lastIndexOf("/"),f=g,m=0;continue}}else if(d.length===2||d.length===1){d="",h=0,f=g,m=0;continue}}c&&(d.length>0?d+="/..":d="..",h=2)}else d.length>0?d+="/"+l.slice(f+1,g):d=l.slice(f+1,g),h=g-f-1;f=g,m=0}else u===46&&m!==-1?++m:m=-1}return d}var o={resolve:function(){for(var l,c="",u=!1,d=arguments.length-1;d>=-1&&!u;d--){var h;d>=0?h=arguments[d]:(l===void 0&&(l=process.cwd()),h=l),s(h),h.length!==0&&(c=h+"/"+c,u=h.charCodeAt(0)===47)}return c=a(c,!u),u?c.length>0?"/"+c:"/":c.length>0?c:"."},normalize:function(l){if(s(l),l.length===0)return".";var c=l.charCodeAt(0)===47,u=l.charCodeAt(l.length-1)===47;return(l=a(l,!c)).length!==0||c||(l="."),l.length>0&&u&&(l+="/"),c?"/"+l:l},isAbsolute:function(l){return s(l),l.length>0&&l.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var l,c=0;c<arguments.length;++c){var u=arguments[c];s(u),u.length>0&&(l===void 0?l=u:l+="/"+u)}return l===void 0?".":o.normalize(l)},relative:function(l,c){if(s(l),s(c),l===c||(l=o.resolve(l))===(c=o.resolve(c)))return"";for(var u=1;u<l.length&&l.charCodeAt(u)===47;++u);for(var d=l.length,h=d-u,f=1;f<c.length&&c.charCodeAt(f)===47;++f);for(var m=c.length-f,g=h<m?h:m,A=-1,y=0;y<=g;++y){if(y===g){if(m>g){if(c.charCodeAt(f+y)===47)return c.slice(f+y+1);if(y===0)return c.slice(f+y)}else h>g&&(l.charCodeAt(u+y)===47?A=y:y===0&&(A=0));break}var E=l.charCodeAt(u+y);if(E!==c.charCodeAt(f+y))break;E===47&&(A=y)}var R="";for(y=u+A+1;y<=d;++y)y!==d&&l.charCodeAt(y)!==47||(R.length===0?R+="..":R+="/..");return R.length>0?R+c.slice(f+A):(f+=A,c.charCodeAt(f)===47&&++f,c.slice(f))},_makeLong:function(l){return l},dirname:function(l){if(s(l),l.length===0)return".";for(var c=l.charCodeAt(0),u=c===47,d=-1,h=!0,f=l.length-1;f>=1;--f)if((c=l.charCodeAt(f))===47){if(!h){d=f;break}}else h=!1;return d===-1?u?"/":".":u&&d===1?"//":l.slice(0,d)},basename:function(l,c){if(c!==void 0&&typeof c!="string")throw new TypeError('"ext" argument must be a string');s(l);var u,d=0,h=-1,f=!0;if(c!==void 0&&c.length>0&&c.length<=l.length){if(c.length===l.length&&c===l)return"";var m=c.length-1,g=-1;for(u=l.length-1;u>=0;--u){var A=l.charCodeAt(u);if(A===47){if(!f){d=u+1;break}}else g===-1&&(f=!1,g=u+1),m>=0&&(A===c.charCodeAt(m)?--m==-1&&(h=u):(m=-1,h=g))}return d===h?h=g:h===-1&&(h=l.length),l.slice(d,h)}for(u=l.length-1;u>=0;--u)if(l.charCodeAt(u)===47){if(!f){d=u+1;break}}else h===-1&&(f=!1,h=u+1);return h===-1?"":l.slice(d,h)},extname:function(l){s(l);for(var c=-1,u=0,d=-1,h=!0,f=0,m=l.length-1;m>=0;--m){var g=l.charCodeAt(m);if(g!==47)d===-1&&(h=!1,d=m+1),g===46?c===-1?c=m:f!==1&&(f=1):c!==-1&&(f=-1);else if(!h){u=m+1;break}}return c===-1||d===-1||f===0||f===1&&c===d-1&&c===u+1?"":l.slice(c,d)},format:function(l){if(l===null||typeof l!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof l);return function(c,u){var d=u.dir||u.root,h=u.base||(u.name||"")+(u.ext||"");return d?d===u.root?d+h:d+"/"+h:h}(0,l)},parse:function(l){s(l);var c={root:"",dir:"",base:"",ext:"",name:""};if(l.length===0)return c;var u,d=l.charCodeAt(0),h=d===47;h?(c.root="/",u=1):u=0;for(var f=-1,m=0,g=-1,A=!0,y=l.length-1,E=0;y>=u;--y)if((d=l.charCodeAt(y))!==47)g===-1&&(A=!1,g=y+1),d===46?f===-1?f=y:E!==1&&(E=1):f!==-1&&(E=-1);else if(!A){m=y+1;break}return f===-1||g===-1||E===0||E===1&&f===g-1&&f===m+1?g!==-1&&(c.base=c.name=m===0&&h?l.slice(1,g):l.slice(m,g)):(m===0&&h?(c.name=l.slice(1,f),c.base=l.slice(1,g)):(c.name=l.slice(m,f),c.base=l.slice(m,g)),c.ext=l.slice(f,g)),m>0?c.dir=l.slice(0,m-1):h&&(c.dir="/"),c},sep:"/",delimiter:":",win32:null,posix:null};o.posix=o,i.exports=o}},e={};function t(i){var s=e[i];if(s!==void 0)return s.exports;var a=e[i]={exports:{}};return n[i](a,a.exports,t),a.exports}t.d=(i,s)=>{for(var a in s)t.o(s,a)&&!t.o(i,a)&&Object.defineProperty(i,a,{enumerable:!0,get:s[a]})},t.o=(i,s)=>Object.prototype.hasOwnProperty.call(i,s),t.r=i=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var r={};(()=>{let i;t.r(r),t.d(r,{URI:()=>h,Utils:()=>Ie}),typeof process=="object"?i=process.platform==="win32":typeof navigator=="object"&&(i=navigator.userAgent.indexOf("Windows")>=0);const s=/^\w[\w\d+.-]*$/,a=/^\//,o=/^\/\//;function l(k,T){if(!k.scheme&&T)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${k.authority}", path: "${k.path}", query: "${k.query}", fragment: "${k.fragment}"}`);if(k.scheme&&!s.test(k.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(k.path){if(k.authority){if(!a.test(k.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(o.test(k.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}const c="",u="/",d=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class h{constructor(T,$,x,b,L,_=!1){Ze(this,"scheme");Ze(this,"authority");Ze(this,"path");Ze(this,"query");Ze(this,"fragment");typeof T=="object"?(this.scheme=T.scheme||c,this.authority=T.authority||c,this.path=T.path||c,this.query=T.query||c,this.fragment=T.fragment||c):(this.scheme=function(Te,q){return Te||q?Te:"file"}(T,_),this.authority=$||c,this.path=function(Te,q){switch(Te){case"https":case"http":case"file":q?q[0]!==u&&(q=u+q):q=u}return q}(this.scheme,x||c),this.query=b||c,this.fragment=L||c,l(this,_))}static isUri(T){return T instanceof h||!!T&&typeof T.authority=="string"&&typeof T.fragment=="string"&&typeof T.path=="string"&&typeof T.query=="string"&&typeof T.scheme=="string"&&typeof T.fsPath=="string"&&typeof T.with=="function"&&typeof T.toString=="function"}get fsPath(){return E(this)}with(T){if(!T)return this;let{scheme:$,authority:x,path:b,query:L,fragment:_}=T;return $===void 0?$=this.scheme:$===null&&($=c),x===void 0?x=this.authority:x===null&&(x=c),b===void 0?b=this.path:b===null&&(b=c),L===void 0?L=this.query:L===null&&(L=c),_===void 0?_=this.fragment:_===null&&(_=c),$===this.scheme&&x===this.authority&&b===this.path&&L===this.query&&_===this.fragment?this:new m($,x,b,L,_)}static parse(T,$=!1){const x=d.exec(T);return x?new m(x[2]||c,re(x[4]||c),re(x[5]||c),re(x[7]||c),re(x[9]||c),$):new m(c,c,c,c,c)}static file(T){let $=c;if(i&&(T=T.replace(/\\/g,u)),T[0]===u&&T[1]===u){const x=T.indexOf(u,2);x===-1?($=T.substring(2),T=u):($=T.substring(2,x),T=T.substring(x)||u)}return new m("file",$,T,c,c)}static from(T){const $=new m(T.scheme,T.authority,T.path,T.query,T.fragment);return l($,!0),$}toString(T=!1){return R(this,T)}toJSON(){return this}static revive(T){if(T){if(T instanceof h)return T;{const $=new m(T);return $._formatted=T.external,$._fsPath=T._sep===f?T.fsPath:null,$}}return T}}const f=i?1:void 0;class m extends h{constructor(){super(...arguments);Ze(this,"_formatted",null);Ze(this,"_fsPath",null)}get fsPath(){return this._fsPath||(this._fsPath=E(this)),this._fsPath}toString($=!1){return $?R(this,!0):(this._formatted||(this._formatted=R(this,!1)),this._formatted)}toJSON(){const $={$mid:1};return this._fsPath&&($.fsPath=this._fsPath,$._sep=f),this._formatted&&($.external=this._formatted),this.path&&($.path=this.path),this.scheme&&($.scheme=this.scheme),this.authority&&($.authority=this.authority),this.query&&($.query=this.query),this.fragment&&($.fragment=this.fragment),$}}const g={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function A(k,T,$){let x,b=-1;for(let L=0;L<k.length;L++){const _=k.charCodeAt(L);if(_>=97&&_<=122||_>=65&&_<=90||_>=48&&_<=57||_===45||_===46||_===95||_===126||T&&_===47||$&&_===91||$&&_===93||$&&_===58)b!==-1&&(x+=encodeURIComponent(k.substring(b,L)),b=-1),x!==void 0&&(x+=k.charAt(L));else{x===void 0&&(x=k.substr(0,L));const Te=g[_];Te!==void 0?(b!==-1&&(x+=encodeURIComponent(k.substring(b,L)),b=-1),x+=Te):b===-1&&(b=L)}}return b!==-1&&(x+=encodeURIComponent(k.substring(b))),x!==void 0?x:k}function y(k){let T;for(let $=0;$<k.length;$++){const x=k.charCodeAt($);x===35||x===63?(T===void 0&&(T=k.substr(0,$)),T+=g[x]):T!==void 0&&(T+=k[$])}return T!==void 0?T:k}function E(k,T){let $;return $=k.authority&&k.path.length>1&&k.scheme==="file"?`//${k.authority}${k.path}`:k.path.charCodeAt(0)===47&&(k.path.charCodeAt(1)>=65&&k.path.charCodeAt(1)<=90||k.path.charCodeAt(1)>=97&&k.path.charCodeAt(1)<=122)&&k.path.charCodeAt(2)===58?k.path[1].toLowerCase()+k.path.substr(2):k.path,i&&($=$.replace(/\//g,"\\")),$}function R(k,T){const $=T?y:A;let x="",{scheme:b,authority:L,path:_,query:Te,fragment:q}=k;if(b&&(x+=b,x+=":"),(L||b==="file")&&(x+=u,x+=u),L){let K=L.indexOf("@");if(K!==-1){const dt=L.substr(0,K);L=L.substr(K+1),K=dt.lastIndexOf(":"),K===-1?x+=$(dt,!1,!1):(x+=$(dt.substr(0,K),!1,!1),x+=":",x+=$(dt.substr(K+1),!1,!0)),x+="@"}L=L.toLowerCase(),K=L.lastIndexOf(":"),K===-1?x+=$(L,!1,!0):(x+=$(L.substr(0,K),!1,!0),x+=L.substr(K))}if(_){if(_.length>=3&&_.charCodeAt(0)===47&&_.charCodeAt(2)===58){const K=_.charCodeAt(1);K>=65&&K<=90&&(_=`/${String.fromCharCode(K+32)}:${_.substr(3)}`)}else if(_.length>=2&&_.charCodeAt(1)===58){const K=_.charCodeAt(0);K>=65&&K<=90&&(_=`${String.fromCharCode(K+32)}:${_.substr(2)}`)}x+=$(_,!0,!1)}return Te&&(x+="?",x+=$(Te,!1,!1)),q&&(x+="#",x+=T?q:A(q,!1,!1)),x}function I(k){try{return decodeURIComponent(k)}catch{return k.length>3?k.substr(0,3)+I(k.substr(3)):k}}const F=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function re(k){return k.match(F)?k.replace(F,T=>I(T)):k}var _e=t(470);const ye=_e.posix||_e,Fe="/";var Ie;(function(k){k.joinPath=function(T,...$){return T.with({path:ye.join(T.path,...$)})},k.resolvePath=function(T,...$){let x=T.path,b=!1;x[0]!==Fe&&(x=Fe+x,b=!0);let L=ye.resolve(x,...$);return b&&L[0]===Fe&&!T.authority&&(L=L.substring(1)),T.with({path:L})},k.dirname=function(T){if(T.path.length===0||T.path===Fe)return T;let $=ye.dirname(T.path);return $.length===1&&$.charCodeAt(0)===46&&($=""),T.with({path:$})},k.basename=function(T){return ye.basename(T.path)},k.extname=function(T){return ye.extname(T.path)}})(Ie||(Ie={}))})(),uu=r})();const{URI:vt,Utils:dn}=uu;var it;(function(n){n.basename=dn.basename,n.dirname=dn.dirname,n.extname=dn.extname,n.joinPath=dn.joinPath,n.resolvePath=dn.resolvePath;function e(i,s){return(i==null?void 0:i.toString())===(s==null?void 0:s.toString())}n.equals=e;function t(i,s){const a=typeof i=="string"?i:i.path,o=typeof s=="string"?s:s.path,l=a.split("/").filter(f=>f.length>0),c=o.split("/").filter(f=>f.length>0);let u=0;for(;u<l.length&&l[u]===c[u];u++);const d="../".repeat(l.length-u),h=c.slice(u).join("/");return d+h}n.relative=t;function r(i){return vt.parse(i.toString()).toString()}n.normalize=r})(it||(it={}));var U;(function(n){n[n.Changed=0]="Changed",n[n.Parsed=1]="Parsed",n[n.IndexedContent=2]="IndexedContent",n[n.ComputedScopes=3]="ComputedScopes",n[n.Linked=4]="Linked",n[n.IndexedReferences=5]="IndexedReferences",n[n.Validated=6]="Validated"})(U||(U={}));class zm{constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,t=V.None){const r=await this.fileSystemProvider.readFile(e);return this.createAsync(e,r,t)}fromTextDocument(e,t,r){return t=t??vt.parse(e.uri),V.is(r)?this.createAsync(t,e,r):this.create(t,e,r)}fromString(e,t,r){return V.is(r)?this.createAsync(t,e,r):this.create(t,e,r)}fromModel(e,t){return this.create(t,{$model:e})}create(e,t,r){if(typeof t=="string"){const i=this.parse(e,t,r);return this.createLangiumDocument(i,e,void 0,t)}else if("$model"in t){const i={value:t.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(i,e)}else{const i=this.parse(e,t.getText(),r);return this.createLangiumDocument(i,e,t)}}async createAsync(e,t,r){if(typeof t=="string"){const i=await this.parseAsync(e,t,r);return this.createLangiumDocument(i,e,void 0,t)}else{const i=await this.parseAsync(e,t.getText(),r);return this.createLangiumDocument(i,e,t)}}createLangiumDocument(e,t,r,i){let s;if(r)s={parseResult:e,uri:t,state:U.Parsed,references:[],textDocument:r};else{const a=this.createTextDocumentGetter(t,i);s={parseResult:e,uri:t,state:U.Parsed,references:[],get textDocument(){return a()}}}return e.value.$document=s,s}async update(e,t){var r,i;const s=(r=e.parseResult.value.$cstNode)===null||r===void 0?void 0:r.root.fullText,a=(i=this.textDocuments)===null||i===void 0?void 0:i.get(e.uri.toString()),o=a?a.getText():await this.fileSystemProvider.readFile(e.uri);if(a)Object.defineProperty(e,"textDocument",{value:a});else{const l=this.createTextDocumentGetter(e.uri,o);Object.defineProperty(e,"textDocument",{get:l})}return s!==o&&(e.parseResult=await this.parseAsync(e.uri,o,t),e.parseResult.value.$document=e),e.state=U.Parsed,e}parse(e,t,r){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(t,r)}parseAsync(e,t,r){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(t,r)}createTextDocumentGetter(e,t){const r=this.serviceRegistry;let i;return()=>i??(i=Vs.create(e.toString(),r.getServices(e).LanguageMetaData.languageId,0,t??""))}}class qm{constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.serviceRegistry=e.ServiceRegistry}get all(){return Z(this.documentMap.values())}addDocument(e){const t=e.uri.toString();if(this.documentMap.has(t))throw new Error(`A document with the URI '${t}' is already present.`);this.documentMap.set(t,e)}getDocument(e){const t=e.toString();return this.documentMap.get(t)}async getOrCreateDocument(e,t){let r=this.getDocument(e);return r||(r=await this.langiumDocumentFactory.fromUri(e,t),this.addDocument(r),r)}createDocument(e,t,r){if(r)return this.langiumDocumentFactory.fromString(t,e,r).then(i=>(this.addDocument(i),i));{const i=this.langiumDocumentFactory.fromString(t,e);return this.addDocument(i),i}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){const t=e.toString(),r=this.documentMap.get(t);return r&&(this.serviceRegistry.getServices(e).references.Linker.unlink(r),r.state=U.Changed,r.precomputedScopes=void 0,r.diagnostics=void 0),r}deleteDocument(e){const t=e.toString(),r=this.documentMap.get(t);return r&&(r.state=U.Changed,this.documentMap.delete(t)),r}}const zi=Symbol("ref_resolving");class Ym{constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,t=V.None){for(const r of wt(e.parseResult.value))await Ae(t),ql(r).forEach(i=>this.doLink(i,e))}doLink(e,t){var r;const i=e.reference;if(i._ref===void 0){i._ref=zi;try{const s=this.getCandidate(e);if(Nr(s))i._ref=s;else if(i._nodeDescription=s,this.langiumDocuments().hasDocument(s.documentUri)){const a=this.loadAstNode(s);i._ref=a??this.createLinkingError(e,s)}else i._ref=void 0}catch(s){console.error(`An error occurred while resolving reference to '${i.$refText}':`,s);const a=(r=s.message)!==null&&r!==void 0?r:String(s);i._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${i.$refText}': ${a}`})}t.references.push(i)}}unlink(e){for(const t of e.references)delete t._ref,delete t._nodeDescription;e.references=[]}getCandidate(e){const r=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return r??this.createLinkingError(e)}buildReference(e,t,r,i){const s=this,a={$refNode:r,$refText:i,get ref(){var o;if(ae(this._ref))return this._ref;if(Ed(this._nodeDescription)){const l=s.loadAstNode(this._nodeDescription);this._ref=l??s.createLinkingError({reference:a,container:e,property:t},this._nodeDescription)}else if(this._ref===void 0){this._ref=zi;const l=us(e).$document,c=s.getLinkedNode({reference:a,container:e,property:t});if(c.error&&l&&l.state<U.ComputedScopes)return this._ref=void 0;this._ref=(o=c.node)!==null&&o!==void 0?o:c.error,this._nodeDescription=c.descr,l==null||l.references.push(this)}else if(this._ref===zi)throw new Error(`Cyclic reference resolution detected: ${s.astNodeLocator.getAstNodePath(e)}/${t} (symbol '${i}')`);return ae(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return Nr(this._ref)?this._ref:void 0}};return a}getLinkedNode(e){var t;try{const r=this.getCandidate(e);if(Nr(r))return{error:r};const i=this.loadAstNode(r);return i?{node:i,descr:r}:{descr:r,error:this.createLinkingError(e,r)}}catch(r){console.error(`An error occurred while resolving reference to '${e.reference.$refText}':`,r);const i=(t=r.message)!==null&&t!==void 0?t:String(r);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${i}`})}}}loadAstNode(e){if(e.node)return e.node;const t=this.langiumDocuments().getDocument(e.documentUri);if(t)return this.astNodeLocator.getAstNode(t.parseResult.value,e.path)}createLinkingError(e,t){const r=us(e.container).$document;r&&r.state<U.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${r.uri}).`);const i=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${i} named '${e.reference.$refText}'.`,targetDescription:t})}}function Xm(n){return typeof n.name=="string"}class Jm{getName(e){if(Xm(e))return e.name}getNameNode(e){return Zl(e.$cstNode,"name")}}class Qm{constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){const t=mf(e),r=e.astNode;if(t&&r){const i=r[t.feature];if(Ue(i))return i.ref;if(Array.isArray(i)){for(const s of i)if(Ue(s)&&s.$refNode&&s.$refNode.offset<=e.offset&&s.$refNode.end>=e.end)return s.ref}}if(r){const i=this.nameProvider.getNameNode(r);if(i&&(i===e||Sd(e,i)))return r}}}findDeclarationNode(e){const t=this.findDeclaration(e);if(t!=null&&t.$cstNode){const r=this.nameProvider.getNameNode(t);return r??t.$cstNode}}findReferences(e,t){const r=[];if(t.includeDeclaration){const s=this.getReferenceToSelf(e);s&&r.push(s)}let i=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return t.documentUri&&(i=i.filter(s=>it.equals(s.sourceUri,t.documentUri))),r.push(...i),Z(r)}getReferenceToSelf(e){const t=this.nameProvider.getNameNode(e);if(t){const r=et(e),i=this.nodeLocator.getAstNodePath(e);return{sourceUri:r.uri,sourcePath:i,targetUri:r.uri,targetPath:i,segment:jr(t),local:!0}}}}class di{constructor(e){if(this.map=new Map,e)for(const[t,r]of e)this.add(t,r)}get size(){return os.sum(Z(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,t){if(t===void 0)return this.map.delete(e);{const r=this.map.get(e);if(r){const i=r.indexOf(t);if(i>=0)return r.length===1?this.map.delete(e):r.splice(i,1),!0}return!1}}get(e){var t;return(t=this.map.get(e))!==null&&t!==void 0?t:[]}has(e,t){if(t===void 0)return this.map.has(e);{const r=this.map.get(e);return r?r.indexOf(t)>=0:!1}}add(e,t){return this.map.has(e)?this.map.get(e).push(t):this.map.set(e,[t]),this}addAll(e,t){return this.map.has(e)?this.map.get(e).push(...t):this.map.set(e,Array.from(t)),this}forEach(e){this.map.forEach((t,r)=>t.forEach(i=>e(i,r,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return Z(this.map.entries()).flatMap(([e,t])=>t.map(r=>[e,r]))}keys(){return Z(this.map.keys())}values(){return Z(this.map.values()).flat()}entriesGroupedByKey(){return Z(this.map.entries())}}class fl{get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(const[t,r]of e)this.set(t,r)}clear(){this.map.clear(),this.inverse.clear()}set(e,t){return this.map.set(e,t),this.inverse.set(t,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){const t=this.map.get(e);return t!==void 0?(this.map.delete(e),this.inverse.delete(t),!0):!1}}class Zm{constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,t=V.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,t)}async computeExportsForNode(e,t,r=Zs,i=V.None){const s=[];this.exportNode(e,s,t);for(const a of r(e))await Ae(i),this.exportNode(a,s,t);return s}exportNode(e,t,r){const i=this.nameProvider.getName(e);i&&t.push(this.descriptions.createDescription(e,i,r))}async computeLocalScopes(e,t=V.None){const r=e.parseResult.value,i=new di;for(const s of nr(r))await Ae(t),this.processNode(s,e,i);return i}processNode(e,t,r){const i=e.$container;if(i){const s=this.nameProvider.getName(e);s&&r.add(i,this.descriptions.createDescription(e,s,t))}}}class hl{constructor(e,t,r){var i;this.elements=e,this.outerScope=t,this.caseInsensitive=(i=r==null?void 0:r.caseInsensitive)!==null&&i!==void 0?i:!1}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){const t=this.caseInsensitive?this.elements.find(r=>r.name.toLowerCase()===e.toLowerCase()):this.elements.find(r=>r.name===e);if(t)return t;if(this.outerScope)return this.outerScope.getElement(e)}}class eg{constructor(e,t,r){var i;this.elements=new Map,this.caseInsensitive=(i=r==null?void 0:r.caseInsensitive)!==null&&i!==void 0?i:!1;for(const s of e){const a=this.caseInsensitive?s.name.toLowerCase():s.name;this.elements.set(a,s)}this.outerScope=t}getElement(e){const t=this.caseInsensitive?e.toLowerCase():e,r=this.elements.get(t);if(r)return r;if(this.outerScope)return this.outerScope.getElement(e)}getAllElements(){let e=Z(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}}class du{constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}}class tg extends du{constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,t){this.throwIfDisposed(),this.cache.set(e,t)}get(e,t){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(t){const r=t();return this.cache.set(e,r),r}else return}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}}class ng extends du{constructor(e){super(),this.cache=new Map,this.converter=e??(t=>t)}has(e,t){return this.throwIfDisposed(),this.cacheForContext(e).has(t)}set(e,t,r){this.throwIfDisposed(),this.cacheForContext(e).set(t,r)}get(e,t,r){this.throwIfDisposed();const i=this.cacheForContext(e);if(i.has(t))return i.get(t);if(r){const s=r();return i.set(t,s),s}else return}delete(e,t){return this.throwIfDisposed(),this.cacheForContext(e).delete(t)}clear(e){if(this.throwIfDisposed(),e){const t=this.converter(e);this.cache.delete(t)}else this.cache.clear()}cacheForContext(e){const t=this.converter(e);let r=this.cache.get(t);return r||(r=new Map,this.cache.set(t,r)),r}}class rg extends tg{constructor(e,t){super(),t?(this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(t,()=>{this.clear()})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((r,i)=>{i.length>0&&this.clear()}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}}class ig{constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new rg(e.shared)}getScope(e){const t=[],r=this.reflection.getReferenceType(e),i=et(e.container).precomputedScopes;if(i){let a=e.container;do{const o=i.get(a);o.length>0&&t.push(Z(o).filter(l=>this.reflection.isSubtype(l.type,r))),a=a.$container}while(a)}let s=this.getGlobalScope(r,e);for(let a=t.length-1;a>=0;a--)s=this.createScope(t[a],s);return s}createScope(e,t,r){return new hl(Z(e),t,r)}createScopeForNodes(e,t,r){const i=Z(e).map(s=>{const a=this.nameProvider.getName(s);if(a)return this.descriptions.createDescription(s,a)}).nonNullable();return new hl(i,t,r)}getGlobalScope(e,t){return this.globalScopeCache.get(e,()=>new eg(this.indexManager.allElements(e)))}}function sg(n){return typeof n.$comment=="string"}function pl(n){return typeof n=="object"&&!!n&&("$ref"in n||"$error"in n)}class ag{constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,t){const r=t??{},i=t==null?void 0:t.replacer,s=(o,l)=>this.replacer(o,l,r),a=i?(o,l)=>i(o,l,s):s;try{return this.currentDocument=et(e),JSON.stringify(e,a,t==null?void 0:t.space)}finally{this.currentDocument=void 0}}deserialize(e,t){const r=t??{},i=JSON.parse(e);return this.linkNode(i,i,r),i}replacer(e,t,{refText:r,sourceText:i,textRegions:s,comments:a,uriConverter:o}){var l,c,u,d;if(!this.ignoreProperties.has(e))if(Ue(t)){const h=t.ref,f=r?t.$refText:void 0;if(h){const m=et(h);let g="";this.currentDocument&&this.currentDocument!==m&&(o?g=o(m.uri,t):g=m.uri.toString());const A=this.astNodeLocator.getAstNodePath(h);return{$ref:`${g}#${A}`,$refText:f}}else return{$error:(c=(l=t.error)===null||l===void 0?void 0:l.message)!==null&&c!==void 0?c:"Could not resolve reference",$refText:f}}else if(ae(t)){let h;if(s&&(h=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},t)),(!e||t.$document)&&(h!=null&&h.$textRegion)&&(h.$textRegion.documentURI=(u=this.currentDocument)===null||u===void 0?void 0:u.uri.toString())),i&&!e&&(h??(h=Object.assign({},t)),h.$sourceText=(d=t.$cstNode)===null||d===void 0?void 0:d.text),a){h??(h=Object.assign({},t));const f=this.commentProvider.getComment(t);f&&(h.$comment=f.replace(/\r/g,""))}return h??t}else return t}addAstNodeRegionWithAssignmentsTo(e){const t=r=>({offset:r.offset,end:r.end,length:r.length,range:r.range});if(e.$cstNode){const r=e.$textRegion=t(e.$cstNode),i=r.assignments={};return Object.keys(e).filter(s=>!s.startsWith("$")).forEach(s=>{const a=ff(e.$cstNode,s).map(t);a.length!==0&&(i[s]=a)}),e}}linkNode(e,t,r,i,s,a){for(const[l,c]of Object.entries(e))if(Array.isArray(c))for(let u=0;u<c.length;u++){const d=c[u];pl(d)?c[u]=this.reviveReference(e,l,t,d,r):ae(d)&&this.linkNode(d,t,r,e,l,u)}else pl(c)?e[l]=this.reviveReference(e,l,t,c,r):ae(c)&&this.linkNode(c,t,r,e,l);const o=e;o.$container=i,o.$containerProperty=s,o.$containerIndex=a}reviveReference(e,t,r,i,s){let a=i.$refText,o=i.$error;if(i.$ref){const l=this.getRefNode(r,i.$ref,s.uriConverter);if(ae(l))return a||(a=this.nameProvider.getName(l)),{$refText:a??"",ref:l};o=l}if(o){const l={$refText:a??""};return l.error={container:e,property:t,message:o,reference:l},l}else return}getRefNode(e,t,r){try{const i=t.indexOf("#");if(i===0){const l=this.astNodeLocator.getAstNode(e,t.substring(1));return l||"Could not resolve path: "+t}if(i<0){const l=r?r(t):vt.parse(t),c=this.langiumDocuments.getDocument(l);return c?c.parseResult.value:"Could not find document for URI: "+t}const s=r?r(t.substring(0,i)):vt.parse(t.substring(0,i)),a=this.langiumDocuments.getDocument(s);if(!a)return"Could not find document for URI: "+t;if(i===t.length-1)return a.parseResult.value;const o=this.astNodeLocator.getAstNode(a.parseResult.value,t.substring(i+1));return o||"Could not resolve URI: "+t}catch(i){return String(i)}}}class og{get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map,this.fileExtensionMap=new Map,this.textDocuments=e==null?void 0:e.workspace.TextDocuments}register(e){const t=e.LanguageMetaData;for(const r of t.fileExtensions)this.fileExtensionMap.has(r)&&console.warn(`The file extension ${r} is used by multiple languages. It is now assigned to '${t.languageId}'.`),this.fileExtensionMap.set(r,e);this.languageIdMap.set(t.languageId,e),this.languageIdMap.size===1?this.singleton=e:this.singleton=void 0}getServices(e){var t,r;if(this.singleton!==void 0)return this.singleton;if(this.languageIdMap.size===0)throw new Error("The service registry is empty. Use `register` to register the services of a language.");const i=(r=(t=this.textDocuments)===null||t===void 0?void 0:t.get(e))===null||r===void 0?void 0:r.languageId;if(i!==void 0){const o=this.languageIdMap.get(i);if(o)return o}const s=it.extname(e),a=this.fileExtensionMap.get(s);if(!a)throw i?new Error(`The service registry contains no services for the extension '${s}' for language '${i}'.`):new Error(`The service registry contains no services for the extension '${s}'.`);return a}hasServices(e){try{return this.getServices(e),!0}catch{return!1}}get all(){return Array.from(this.languageIdMap.values())}}function Kn(n){return{code:n}}var fi;(function(n){n.all=["fast","slow","built-in"]})(fi||(fi={}));class lg{constructor(e){this.entries=new di,this.entriesBefore=[],this.entriesAfter=[],this.reflection=e.shared.AstReflection}register(e,t=this,r="fast"){if(r==="built-in")throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(const[i,s]of Object.entries(e)){const a=s;if(Array.isArray(a))for(const o of a){const l={check:this.wrapValidationException(o,t),category:r};this.addEntry(i,l)}else if(typeof a=="function"){const o={check:this.wrapValidationException(a,t),category:r};this.addEntry(i,o)}else tr()}}wrapValidationException(e,t){return async(r,i,s)=>{await this.handleException(()=>e.call(t,r,i,s),"An error occurred during validation",i,r)}}async handleException(e,t,r,i){try{await e()}catch(s){if(Ni(s))throw s;console.error(`${t}:`,s),s instanceof Error&&s.stack&&console.error(s.stack);const a=s instanceof Error?s.message:String(s);r("error",`${t}: ${a}`,{node:i})}}addEntry(e,t){if(e==="AstNode"){this.entries.add("AstNode",t);return}for(const r of this.reflection.getAllSubTypes(e))this.entries.add(r,t)}getChecks(e,t){let r=Z(this.entries.get(e)).concat(this.entries.get("AstNode"));return t&&(r=r.filter(i=>t.includes(i.category))),r.map(i=>i.check)}registerBeforeDocument(e,t=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",t))}registerAfterDocument(e,t=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",t))}wrapPreparationException(e,t,r){return async(i,s,a,o)=>{await this.handleException(()=>e.call(r,i,s,a,o),t,s,i)}}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}}class cg{constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,t={},r=V.None){const i=e.parseResult,s=[];if(await Ae(r),(!t.categories||t.categories.includes("built-in"))&&(this.processLexingErrors(i,s,t),t.stopAfterLexingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===be.LexingError})||(this.processParsingErrors(i,s,t),t.stopAfterParsingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===be.ParsingError}))||(this.processLinkingErrors(e,s,t),t.stopAfterLinkingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===be.LinkingError}))))return s;try{s.push(...await this.validateAst(i.value,t,r))}catch(a){if(Ni(a))throw a;console.error("An error occurred during validation:",a)}return await Ae(r),s}processLexingErrors(e,t,r){var i,s,a;const o=[...e.lexerErrors,...(s=(i=e.lexerReport)===null||i===void 0?void 0:i.diagnostics)!==null&&s!==void 0?s:[]];for(const l of o){const c=(a=l.severity)!==null&&a!==void 0?a:"error",u={severity:qi(c),range:{start:{line:l.line-1,character:l.column-1},end:{line:l.line-1,character:l.column+l.length-1}},message:l.message,data:dg(c),source:this.getSource()};t.push(u)}}processParsingErrors(e,t,r){for(const i of e.parserErrors){let s;if(isNaN(i.token.startOffset)){if("previousToken"in i){const a=i.previousToken;if(isNaN(a.startOffset)){const o={line:0,character:0};s={start:o,end:o}}else{const o={line:a.endLine-1,character:a.endColumn};s={start:o,end:o}}}}else s=cs(i.token);if(s){const a={severity:qi("error"),range:s,message:i.message,data:Kn(be.ParsingError),source:this.getSource()};t.push(a)}}}processLinkingErrors(e,t,r){for(const i of e.references){const s=i.error;if(s){const a={node:s.container,property:s.property,index:s.index,data:{code:be.LinkingError,containerType:s.container.$type,property:s.property,refText:s.reference.$refText}};t.push(this.toDiagnostic("error",s.message,a))}}}async validateAst(e,t,r=V.None){const i=[],s=(a,o,l)=>{i.push(this.toDiagnostic(a,o,l))};return await this.validateAstBefore(e,t,s,r),await this.validateAstNodes(e,t,s,r),await this.validateAstAfter(e,t,s,r),i}async validateAstBefore(e,t,r,i=V.None){var s;const a=this.validationRegistry.checksBefore;for(const o of a)await Ae(i),await o(e,r,(s=t.categories)!==null&&s!==void 0?s:[],i)}async validateAstNodes(e,t,r,i=V.None){await Promise.all(wt(e).map(async s=>{await Ae(i);const a=this.validationRegistry.getChecks(s.$type,t.categories);for(const o of a)await o(s,r,i)}))}async validateAstAfter(e,t,r,i=V.None){var s;const a=this.validationRegistry.checksAfter;for(const o of a)await Ae(i),await o(e,r,(s=t.categories)!==null&&s!==void 0?s:[],i)}toDiagnostic(e,t,r){return{message:t,range:ug(r),severity:qi(e),code:r.code,codeDescription:r.codeDescription,tags:r.tags,relatedInformation:r.relatedInformation,data:r.data,source:this.getSource()}}getSource(){return this.metadata.languageId}}function ug(n){if(n.range)return n.range;let e;return typeof n.property=="string"?e=Zl(n.node.$cstNode,n.property,n.index):typeof n.keyword=="string"&&(e=hf(n.node.$cstNode,n.keyword,n.index)),e??(e=n.node.$cstNode),e?e.range:{start:{line:0,character:0},end:{line:0,character:0}}}function qi(n){switch(n){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+n)}}function dg(n){switch(n){case"error":return Kn(be.LexingError);case"warning":return Kn(be.LexingWarning);case"info":return Kn(be.LexingInfo);case"hint":return Kn(be.LexingHint);default:throw new Error("Invalid diagnostic severity: "+n)}}var be;(function(n){n.LexingError="lexing-error",n.LexingWarning="lexing-warning",n.LexingInfo="lexing-info",n.LexingHint="lexing-hint",n.ParsingError="parsing-error",n.LinkingError="linking-error"})(be||(be={}));class fg{constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,t,r){const i=r??et(e);t??(t=this.nameProvider.getName(e));const s=this.astNodeLocator.getAstNodePath(e);if(!t)throw new Error(`Node at path ${s} has no name.`);let a;const o=()=>{var l;return a??(a=jr((l=this.nameProvider.getNameNode(e))!==null&&l!==void 0?l:e.$cstNode))};return{node:e,name:t,get nameSegment(){return o()},selectionSegment:jr(e.$cstNode),type:e.$type,documentUri:i.uri,path:s}}}class hg{constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,t=V.None){const r=[],i=e.parseResult.value;for(const s of wt(i))await Ae(t),ql(s).filter(a=>!Nr(a)).forEach(a=>{const o=this.createDescription(a);o&&r.push(o)});return r}createDescription(e){const t=e.reference.$nodeDescription,r=e.reference.$refNode;if(!t||!r)return;const i=et(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:t.documentUri,targetPath:t.path,segment:jr(r),local:it.equals(t.documentUri,i)}}}class pg{constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){const t=this.getAstNodePath(e.$container),r=this.getPathSegment(e);return t+this.segmentSeparator+r}return""}getPathSegment({$containerProperty:e,$containerIndex:t}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return t!==void 0?e+this.indexSeparator+t:e}getAstNode(e,t){return t.split(this.segmentSeparator).reduce((i,s)=>{if(!i||s.length===0)return i;const a=s.indexOf(this.indexSeparator);if(a>0){const o=s.substring(0,a),l=parseInt(s.substring(a+1)),c=i[o];return c==null?void 0:c[l]}return i[s]},e)}}class mg{constructor(e){this._ready=new Ta,this.settings={},this.workspaceConfig=!1,this.onConfigurationSectionUpdateEmitter=new ou,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var t,r;this.workspaceConfig=(r=(t=e.capabilities.workspace)===null||t===void 0?void 0:t.configuration)!==null&&r!==void 0?r:!1}async initialized(e){if(this.workspaceConfig){if(e.register){const t=this.serviceRegistry.all;e.register({section:t.map(r=>this.toSectionName(r.LanguageMetaData.languageId))})}if(e.fetchConfiguration){const t=this.serviceRegistry.all.map(i=>({section:this.toSectionName(i.LanguageMetaData.languageId)})),r=await e.fetchConfiguration(t);t.forEach((i,s)=>{this.updateSectionConfiguration(i.section,r[s])})}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(t=>{const r=e.settings[t];this.updateSectionConfiguration(t,r),this.onConfigurationSectionUpdateEmitter.fire({section:t,configuration:r})})}updateSectionConfiguration(e,t){this.settings[e]=t}async getConfiguration(e,t){await this.ready;const r=this.toSectionName(e);if(this.settings[r])return this.settings[r][t]}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}}var qn;(function(n){function e(t){return{dispose:async()=>await t()}}n.create=e})(qn||(qn={}));class gg{constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new di,this.documentPhaseListeners=new di,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=U.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.textDocuments=e.workspace.TextDocuments,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,t={},r=V.None){var i,s;for(const a of e){const o=a.uri.toString();if(a.state===U.Validated){if(typeof t.validation=="boolean"&&t.validation)a.state=U.IndexedReferences,a.diagnostics=void 0,this.buildState.delete(o);else if(typeof t.validation=="object"){const l=this.buildState.get(o),c=(i=l==null?void 0:l.result)===null||i===void 0?void 0:i.validationChecks;if(c){const d=((s=t.validation.categories)!==null&&s!==void 0?s:fi.all).filter(h=>!c.includes(h));d.length>0&&(this.buildState.set(o,{completed:!1,options:{validation:Object.assign(Object.assign({},t.validation),{categories:d})},result:l.result}),a.state=U.IndexedReferences)}}}else this.buildState.delete(o)}this.currentState=U.Changed,await this.emitUpdate(e.map(a=>a.uri),[]),await this.buildDocuments(e,t,r)}async update(e,t,r=V.None){this.currentState=U.Changed;for(const a of t)this.langiumDocuments.deleteDocument(a),this.buildState.delete(a.toString()),this.indexManager.remove(a);for(const a of e){if(!this.langiumDocuments.invalidateDocument(a)){const l=this.langiumDocumentFactory.fromModel({$type:"INVALID"},a);l.state=U.Changed,this.langiumDocuments.addDocument(l)}this.buildState.delete(a.toString())}const i=Z(e).concat(t).map(a=>a.toString()).toSet();this.langiumDocuments.all.filter(a=>!i.has(a.uri.toString())&&this.shouldRelink(a,i)).forEach(a=>{this.serviceRegistry.getServices(a.uri).references.Linker.unlink(a),a.state=Math.min(a.state,U.ComputedScopes),a.diagnostics=void 0}),await this.emitUpdate(e,t),await Ae(r);const s=this.sortDocuments(this.langiumDocuments.all.filter(a=>{var o;return a.state<U.Linked||!(!((o=this.buildState.get(a.uri.toString()))===null||o===void 0)&&o.completed)}).toArray());await this.buildDocuments(s,this.updateBuildOptions,r)}async emitUpdate(e,t){await Promise.all(this.updateListeners.map(r=>r(e,t)))}sortDocuments(e){let t=0,r=e.length-1;for(;t<r;){for(;t<e.length&&this.hasTextDocument(e[t]);)t++;for(;r>=0&&!this.hasTextDocument(e[r]);)r--;t<r&&([e[t],e[r]]=[e[r],e[t]])}return e}hasTextDocument(e){var t;return!!(!((t=this.textDocuments)===null||t===void 0)&&t.get(e.uri))}shouldRelink(e,t){return e.references.some(r=>r.error!==void 0)?!0:this.indexManager.isAffected(e,t)}onUpdate(e){return this.updateListeners.push(e),qn.create(()=>{const t=this.updateListeners.indexOf(e);t>=0&&this.updateListeners.splice(t,1)})}async buildDocuments(e,t,r){this.prepareBuild(e,t),await this.runCancelable(e,U.Parsed,r,s=>this.langiumDocumentFactory.update(s,r)),await this.runCancelable(e,U.IndexedContent,r,s=>this.indexManager.updateContent(s,r)),await this.runCancelable(e,U.ComputedScopes,r,async s=>{const a=this.serviceRegistry.getServices(s.uri).references.ScopeComputation;s.precomputedScopes=await a.computeLocalScopes(s,r)}),await this.runCancelable(e,U.Linked,r,s=>this.serviceRegistry.getServices(s.uri).references.Linker.link(s,r)),await this.runCancelable(e,U.IndexedReferences,r,s=>this.indexManager.updateReferences(s,r));const i=e.filter(s=>this.shouldValidate(s));await this.runCancelable(i,U.Validated,r,s=>this.validate(s,r));for(const s of e){const a=this.buildState.get(s.uri.toString());a&&(a.completed=!0)}}prepareBuild(e,t){for(const r of e){const i=r.uri.toString(),s=this.buildState.get(i);(!s||s.completed)&&this.buildState.set(i,{completed:!1,options:t,result:s==null?void 0:s.result})}}async runCancelable(e,t,r,i){const s=e.filter(o=>o.state<t);for(const o of s)await Ae(r),await i(o),o.state=t,await this.notifyDocumentPhase(o,t,r);const a=e.filter(o=>o.state===t);await this.notifyBuildPhase(a,t,r),this.currentState=t}onBuildPhase(e,t){return this.buildPhaseListeners.add(e,t),qn.create(()=>{this.buildPhaseListeners.delete(e,t)})}onDocumentPhase(e,t){return this.documentPhaseListeners.add(e,t),qn.create(()=>{this.documentPhaseListeners.delete(e,t)})}waitUntil(e,t,r){let i;if(t&&"path"in t?i=t:r=t,r??(r=V.None),i){const s=this.langiumDocuments.getDocument(i);if(s&&s.state>e)return Promise.resolve(i)}return this.currentState>=e?Promise.resolve(void 0):r.isCancellationRequested?Promise.reject(ui):new Promise((s,a)=>{const o=this.onBuildPhase(e,()=>{if(o.dispose(),l.dispose(),i){const c=this.langiumDocuments.getDocument(i);s(c==null?void 0:c.uri)}else s(void 0)}),l=r.onCancellationRequested(()=>{o.dispose(),l.dispose(),a(ui)})})}async notifyDocumentPhase(e,t,r){const s=this.documentPhaseListeners.get(t).slice();for(const a of s)try{await a(e,r)}catch(o){if(!Ni(o))throw o}}async notifyBuildPhase(e,t,r){if(e.length===0)return;const s=this.buildPhaseListeners.get(t).slice();for(const a of s)await Ae(r),await a(e,r)}shouldValidate(e){return!!this.getBuildOptions(e).validation}async validate(e,t){var r,i;const s=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,a=this.getBuildOptions(e).validation,o=typeof a=="object"?a:void 0,l=await s.validateDocument(e,o,t);e.diagnostics?e.diagnostics.push(...l):e.diagnostics=l;const c=this.buildState.get(e.uri.toString());if(c){(r=c.result)!==null&&r!==void 0||(c.result={});const u=(i=o==null?void 0:o.categories)!==null&&i!==void 0?i:fi.all;c.result.validationChecks?c.result.validationChecks.push(...u):c.result.validationChecks=[...u]}}getBuildOptions(e){var t,r;return(r=(t=this.buildState.get(e.uri.toString()))===null||t===void 0?void 0:t.options)!==null&&r!==void 0?r:{}}}class yg{constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new ng,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,t){const r=et(e).uri,i=[];return this.referenceIndex.forEach(s=>{s.forEach(a=>{it.equals(a.targetUri,r)&&a.targetPath===t&&i.push(a)})}),Z(i)}allElements(e,t){let r=Z(this.symbolIndex.keys());return t&&(r=r.filter(i=>!t||t.has(i))),r.map(i=>this.getFileDescriptions(i,e)).flat()}getFileDescriptions(e,t){var r;return t?this.symbolByTypeIndex.get(e,t,()=>{var s;return((s=this.symbolIndex.get(e))!==null&&s!==void 0?s:[]).filter(o=>this.astReflection.isSubtype(o.type,t))}):(r=this.symbolIndex.get(e))!==null&&r!==void 0?r:[]}remove(e){const t=e.toString();this.symbolIndex.delete(t),this.symbolByTypeIndex.clear(t),this.referenceIndex.delete(t)}async updateContent(e,t=V.None){const i=await this.serviceRegistry.getServices(e.uri).references.ScopeComputation.computeExports(e,t),s=e.uri.toString();this.symbolIndex.set(s,i),this.symbolByTypeIndex.clear(s)}async updateReferences(e,t=V.None){const i=await this.serviceRegistry.getServices(e.uri).workspace.ReferenceDescriptionProvider.createDescriptions(e,t);this.referenceIndex.set(e.uri.toString(),i)}isAffected(e,t){const r=this.referenceIndex.get(e.uri.toString());return r?r.some(i=>!i.local&&t.has(i.targetUri.toString())):!1}}class Tg{constructor(e){this.initialBuildOptions={},this._ready=new Ta,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var t;this.folders=(t=e.workspaceFolders)!==null&&t!==void 0?t:void 0}initialized(e){return this.mutex.write(t=>{var r;return this.initializeWorkspace((r=this.folders)!==null&&r!==void 0?r:[],t)})}async initializeWorkspace(e,t=V.None){const r=await this.performStartup(e);await Ae(t),await this.documentBuilder.build(r,this.initialBuildOptions,t)}async performStartup(e){const t=this.serviceRegistry.all.flatMap(s=>s.LanguageMetaData.fileExtensions),r=[],i=s=>{r.push(s),this.langiumDocuments.hasDocument(s.uri)||this.langiumDocuments.addDocument(s)};return await this.loadAdditionalDocuments(e,i),await Promise.all(e.map(s=>[s,this.getRootFolder(s)]).map(async s=>this.traverseFolder(...s,t,i))),this._ready.resolve(),r}loadAdditionalDocuments(e,t){return Promise.resolve()}getRootFolder(e){return vt.parse(e.uri)}async traverseFolder(e,t,r,i){const s=await this.fileSystemProvider.readDirectory(t);await Promise.all(s.map(async a=>{if(this.includeEntry(e,a,r)){if(a.isDirectory)await this.traverseFolder(e,a.uri,r,i);else if(a.isFile){const o=await this.langiumDocuments.getOrCreateDocument(a.uri);i(o)}}}))}includeEntry(e,t,r){const i=it.basename(t.uri);if(i.startsWith("."))return!1;if(t.isDirectory)return i!=="node_modules"&&i!=="out";if(t.isFile){const s=it.extname(t.uri);return r.includes(s)}return!1}}class Rg{buildUnexpectedCharactersMessage(e,t,r,i,s){return ms.buildUnexpectedCharactersMessage(e,t,r,i,s)}buildUnableToPopLexerModeMessage(e){return ms.buildUnableToPopLexerModeMessage(e)}}const vg={mode:"full"};class Ag{constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider,this.tokenBuilder=e.parser.TokenBuilder;const t=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(t);const r=ml(t)?Object.values(t):t,i=e.LanguageMetaData.mode==="production";this.chevrotainLexer=new fe(r,{positionTracking:"full",skipValidations:i,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,t=vg){var r,i,s;const a=this.chevrotainLexer.tokenize(e);return{tokens:a.tokens,errors:a.errors,hidden:(r=a.groups.hidden)!==null&&r!==void 0?r:[],report:(s=(i=this.tokenBuilder).flushLexingReport)===null||s===void 0?void 0:s.call(i,e)}}toTokenTypeDictionary(e){if(ml(e))return e;const t=fu(e)?Object.values(e.modes).flat():e,r={};return t.forEach(i=>r[i.name]=i),r}}function Eg(n){return Array.isArray(n)&&(n.length===0||"name"in n[0])}function fu(n){return n&&"modes"in n&&"defaultMode"in n}function ml(n){return!Eg(n)&&!fu(n)}function $g(n,e,t){let r,i;typeof n=="string"?(i=e,r=t):(i=n.range.start,r=e),i||(i=P.create(0,0));const s=hu(n),a=Ra(r),o=xg({lines:s,position:i,options:a});return _g({index:0,tokens:o,position:i})}function kg(n,e){const t=Ra(e),r=hu(n);if(r.length===0)return!1;const i=r[0],s=r[r.length-1],a=t.start,o=t.end;return!!(a!=null&&a.exec(i))&&!!(o!=null&&o.exec(s))}function hu(n){let e="";return typeof n=="string"?e=n:e=n.text,e.split(Zd)}const gl=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,Sg=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function xg(n){var e,t,r;const i=[];let s=n.position.line,a=n.position.character;for(let o=0;o<n.lines.length;o++){const l=o===0,c=o===n.lines.length-1;let u=n.lines[o],d=0;if(l&&n.options.start){const f=(e=n.options.start)===null||e===void 0?void 0:e.exec(u);f&&(d=f.index+f[0].length)}else{const f=(t=n.options.line)===null||t===void 0?void 0:t.exec(u);f&&(d=f.index+f[0].length)}if(c){const f=(r=n.options.end)===null||r===void 0?void 0:r.exec(u);f&&(u=u.substring(0,f.index))}if(u=u.substring(0,wg(u)),Ws(u,d)>=u.length){if(i.length>0){const f=P.create(s,a);i.push({type:"break",content:"",range:O.create(f,f)})}}else{gl.lastIndex=d;const f=gl.exec(u);if(f){const m=f[0],g=f[1],A=P.create(s,a+d),y=P.create(s,a+d+m.length);i.push({type:"tag",content:g,range:O.create(A,y)}),d+=m.length,d=Ws(u,d)}if(d<u.length){const m=u.substring(d),g=Array.from(m.matchAll(Sg));i.push(...Ig(g,m,s,a+d))}}s++,a=0}return i.length>0&&i[i.length-1].type==="break"?i.slice(0,-1):i}function Ig(n,e,t,r){const i=[];if(n.length===0){const s=P.create(t,r),a=P.create(t,r+e.length);i.push({type:"text",content:e,range:O.create(s,a)})}else{let s=0;for(const o of n){const l=o.index,c=e.substring(s,l);c.length>0&&i.push({type:"text",content:e.substring(s,l),range:O.create(P.create(t,s+r),P.create(t,l+r))});let u=c.length+1;const d=o[1];if(i.push({type:"inline-tag",content:d,range:O.create(P.create(t,s+u+r),P.create(t,s+u+d.length+r))}),u+=d.length,o.length===4){u+=o[2].length;const h=o[3];i.push({type:"text",content:h,range:O.create(P.create(t,s+u+r),P.create(t,s+u+h.length+r))})}else i.push({type:"text",content:"",range:O.create(P.create(t,s+u+r),P.create(t,s+u+r))});s=l+o[0].length}const a=e.substring(s);a.length>0&&i.push({type:"text",content:a,range:O.create(P.create(t,s+r),P.create(t,s+r+a.length))})}return i}const Cg=/\S/,Ng=/\s*$/;function Ws(n,e){const t=n.substring(e).match(Cg);return t?e+t.index:n.length}function wg(n){const e=n.match(Ng);if(e&&typeof e.index=="number")return e.index}function _g(n){var e,t,r,i;const s=P.create(n.position.line,n.position.character);if(n.tokens.length===0)return new yl([],O.create(s,s));const a=[];for(;n.index<n.tokens.length;){const c=Lg(n,a[a.length-1]);c&&a.push(c)}const o=(t=(e=a[0])===null||e===void 0?void 0:e.range.start)!==null&&t!==void 0?t:s,l=(i=(r=a[a.length-1])===null||r===void 0?void 0:r.range.end)!==null&&i!==void 0?i:s;return new yl(a,O.create(o,l))}function Lg(n,e){const t=n.tokens[n.index];if(t.type==="tag")return mu(n,!1);if(t.type==="text"||t.type==="inline-tag")return pu(n);bg(t,e),n.index++}function bg(n,e){if(e){const t=new yu("",n.range);"inlines"in e?e.inlines.push(t):e.content.inlines.push(t)}}function pu(n){let e=n.tokens[n.index];const t=e;let r=e;const i=[];for(;e&&e.type!=="break"&&e.type!=="tag";)i.push(Og(n)),r=e,e=n.tokens[n.index];return new js(i,O.create(t.range.start,r.range.end))}function Og(n){return n.tokens[n.index].type==="inline-tag"?mu(n,!0):gu(n)}function mu(n,e){const t=n.tokens[n.index++],r=t.content.substring(1),i=n.tokens[n.index];if((i==null?void 0:i.type)==="text")if(e){const s=gu(n);return new Xi(r,new js([s],s.range),e,O.create(t.range.start,s.range.end))}else{const s=pu(n);return new Xi(r,s,e,O.create(t.range.start,s.range.end))}else{const s=t.range;return new Xi(r,new js([],s),e,s)}}function gu(n){const e=n.tokens[n.index++];return new yu(e.content,e.range)}function Ra(n){if(!n)return Ra({start:"/**",end:"*/",line:"*"});const{start:e,end:t,line:r}=n;return{start:Yi(e,!0),end:Yi(t,!1),line:Yi(r,!0)}}function Yi(n,e){if(typeof n=="string"||typeof n=="object"){const t=typeof n=="string"?Ri(n):n.source;return e?new RegExp(`^\\s*${t}`):new RegExp(`\\s*${t}\\s*$`)}else return n}class yl{constructor(e,t){this.elements=e,this.range=t}getTag(e){return this.getAllTags().find(t=>t.name===e)}getTags(e){return this.getAllTags().filter(t=>t.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(const t of this.elements)if(e.length===0)e=t.toString();else{const r=t.toString();e+=Tl(e)+r}return e.trim()}toMarkdown(e){let t="";for(const r of this.elements)if(t.length===0)t=r.toMarkdown(e);else{const i=r.toMarkdown(e);t+=Tl(t)+i}return t.trim()}}class Xi{constructor(e,t,r,i){this.name=e,this.content=t,this.inline=r,this.range=i}toString(){let e=`@${this.name}`;const t=this.content.toString();return this.content.inlines.length===1?e=`${e} ${t}`:this.content.inlines.length>1&&(e=`${e}
${t}`),this.inline?`{${e}}`:e}toMarkdown(e){var t,r;return(r=(t=e==null?void 0:e.renderTag)===null||t===void 0?void 0:t.call(e,this))!==null&&r!==void 0?r:this.toMarkdownDefault(e)}toMarkdownDefault(e){const t=this.content.toMarkdown(e);if(this.inline){const s=Pg(this.name,t,e??{});if(typeof s=="string")return s}let r="";(e==null?void 0:e.tag)==="italic"||(e==null?void 0:e.tag)===void 0?r="*":(e==null?void 0:e.tag)==="bold"?r="**":(e==null?void 0:e.tag)==="bold-italic"&&(r="***");let i=`${r}@${this.name}${r}`;return this.content.inlines.length===1?i=`${i} — ${t}`:this.content.inlines.length>1&&(i=`${i}
${t}`),this.inline?`{${i}}`:i}}function Pg(n,e,t){var r,i;if(n==="linkplain"||n==="linkcode"||n==="link"){const s=e.indexOf(" ");let a=e;if(s>0){const l=Ws(e,s);a=e.substring(l),e=e.substring(0,s)}return(n==="linkcode"||n==="link"&&t.link==="code")&&(a=`\`${a}\``),(i=(r=t.renderLink)===null||r===void 0?void 0:r.call(t,e,a))!==null&&i!==void 0?i:Mg(e,a)}}function Mg(n,e){try{return vt.parse(n,!0),`[${e}](${n})`}catch{return n}}class js{constructor(e,t){this.inlines=e,this.range=t}toString(){let e="";for(let t=0;t<this.inlines.length;t++){const r=this.inlines[t],i=this.inlines[t+1];e+=r.toString(),i&&i.range.start.line>r.range.start.line&&(e+=`
`)}return e}toMarkdown(e){let t="";for(let r=0;r<this.inlines.length;r++){const i=this.inlines[r],s=this.inlines[r+1];t+=i.toMarkdown(e),s&&s.range.start.line>i.range.start.line&&(t+=`
`)}return t}}class yu{constructor(e,t){this.text=e,this.range=t}toString(){return this.text}toMarkdown(){return this.text}}function Tl(n){return n.endsWith(`
`)?`
`:`

`}class Dg{constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){const t=this.commentProvider.getComment(e);if(t&&kg(t))return $g(t).toMarkdown({renderLink:(i,s)=>this.documentationLinkRenderer(e,i,s),renderTag:i=>this.documentationTagRenderer(e,i)})}documentationLinkRenderer(e,t,r){var i;const s=(i=this.findNameInPrecomputedScopes(e,t))!==null&&i!==void 0?i:this.findNameInGlobalScope(e,t);if(s&&s.nameSegment){const a=s.nameSegment.range.start.line+1,o=s.nameSegment.range.start.character+1,l=s.documentUri.with({fragment:`L${a},${o}`});return`[${r}](${l.toString()})`}else return}documentationTagRenderer(e,t){}findNameInPrecomputedScopes(e,t){const i=et(e).precomputedScopes;if(!i)return;let s=e;do{const o=i.get(s).find(l=>l.name===t);if(o)return o;s=s.$container}while(s)}findNameInGlobalScope(e,t){return this.indexManager.allElements().find(i=>i.name===t)}}class Fg{constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var t;return sg(e)?e.$comment:(t=Nd(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||t===void 0?void 0:t.text}}class Gg{constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,t){return Promise.resolve(this.syncParser.parse(e))}}class Ug{constructor(){this.previousTokenSource=new ya,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();const t=jm();return this.previousTokenSource=t,this.enqueue(this.writeQueue,e,t.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,t,r=V.None){const i=new Ta,s={action:t,deferred:i,cancellationToken:r};return e.push(s),this.performNextOperation(),i.promise}async performNextOperation(){if(!this.done)return;const e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else if(this.readQueue.length>0)e.push(...this.readQueue.splice(0,this.readQueue.length));else return;this.done=!1,await Promise.all(e.map(async({action:t,deferred:r,cancellationToken:i})=>{try{const s=await Promise.resolve().then(()=>t(i));r.resolve(s)}catch(s){Ni(s)?r.resolve(void 0):r.reject(s)}})),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}}class Bg{constructor(e){this.grammarElementIdMap=new fl,this.tokenTypeIdMap=new fl,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):void 0,parserErrors:e.parserErrors.map(t=>Object.assign(Object.assign({},t),{message:t.message})),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){const t=new Map,r=new Map;for(const i of wt(e))t.set(i,{});if(e.$cstNode)for(const i of ls(e.$cstNode))r.set(i,{});return{astNodes:t,cstNodes:r}}dehydrateAstNode(e,t){const r=t.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode!==void 0&&(r.$cstNode=this.dehydrateCstNode(e.$cstNode,t));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const a=[];r[i]=a;for(const o of s)ae(o)?a.push(this.dehydrateAstNode(o,t)):Ue(o)?a.push(this.dehydrateReference(o,t)):a.push(o)}else ae(s)?r[i]=this.dehydrateAstNode(s,t):Ue(s)?r[i]=this.dehydrateReference(s,t):s!==void 0&&(r[i]=s);return r}dehydrateReference(e,t){const r={};return r.$refText=e.$refText,e.$refNode&&(r.$refNode=t.cstNodes.get(e.$refNode)),r}dehydrateCstNode(e,t){const r=t.cstNodes.get(e);return Ul(e)?r.fullText=e.fullText:r.grammarSource=this.getGrammarElementId(e.grammarSource),r.hidden=e.hidden,r.astNode=t.astNodes.get(e.astNode),Xn(e)?r.content=e.content.map(i=>this.dehydrateCstNode(i,t)):Gl(e)&&(r.tokenType=e.tokenType.name,r.offset=e.offset,r.length=e.length,r.startLine=e.range.start.line,r.startColumn=e.range.start.character,r.endLine=e.range.end.line,r.endColumn=e.range.end.character),r}hydrate(e){const t=e.value,r=this.createHydrationContext(t);return"$cstNode"in t&&this.hydrateCstNode(t.$cstNode,r),{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(t,r)}}createHydrationContext(e){const t=new Map,r=new Map;for(const s of wt(e))t.set(s,{});let i;if(e.$cstNode)for(const s of ls(e.$cstNode)){let a;"fullText"in s?(a=new Yc(s.fullText),i=a):"content"in s?a=new ma:"tokenType"in s&&(a=this.hydrateCstLeafNode(s)),a&&(r.set(s,a),a.root=i)}return{astNodes:t,cstNodes:r}}hydrateAstNode(e,t){const r=t.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode&&(r.$cstNode=t.cstNodes.get(e.$cstNode));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const a=[];r[i]=a;for(const o of s)ae(o)?a.push(this.setParent(this.hydrateAstNode(o,t),r)):Ue(o)?a.push(this.hydrateReference(o,r,i,t)):a.push(o)}else ae(s)?r[i]=this.setParent(this.hydrateAstNode(s,t),r):Ue(s)?r[i]=this.hydrateReference(s,r,i,t):s!==void 0&&(r[i]=s);return r}setParent(e,t){return e.$container=t,e}hydrateReference(e,t,r,i){return this.linker.buildReference(t,r,i.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,t,r=0){const i=t.cstNodes.get(e);if(typeof e.grammarSource=="number"&&(i.grammarSource=this.getGrammarElement(e.grammarSource)),i.astNode=t.astNodes.get(e.astNode),Xn(i))for(const s of e.content){const a=this.hydrateCstNode(s,t,r++);i.content.push(a)}return i}hydrateCstLeafNode(e){const t=this.getTokenType(e.tokenType),r=e.offset,i=e.length,s=e.startLine,a=e.startColumn,o=e.endLine,l=e.endColumn,c=e.hidden;return new Ms(r,i,{start:{line:s,character:a},end:{line:o,character:l}},t,c)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(e)return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.getKey(e)}createGrammarElementIdMap(){let e=0;for(const t of wt(this.grammar))_d(t)&&this.grammarElementIdMap.set(t,e++)}}function ot(n){return{documentation:{CommentProvider:e=>new Fg(e),DocumentationProvider:e=>new Dg(e)},parser:{AsyncParser:e=>new Gg(e),GrammarConfig:e=>$f(e),LangiumParser:e=>wm(e),CompletionParser:e=>Nm(e),ValueConverter:()=>new iu,TokenBuilder:()=>new ru,Lexer:e=>new Ag(e),ParserErrorMessageProvider:()=>new Qc,LexerErrorMessageProvider:()=>new Rg},workspace:{AstNodeLocator:()=>new pg,AstNodeDescriptionProvider:e=>new fg(e),ReferenceDescriptionProvider:e=>new hg(e)},references:{Linker:e=>new Ym(e),NameProvider:()=>new Jm,ScopeProvider:e=>new ig(e),ScopeComputation:e=>new Zm(e),References:e=>new Qm(e)},serializer:{Hydrator:e=>new Bg(e),JsonSerializer:e=>new ag(e)},validation:{DocumentValidator:e=>new cg(e),ValidationRegistry:e=>new lg(e)},shared:()=>n.shared}}function lt(n){return{ServiceRegistry:e=>new og(e),workspace:{LangiumDocuments:e=>new qm(e),LangiumDocumentFactory:e=>new zm(e),DocumentBuilder:e=>new gg(e),IndexManager:e=>new yg(e),WorkspaceManager:e=>new Tg(e),FileSystemProvider:e=>n.fileSystemProvider(e),WorkspaceLock:()=>new Ug,ConfigurationProvider:e=>new mg(e)}}}var Rl;(function(n){n.merge=(e,t)=>hi(hi({},e),t)})(Rl||(Rl={}));function oe(n,e,t,r,i,s,a,o,l){const c=[n,e,t,r,i,s,a,o,l].reduce(hi,{});return Tu(c)}const Vg=Symbol("isProxy");function Tu(n,e){const t=new Proxy({},{deleteProperty:()=>!1,set:()=>{throw new Error("Cannot set property on injected service container")},get:(r,i)=>i===Vg?!0:Al(r,i,n,e||t),getOwnPropertyDescriptor:(r,i)=>(Al(r,i,n,e||t),Object.getOwnPropertyDescriptor(r,i)),has:(r,i)=>i in n,ownKeys:()=>[...Object.getOwnPropertyNames(n)]});return t}const vl=Symbol();function Al(n,e,t,r){if(e in n){if(n[e]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:n[e]});if(n[e]===vl)throw new Error('Cycle detected. Please make "'+String(e)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');return n[e]}else if(e in t){const i=t[e];n[e]=vl;try{n[e]=typeof i=="function"?i(r):Tu(i,r)}catch(s){throw n[e]=s instanceof Error?s:void 0,s}return n[e]}else return}function hi(n,e){if(e){for(const[t,r]of Object.entries(e))if(r!==void 0){const i=n[t];i!==null&&r!==null&&typeof i=="object"&&typeof r=="object"?n[t]=hi(i,r):n[t]=r}}return n}class Kg{readFile(){throw new Error("No file system is available.")}async readDirectory(){return[]}}const ct={fileSystemProvider:()=>new Kg},Wg={Grammar:()=>{},LanguageMetaData:()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"})},jg={AstReflection:()=>new zl};function Hg(){const n=oe(lt(ct),jg),e=oe(ot({shared:n}),Wg);return n.ServiceRegistry.register(e),e}function kt(n){var e;const t=Hg(),r=t.serializer.JsonSerializer.deserialize(n);return t.shared.workspace.LangiumDocumentFactory.fromModel(r,vt.parse(`memory://${(e=r.name)!==null&&e!==void 0?e:"grammar"}.langium`)),r}var zg=Object.defineProperty,v=(n,e)=>zg(n,"name",{value:e,configurable:!0}),El="Statement",Dr="Architecture";function qg(n){return De.isInstance(n,Dr)}v(qg,"isArchitecture");var Er="Axis",Wn="Branch";function Yg(n){return De.isInstance(n,Wn)}v(Yg,"isBranch");var $r="Checkout",kr="CherryPicking",Ji="ClassDefStatement",jn="Commit";function Xg(n){return De.isInstance(n,jn)}v(Xg,"isCommit");var Qi="Curve",Zi="Edge",es="Entry",Hn="GitGraph";function Jg(n){return De.isInstance(n,Hn)}v(Jg,"isGitGraph");var ts="Group",Fr="Info";function Qg(n){return De.isInstance(n,Fr)}v(Qg,"isInfo");var Sr="Item",ns="Junction",zn="Merge";function Zg(n){return De.isInstance(n,zn)}v(Zg,"isMerge");var rs="Option",Gr="Packet";function ey(n){return De.isInstance(n,Gr)}v(ey,"isPacket");var Ur="PacketBlock";function ty(n){return De.isInstance(n,Ur)}v(ty,"isPacketBlock");var Br="Pie";function ny(n){return De.isInstance(n,Br)}v(ny,"isPie");var Vr="PieSection";function ry(n){return De.isInstance(n,Vr)}v(ry,"isPieSection");var is="Radar",ss="Service",Kr="Treemap";function iy(n){return De.isInstance(n,Kr)}v(iy,"isTreemap");var as="TreemapRow",xr="Direction",Ir="Leaf",Cr="Section",Lt,Ru=(Lt=class extends Fl{getAllTypes(){return[Dr,Er,Wn,$r,kr,Ji,jn,Qi,xr,Zi,es,Hn,ts,Fr,Sr,ns,Ir,zn,rs,Gr,Ur,Br,Vr,is,Cr,ss,El,Kr,as]}computeIsSubtype(e,t){switch(e){case Wn:case $r:case kr:case jn:case zn:return this.isSubtype(El,t);case xr:return this.isSubtype(Hn,t);case Ir:case Cr:return this.isSubtype(Sr,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Entry:axis":return Er;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case Dr:return{name:Dr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case Er:return{name:Er,properties:[{name:"label"},{name:"name"}]};case Wn:return{name:Wn,properties:[{name:"name"},{name:"order"}]};case $r:return{name:$r,properties:[{name:"branch"}]};case kr:return{name:kr,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case Ji:return{name:Ji,properties:[{name:"className"},{name:"styleText"}]};case jn:return{name:jn,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case Qi:return{name:Qi,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]};case Zi:return{name:Zi,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case es:return{name:es,properties:[{name:"axis"},{name:"value"}]};case Hn:return{name:Hn,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case ts:return{name:ts,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case Fr:return{name:Fr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case Sr:return{name:Sr,properties:[{name:"classSelector"},{name:"name"}]};case ns:return{name:ns,properties:[{name:"id"},{name:"in"}]};case zn:return{name:zn,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case rs:return{name:rs,properties:[{name:"name"},{name:"value",defaultValue:!1}]};case Gr:return{name:Gr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case Ur:return{name:Ur,properties:[{name:"bits"},{name:"end"},{name:"label"},{name:"start"}]};case Br:return{name:Br,properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case Vr:return{name:Vr,properties:[{name:"label"},{name:"value"}]};case is:return{name:is,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]};case ss:return{name:ss,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case Kr:return{name:Kr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"},{name:"TreemapRows",defaultValue:[]}]};case as:return{name:as,properties:[{name:"indent"},{name:"item"}]};case xr:return{name:xr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};case Ir:return{name:Ir,properties:[{name:"classSelector"},{name:"name"},{name:"value"}]};case Cr:return{name:Cr,properties:[{name:"classSelector"},{name:"name"}]};default:return{name:e,properties:[]}}}},v(Lt,"MermaidAstReflection"),Lt),De=new Ru,$l,sy=v(()=>$l??($l=kt(`{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@7"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"InfoGrammar"),kl,ay=v(()=>kl??(kl=kt(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"packet"},{"$type":"Keyword","value":"packet-beta"}]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}],"cardinality":"?"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"+"},{"$type":"Assignment","feature":"bits","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]}]},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@9"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"PacketGrammar"),Sl,oy=v(()=>Sl??(Sl=kt(`{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@9"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"PieGrammar"),xl,ly=v(()=>xl??(xl=kt(`{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@18"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@19"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"ArchitectureGrammar"),Il,cy=v(()=>Il??(Il=kt(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@14"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"REFERENCE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`)),"GitGraphGrammar"),Cl,uy=v(()=>Cl??(Cl=kt(`{"$type":"Grammar","isDeclared":true,"name":"Radar","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@2"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@16"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"interfaces":[{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@2"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"definesHiddenTokens":false,"hiddenTokens":[],"types":[],"usedGrammars":[]}`)),"RadarGrammar"),Nl,dy=v(()=>Nl??(Nl=kt(`{"$type":"Grammar","isDeclared":true,"name":"Treemap","rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"ParserRule","entry":true,"name":"Treemap","returnType":{"$ref":"#/interfaces@4"},"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"TreemapRows","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"TREEMAP_KEYWORD","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"treemap-beta"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"treemap"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"CLASS_DEF","definition":{"$type":"RegexToken","regex":"/classDef\\\\s+([a-zA-Z_][a-zA-Z0-9_]+)(?:\\\\s+([^;\\\\r\\\\n]*))?(?:;)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STYLE_SEPARATOR","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":":::"}},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"SEPARATOR","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":":"}},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"COMMA","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":","}},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WS","definition":{"$type":"RegexToken","regex":"/[ \\\\t]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"ML_COMMENT","definition":{"$type":"RegexToken","regex":"/\\\\%\\\\%[^\\\\n]*/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"NL","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false},{"$type":"ParserRule","name":"TreemapRow","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"indent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"item","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"ClassDef","dataType":"string","definition":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Item","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Section","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]},{"$type":"Assignment","feature":"classSelector","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Leaf","returnType":{"$ref":"#/interfaces@2"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]},{"$type":"Assignment","feature":"classSelector","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INDENTATION","definition":{"$type":"RegexToken","regex":"/[ \\\\t]{1,}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID2","definition":{"$type":"RegexToken","regex":"/[a-zA-Z_][a-zA-Z0-9_]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER2","definition":{"$type":"RegexToken","regex":"/[0-9_\\\\.\\\\,]+/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"MyNumber","dataType":"number","definition":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"STRING2","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"interfaces":[{"$type":"Interface","name":"Item","attributes":[{"$type":"TypeAttribute","name":"name","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false},{"$type":"TypeAttribute","name":"classSelector","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]},{"$type":"Interface","name":"Section","superTypes":[{"$ref":"#/interfaces@0"}],"attributes":[]},{"$type":"Interface","name":"Leaf","superTypes":[{"$ref":"#/interfaces@0"}],"attributes":[{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}]},{"$type":"Interface","name":"ClassDefStatement","attributes":[{"$type":"TypeAttribute","name":"className","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false},{"$type":"TypeAttribute","name":"styleText","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false}],"superTypes":[]},{"$type":"Interface","name":"Treemap","attributes":[{"$type":"TypeAttribute","name":"TreemapRows","type":{"$type":"ArrayType","elementType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@14"}}},"isOptional":false},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[],"$comment":"/**\\n * Treemap grammar for Langium\\n * Converted from mindmap grammar\\n *\\n * The ML_COMMENT and NL hidden terminals handle whitespace, comments, and newlines\\n * before the treemap keyword, allowing for empty lines and comments before the\\n * treemap declaration.\\n */"}`)),"TreemapGrammar"),fy={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},hy={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},py={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},my={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},gy={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},yy={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Ty={languageId:"treemap",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},St={AstReflection:v(()=>new Ru,"AstReflection")},Ry={Grammar:v(()=>sy(),"Grammar"),LanguageMetaData:v(()=>fy,"LanguageMetaData"),parser:{}},vy={Grammar:v(()=>ay(),"Grammar"),LanguageMetaData:v(()=>hy,"LanguageMetaData"),parser:{}},Ay={Grammar:v(()=>oy(),"Grammar"),LanguageMetaData:v(()=>py,"LanguageMetaData"),parser:{}},Ey={Grammar:v(()=>ly(),"Grammar"),LanguageMetaData:v(()=>my,"LanguageMetaData"),parser:{}},$y={Grammar:v(()=>cy(),"Grammar"),LanguageMetaData:v(()=>gy,"LanguageMetaData"),parser:{}},ky={Grammar:v(()=>uy(),"Grammar"),LanguageMetaData:v(()=>yy,"LanguageMetaData"),parser:{}},Sy={Grammar:v(()=>dy(),"Grammar"),LanguageMetaData:v(()=>Ty,"LanguageMetaData"),parser:{}},xy=/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,Iy=/accTitle[\t ]*:([^\n\r]*)/,Cy=/title([\t ][^\n\r]*|)/,Ny={ACC_DESCR:xy,ACC_TITLE:Iy,TITLE:Cy},bt,wi=(bt=class extends iu{runConverter(e,t,r){let i=this.runCommonConverter(e,t,r);return i===void 0&&(i=this.runCustomConverter(e,t,r)),i===void 0?super.runConverter(e,t,r):i}runCommonConverter(e,t,r){const i=Ny[e.name];if(i===void 0)return;const s=i.exec(t);if(s!==null){if(s[1]!==void 0)return s[1].trim().replace(/[\t ]{2,}/gm," ");if(s[2]!==void 0)return s[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,`
`)}}},v(bt,"AbstractMermaidValueConverter"),bt),Ot,_i=(Ot=class extends wi{runCustomConverter(e,t,r){}},v(Ot,"CommonValueConverter"),Ot),Pt,ut=(Pt=class extends ru{constructor(e){super(),this.keywords=new Set(e)}buildKeywordTokens(e,t,r){const i=super.buildKeywordTokens(e,t,r);return i.forEach(s=>{this.keywords.has(s.name)&&s.PATTERN!==void 0&&(s.PATTERN=new RegExp(s.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),i}},v(Pt,"AbstractMermaidTokenBuilder"),Pt),Mt;Mt=class extends ut{},v(Mt,"CommonTokenBuilder");var Dt,wy=(Dt=class extends ut{constructor(){super(["gitGraph"])}},v(Dt,"GitGraphTokenBuilder"),Dt),vu={parser:{TokenBuilder:v(()=>new wy,"TokenBuilder"),ValueConverter:v(()=>new _i,"ValueConverter")}};function Au(n=ct){const e=oe(lt(n),St),t=oe(ot({shared:e}),$y,vu);return e.ServiceRegistry.register(t),{shared:e,GitGraph:t}}v(Au,"createGitGraphServices");var Ft,_y=(Ft=class extends ut{constructor(){super(["info","showInfo"])}},v(Ft,"InfoTokenBuilder"),Ft),Eu={parser:{TokenBuilder:v(()=>new _y,"TokenBuilder"),ValueConverter:v(()=>new _i,"ValueConverter")}};function $u(n=ct){const e=oe(lt(n),St),t=oe(ot({shared:e}),Ry,Eu);return e.ServiceRegistry.register(t),{shared:e,Info:t}}v($u,"createInfoServices");var Gt,Ly=(Gt=class extends ut{constructor(){super(["packet"])}},v(Gt,"PacketTokenBuilder"),Gt),ku={parser:{TokenBuilder:v(()=>new Ly,"TokenBuilder"),ValueConverter:v(()=>new _i,"ValueConverter")}};function Su(n=ct){const e=oe(lt(n),St),t=oe(ot({shared:e}),vy,ku);return e.ServiceRegistry.register(t),{shared:e,Packet:t}}v(Su,"createPacketServices");var Ut,by=(Ut=class extends ut{constructor(){super(["pie","showData"])}},v(Ut,"PieTokenBuilder"),Ut),Bt,Oy=(Bt=class extends wi{runCustomConverter(e,t,r){if(e.name==="PIE_SECTION_LABEL")return t.replace(/"/g,"").trim()}},v(Bt,"PieValueConverter"),Bt),xu={parser:{TokenBuilder:v(()=>new by,"TokenBuilder"),ValueConverter:v(()=>new Oy,"ValueConverter")}};function Iu(n=ct){const e=oe(lt(n),St),t=oe(ot({shared:e}),Ay,xu);return e.ServiceRegistry.register(t),{shared:e,Pie:t}}v(Iu,"createPieServices");var Vt,Py=(Vt=class extends ut{constructor(){super(["architecture"])}},v(Vt,"ArchitectureTokenBuilder"),Vt),Kt,My=(Kt=class extends wi{runCustomConverter(e,t,r){if(e.name==="ARCH_ICON")return t.replace(/[()]/g,"").trim();if(e.name==="ARCH_TEXT_ICON")return t.replace(/["()]/g,"");if(e.name==="ARCH_TITLE")return t.replace(/[[\]]/g,"").trim()}},v(Kt,"ArchitectureValueConverter"),Kt),Cu={parser:{TokenBuilder:v(()=>new Py,"TokenBuilder"),ValueConverter:v(()=>new My,"ValueConverter")}};function Nu(n=ct){const e=oe(lt(n),St),t=oe(ot({shared:e}),Ey,Cu);return e.ServiceRegistry.register(t),{shared:e,Architecture:t}}v(Nu,"createArchitectureServices");var Wt,Dy=(Wt=class extends ut{constructor(){super(["radar-beta"])}},v(Wt,"RadarTokenBuilder"),Wt),wu={parser:{TokenBuilder:v(()=>new Dy,"TokenBuilder"),ValueConverter:v(()=>new _i,"ValueConverter")}};function _u(n=ct){const e=oe(lt(n),St),t=oe(ot({shared:e}),ky,wu);return e.ServiceRegistry.register(t),{shared:e,Radar:t}}v(_u,"createRadarServices");var jt,Fy=(jt=class extends ut{constructor(){super(["treemap"])}},v(jt,"TreemapTokenBuilder"),jt),Gy=/classDef\s+([A-Z_a-z]\w+)(?:\s+([^\n\r;]*))?;?/,Ht,Uy=(Ht=class extends wi{runCustomConverter(e,t,r){if(e.name==="NUMBER2")return parseFloat(t.replace(/,/g,""));if(e.name==="SEPARATOR")return t.substring(1,t.length-1);if(e.name==="STRING2")return t.substring(1,t.length-1);if(e.name==="INDENTATION")return t.length;if(e.name==="ClassDef"){if(typeof t!="string")return t;const i=Gy.exec(t);if(i)return{$type:"ClassDefStatement",className:i[1],styleText:i[2]||void 0}}}},v(Ht,"TreemapValueConverter"),Ht);function Lu(n){const e=n.validation.TreemapValidator,t=n.validation.ValidationRegistry;if(t){const r={Treemap:e.checkSingleRoot.bind(e)};t.register(r,e)}}v(Lu,"registerValidationChecks");var zt,By=(zt=class{checkSingleRoot(e,t){let r;for(const i of e.TreemapRows)i.item&&(r===void 0&&i.indent===void 0?r=0:i.indent===void 0?t("error","Multiple root nodes are not allowed in a treemap.",{node:i,property:"item"}):r!==void 0&&r>=parseInt(i.indent,10)&&t("error","Multiple root nodes are not allowed in a treemap.",{node:i,property:"item"}))}},v(zt,"TreemapValidator"),zt),bu={parser:{TokenBuilder:v(()=>new Fy,"TokenBuilder"),ValueConverter:v(()=>new Uy,"ValueConverter")},validation:{TreemapValidator:v(()=>new By,"TreemapValidator")}};function Ou(n=ct){const e=oe(lt(n),St),t=oe(ot({shared:e}),Sy,bu);return e.ServiceRegistry.register(t),Lu(t),{shared:e,Treemap:t}}v(Ou,"createTreemapServices");var je={},Vy={info:v(async()=>{const{createInfoServices:n}=await ht(async()=>{const{createInfoServices:t}=await Promise.resolve().then(()=>jy);return{createInfoServices:t}},void 0),e=n().Info.parser.LangiumParser;je.info=e},"info"),packet:v(async()=>{const{createPacketServices:n}=await ht(async()=>{const{createPacketServices:t}=await Promise.resolve().then(()=>Hy);return{createPacketServices:t}},void 0),e=n().Packet.parser.LangiumParser;je.packet=e},"packet"),pie:v(async()=>{const{createPieServices:n}=await ht(async()=>{const{createPieServices:t}=await Promise.resolve().then(()=>zy);return{createPieServices:t}},void 0),e=n().Pie.parser.LangiumParser;je.pie=e},"pie"),architecture:v(async()=>{const{createArchitectureServices:n}=await ht(async()=>{const{createArchitectureServices:t}=await Promise.resolve().then(()=>qy);return{createArchitectureServices:t}},void 0),e=n().Architecture.parser.LangiumParser;je.architecture=e},"architecture"),gitGraph:v(async()=>{const{createGitGraphServices:n}=await ht(async()=>{const{createGitGraphServices:t}=await Promise.resolve().then(()=>Yy);return{createGitGraphServices:t}},void 0),e=n().GitGraph.parser.LangiumParser;je.gitGraph=e},"gitGraph"),radar:v(async()=>{const{createRadarServices:n}=await ht(async()=>{const{createRadarServices:t}=await Promise.resolve().then(()=>Xy);return{createRadarServices:t}},void 0),e=n().Radar.parser.LangiumParser;je.radar=e},"radar"),treemap:v(async()=>{const{createTreemapServices:n}=await ht(async()=>{const{createTreemapServices:t}=await Promise.resolve().then(()=>Jy);return{createTreemapServices:t}},void 0),e=n().Treemap.parser.LangiumParser;je.treemap=e},"treemap")};async function Ky(n,e){const t=Vy[n];if(!t)throw new Error(`Unknown diagram type: ${n}`);je[n]||await t();const i=je[n].parse(e);if(i.lexerErrors.length>0||i.parserErrors.length>0)throw new Wy(i);return i.value}v(Ky,"parse");var qt,Wy=(qt=class extends Error{constructor(e){const t=e.lexerErrors.map(i=>i.message).join(`
`),r=e.parserErrors.map(i=>i.message).join(`
`);super(`Parsing failed: ${t} ${r}`),this.result=e}},v(qt,"MermaidParseError"),qt);const jy=Object.freeze(Object.defineProperty({__proto__:null,InfoModule:Eu,createInfoServices:$u},Symbol.toStringTag,{value:"Module"})),Hy=Object.freeze(Object.defineProperty({__proto__:null,PacketModule:ku,createPacketServices:Su},Symbol.toStringTag,{value:"Module"})),zy=Object.freeze(Object.defineProperty({__proto__:null,PieModule:xu,createPieServices:Iu},Symbol.toStringTag,{value:"Module"})),qy=Object.freeze(Object.defineProperty({__proto__:null,ArchitectureModule:Cu,createArchitectureServices:Nu},Symbol.toStringTag,{value:"Module"})),Yy=Object.freeze(Object.defineProperty({__proto__:null,GitGraphModule:vu,createGitGraphServices:Au},Symbol.toStringTag,{value:"Module"})),Xy=Object.freeze(Object.defineProperty({__proto__:null,RadarModule:wu,createRadarServices:_u},Symbol.toStringTag,{value:"Module"})),Jy=Object.freeze(Object.defineProperty({__proto__:null,TreemapModule:bu,createTreemapServices:Ou},Symbol.toStringTag,{value:"Module"}));export{Ky as p};
