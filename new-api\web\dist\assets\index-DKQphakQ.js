import{j as i,u as rt,v as nt,w as ot,B as W,x as it,y as st,z as ct,G as lt,H as He}from"./semi-ui-Csx8wKaA.js";import{r as c,L as at}from"./react-core-DskXcPn0.js";import{bk as U,bl as G,bm as ut,aQ as ft,aR as pt,bn as yt,bo as mt,bp as bt,bq as vt,br as ht,bs as jt,bt as dt,bu as gt,bv as Ot,bw as wt,bx as xt,by as Pt,bz as St,bA as $t,bB as _t,bC as zt,bD as Et,bE as Ct,A as je,s as It,a_ as At,a$ as Dt}from"./index-DTzYmM8W.js";import{u as Mt}from"./i18n-bne0o_C4.js";import{m as Nt}from"./tools-C3llIrvJ.js";import"./react-components-C55tCU1e.js";var F="Grok",Tt=.75,Lt=.2,Re="#000",Vt=Re,Ht="#fff",Rt=.75;function P(e){"@babel/helpers - typeof";return P=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},P(e)}var Bt=["size","style"];function de(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function q(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?de(Object(r),!0).forEach(function(n){Kt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):de(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Kt(e,t,r){return t=Ft(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ft(e){var t=Wt(e,"string");return P(t)=="symbol"?t:String(t)}function Wt(e,t){if(P(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(P(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ut(e,t){if(e==null)return{};var r=Gt(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Gt(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var ce=c.memo(function(e){var t=e.size,r=t===void 0?"1em":t,n=e.style,o=Ut(e,Bt);return i.jsxs("svg",q(q({fill:"currentColor",fillRule:"evenodd",height:r,style:q({flex:"none",lineHeight:1},n),viewBox:"0 0 24 24",width:r,xmlns:"http://www.w3.org/2000/svg"},o),{},{children:[i.jsx("title",{children:F}),i.jsx("path",{d:"M9.27 15.29l7.978-5.897c.391-.29.95-.177 1.137.272.98 2.369.542 5.215-1.41 7.169-1.951 1.954-4.667 2.382-7.149 1.406l-2.711 1.257c3.889 2.661 8.611 2.003 11.562-.953 2.341-2.344 3.066-5.539 2.388-8.42l.006.007c-.983-4.232.242-5.924 2.75-9.383.06-.082.12-.164.179-.248l-3.301 3.305v-.01L9.267 15.292M7.623 16.723c-2.792-2.67-2.31-6.801.071-9.184 1.761-1.763 4.647-2.483 7.166-1.425l2.705-1.25a7.808 7.808 0 00-1.829-1A8.975 8.975 0 005.984 5.83c-2.533 2.536-3.33 6.436-1.962 9.764 1.022 2.487-.653 4.246-2.34 6.022-.599.63-1.199 1.259-1.682 1.925l7.62-6.815"})]}))});function S(e){"@babel/helpers - typeof";return S=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},S(e)}function ge(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function kt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ge(Object(r),!0).forEach(function(n){Xt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ge(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Xt(e,t,r){return t=Yt(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Yt(e){var t=qt(e,"string");return S(t)=="symbol"?t:String(t)}function qt(e,t){if(S(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(S(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Qt(e){if(e==null)throw new TypeError("Cannot destructure "+e)}var Jt=c.memo(function(e){var t=Object.assign({},(Qt(e),e));return i.jsx(U,kt({Icon:ce,"aria-label":F,background:Vt,color:Ht,iconMultiple:Rt},t))});function $(e){"@babel/helpers - typeof";return $=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$(e)}var Zt=["size","style"];function Oe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function Q(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Oe(Object(r),!0).forEach(function(n){er(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Oe(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function er(e,t,r){return t=tr(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tr(e){var t=rr(e,"string");return $(t)=="symbol"?t:String(t)}function rr(e,t){if($(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if($(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function nr(e,t){if(e==null)return{};var r=or(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function or(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var Be=c.memo(function(e){var t=e.size,r=t===void 0?"1em":t,n=e.style,o=nr(e,Zt);return i.jsxs("svg",Q(Q({fill:"currentColor",fillRule:"evenodd",height:r,style:Q({flex:"none",lineHeight:1},n),viewBox:"0 0 63 24",xmlns:"http://www.w3.org/2000/svg"},o),{},{children:[i.jsx("title",{children:F}),i.jsx("path",{d:"M47.419 21.645V2.457h3.033V15.12l6.415-7.369h3.678l-5.772 6.316 5.825 7.578h-3.624l-4.717-6.512-1.805-.012v6.524h-3.033zM38.22 21.968c-4.51 0-6.952-3.198-6.952-7.283 0-4.112 2.443-7.283 6.952-7.283 4.537 0 6.952 3.17 6.952 7.283 0 4.085-2.415 7.283-6.952 7.283zm-3.785-7.283c0 3.17 1.718 4.756 3.785 4.756 2.094 0 3.785-1.585 3.785-4.756 0-3.172-1.691-4.784-3.785-4.784-2.067 0-3.785 1.612-3.785 4.784zM22.826 21.645V9.955l2.55-2.204h5.422v2.58H25.86v11.314h-3.033zM11.228 22C5.447 22 2 17.802 2 12.078 2 6.3 5.57 2 11.341 2c4.51 0 7.811 2.311 8.59 6.611h-3.463c-.51-2.445-2.55-3.816-5.127-3.816-4.16 0-5.986 3.601-5.986 7.283 0 3.682 1.826 7.256 5.986 7.256 3.973 0 5.717-2.876 5.852-5.267h-5.986v-2.783h9.046l-.015 1.455c0 5.406-2.203 9.261-9.01 9.261z"})]}))});function _(e){"@babel/helpers - typeof";return _=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_(e)}function we(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function ir(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?we(Object(r),!0).forEach(function(n){sr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):we(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function sr(e,t,r){return t=cr(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cr(e){var t=lr(e,"string");return _(t)=="symbol"?t:String(t)}function lr(e,t){if(_(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(_(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ar(e){if(e==null)throw new TypeError("Cannot destructure "+e)}var ur=c.memo(function(e){var t=Object.assign({},(ar(e),e));return i.jsx(G,ir({Icon:ce,Text:Be,"aria-label":F,iconProps:{shape:"square"},spaceMultiple:Lt,textMultiple:Tt},t))}),d=ce;d.Text=Be;d.Combine=ur;d.Avatar=Jt;d.colorPrimary=Re;d.title=F;var b="AzureAI",fr=.7,pr=.2,Ke="#000",yr=Ke,mr="#fff",br=.6;function z(e){"@babel/helpers - typeof";return z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},z(e)}var vr=["size","style"];function xe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function J(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xe(Object(r),!0).forEach(function(n){hr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xe(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function hr(e,t,r){return t=jr(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jr(e){var t=dr(e,"string");return z(t)=="symbol"?t:String(t)}function dr(e,t){if(z(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(z(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function gr(e,t){return Pr(e)||xr(e,t)||wr(e,t)||Or()}function Or(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wr(e,t){if(e){if(typeof e=="string")return Pe(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pe(e,t)}}function Pe(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function xr(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,o,s,l,p=[],u=!0,a=!1;try{if(s=(r=r.call(e)).next,t!==0)for(;!(u=(n=s.call(r)).done)&&(p.push(n.value),p.length!==t);u=!0);}catch(k){a=!0,o=k}finally{try{if(!u&&r.return!=null&&(l=r.return(),Object(l)!==l))return}finally{if(a)throw o}}return p}}function Pr(e){if(Array.isArray(e))return e}function Sr(e,t){if(e==null)return{};var r=$r(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function $r(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var le=c.memo(function(e){var t=e.size,r=t===void 0?"1em":t,n=e.style,o=Sr(e,vr),s=ut(b,3),l=gr(s,3),p=l[0],u=l[1],a=l[2];return i.jsxs("svg",J(J({height:r,style:J({flex:"none",lineHeight:1},n),viewBox:"0 0 24 24",width:r,xmlns:"http://www.w3.org/2000/svg"},o),{},{children:[i.jsx("title",{children:b}),i.jsx("path",{clipRule:"evenodd",d:"M16.233 0c.713 0 1.345.551 1.572 1.329.227.778 1.555 5.59 1.555 5.59v9.562h-4.813L14.645 0h1.588z",fill:p.fill,fillRule:"evenodd"}),i.jsx("path",{d:"M23.298 7.47c0-.34-.275-.6-.6-.6h-2.835a3.617 3.617 0 00-3.614 3.615v5.996h3.436a3.617 3.617 0 003.613-3.614V7.47z",fill:u.fill}),i.jsx("path",{clipRule:"evenodd",d:"M16.233 0a.982.982 0 00-.989.989l-.097 18.198A4.814 4.814 0 0110.334 24H1.6a.597.597 0 01-.567-.794l7-19.981A4.819 4.819 0 0112.57 0h3.679-.016z",fill:a.fill,fillRule:"evenodd"}),i.jsxs("defs",{children:[i.jsxs("linearGradient",{gradientUnits:"userSpaceOnUse",id:p.id,x1:"18.242",x2:"14.191",y1:"16.837",y2:".616",children:[i.jsx("stop",{stopColor:"#712575"}),i.jsx("stop",{offset:".09",stopColor:"#9A2884"}),i.jsx("stop",{offset:".18",stopColor:"#BF2C92"}),i.jsx("stop",{offset:".27",stopColor:"#DA2E9C"}),i.jsx("stop",{offset:".34",stopColor:"#EB30A2"}),i.jsx("stop",{offset:".4",stopColor:"#F131A5"}),i.jsx("stop",{offset:".5",stopColor:"#EC30A3"}),i.jsx("stop",{offset:".61",stopColor:"#DF2F9E"}),i.jsx("stop",{offset:".72",stopColor:"#C92D96"}),i.jsx("stop",{offset:".83",stopColor:"#AA2A8A"}),i.jsx("stop",{offset:".95",stopColor:"#83267C"}),i.jsx("stop",{offset:"1",stopColor:"#712575"})]}),i.jsxs("linearGradient",{gradientUnits:"userSpaceOnUse",id:u.id,x1:"19.782",x2:"19.782",y1:".34",y2:"23.222",children:[i.jsx("stop",{stopColor:"#DA7ED0"}),i.jsx("stop",{offset:".08",stopColor:"#B17BD5"}),i.jsx("stop",{offset:".19",stopColor:"#8778DB"}),i.jsx("stop",{offset:".3",stopColor:"#6276E1"}),i.jsx("stop",{offset:".41",stopColor:"#4574E5"}),i.jsx("stop",{offset:".54",stopColor:"#2E72E8"}),i.jsx("stop",{offset:".67",stopColor:"#1D71EB"}),i.jsx("stop",{offset:".81",stopColor:"#1471EC"}),i.jsx("stop",{offset:"1",stopColor:"#1171ED"})]}),i.jsxs("linearGradient",{gradientUnits:"userSpaceOnUse",id:a.id,x1:"18.404",x2:"3.236",y1:".859",y2:"25.183",children:[i.jsx("stop",{stopColor:"#DA7ED0"}),i.jsx("stop",{offset:".05",stopColor:"#B77BD4"}),i.jsx("stop",{offset:".11",stopColor:"#9079DA"}),i.jsx("stop",{offset:".18",stopColor:"#6E77DF"}),i.jsx("stop",{offset:".25",stopColor:"#5175E3"}),i.jsx("stop",{offset:".33",stopColor:"#3973E7"}),i.jsx("stop",{offset:".42",stopColor:"#2772E9"}),i.jsx("stop",{offset:".54",stopColor:"#1A71EB"}),i.jsx("stop",{offset:".68",stopColor:"#1371EC"}),i.jsx("stop",{offset:"1",stopColor:"#1171ED"})]})]})]}))});function E(e){"@babel/helpers - typeof";return E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},E(e)}function Se(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function _r(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Se(Object(r),!0).forEach(function(n){zr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Se(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zr(e,t,r){return t=Er(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Er(e){var t=Cr(e,"string");return E(t)=="symbol"?t:String(t)}function Cr(e,t){if(E(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(E(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ir(e){if(e==null)throw new TypeError("Cannot destructure "+e)}var Ar=c.memo(function(e){var t=Object.assign({},(Ir(e),e));return i.jsx(U,_r({Icon:le,"aria-label":b,background:yr,color:mr,iconMultiple:br},t))});function C(e){"@babel/helpers - typeof";return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},C(e)}var Dr=["size","style"];function $e(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function Z(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$e(Object(r),!0).forEach(function(n){Mr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$e(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Mr(e,t,r){return t=Nr(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Nr(e){var t=Tr(e,"string");return C(t)=="symbol"?t:String(t)}function Tr(e,t){if(C(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(C(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Lr(e,t){if(e==null)return{};var r=Vr(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Vr(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var Fe=c.memo(function(e){var t=e.size,r=t===void 0?"1em":t,n=e.style,o=Lr(e,Dr);return i.jsxs("svg",Z(Z({fill:"currentColor",fillRule:"evenodd",height:r,style:Z({flex:"none",lineHeight:1},n),viewBox:"0 0 24 24",width:r,xmlns:"http://www.w3.org/2000/svg"},o),{},{children:[i.jsx("title",{children:b}),i.jsx("path",{clipRule:"evenodd",d:"M16.233 0c.713 0 1.345.551 1.572 1.329.227.778 1.555 5.59 1.555 5.59v9.562h-4.813L14.645 0h1.588z",fillOpacity:".5"}),i.jsx("path",{d:"M23.298 7.47c0-.34-.275-.6-.6-.6h-2.835a3.617 3.617 0 00-3.614 3.615v5.996h3.436a3.617 3.617 0 003.613-3.614V7.47z"}),i.jsx("path",{clipRule:"evenodd",d:"M16.233 0a.982.982 0 00-.989.989l-.097 18.198A4.814 4.814 0 0110.334 24H1.6a.597.597 0 01-.567-.794l7-19.981A4.819 4.819 0 0112.57 0h3.679-.016z"})]}))});function I(e){"@babel/helpers - typeof";return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},I(e)}var Hr=["size","style"];function _e(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function ee(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_e(Object(r),!0).forEach(function(n){Rr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_e(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Rr(e,t,r){return t=Br(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Br(e){var t=Kr(e,"string");return I(t)=="symbol"?t:String(t)}function Kr(e,t){if(I(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(I(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Fr(e,t){if(e==null)return{};var r=Wr(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Wr(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var We=c.memo(function(e){var t=e.size,r=t===void 0?"1em":t,n=e.style,o=Fr(e,Hr);return i.jsxs("svg",ee(ee({fill:"currentColor",fillRule:"evenodd",height:r,style:ee({flex:"none",lineHeight:1},n),viewBox:"0 0 96 24",xmlns:"http://www.w3.org/2000/svg"},o),{},{children:[i.jsx("title",{children:b}),i.jsx("path",{clipRule:"evenodd",d:"M12.504 3l6.915 18.325H15.91l-1.625-4.548H7.047l-1.562 4.548H2L8.916 3h3.588zm-1.806 3.4h-.09l-2.717 7.718h5.508l-2.7-7.719-.001.001zM21 8.188h10.517v1.24l-6.904 9.444h6.943v2.453H20.513v-1.47l6.802-9.213H21V8.188zm24.2 0v13.137h-3.127v-1.724h-.05a4.185 4.185 0 01-1.646 1.398c-.689.337-1.459.506-2.313.506-1.53 0-2.692-.433-3.49-1.298-.799-.864-1.2-2.208-1.2-4.032V8.188h3.141v7.617c0 1.108.219 1.94.66 2.498.439.558 1.102.837 1.99.837.88 0 1.585-.308 2.115-.926.53-.617.793-1.426.793-2.421V8.188H45.2zm8.908-.217c.247 0 .469.017.666.051.196.034.364.076.5.128v3.131c-.164-.12-.398-.232-.71-.338-.312-.107-.69-.16-1.133-.16-.761 0-1.405.32-1.93.958-.525.64-.788 1.624-.788 2.952v6.632h-3.1V8.188h3.1v2.07h.052c.28-.715.71-1.275 1.28-1.68.573-.404 1.26-.607 2.063-.607zm7.673-.05c1.82 0 3.236.554 4.25 1.667 1.01 1.111 1.516 2.648 1.516 4.607v1.494h-9.018c.138 1.32.562 2.243 1.276 2.768.71.524 1.633.785 2.759.785a6.64 6.64 0 002.126-.339 6.884 6.884 0 001.794-.887v2.544c-.512.323-1.182.585-2.011.784-.829.201-1.712.301-2.653.301-2.007 0-3.566-.592-4.68-1.776-1.116-1.185-1.672-2.832-1.672-4.945 0-2.036.595-3.714 1.787-5.03 1.19-1.316 2.7-1.974 4.526-1.974v.001zm0 2.363c-.741 0-1.407.267-1.996.8-.59.531-.983 1.312-1.18 2.343h5.893c0-1.021-.236-1.8-.71-2.338-.474-.536-1.142-.805-2.007-.805z"}),i.jsx("path",{clipRule:"evenodd",d:"M12.504 3l6.915 18.325H15.91l-1.625-4.548H7.047l-1.562 4.548H2L8.916 3h3.588zm-1.806 3.4h-.09l-2.717 7.718h5.508l-2.7-7.719-.001.001zM21 8.188h10.517v1.24l-6.904 9.444h6.943v2.453H20.513v-1.47l6.802-9.213H21V8.188zm24.2 0v13.137h-3.127v-1.724h-.05a4.185 4.185 0 01-1.646 1.398c-.689.337-1.459.506-2.313.506-1.53 0-2.692-.433-3.49-1.298-.799-.864-1.2-2.208-1.2-4.032V8.188h3.141v7.617c0 1.108.219 1.94.66 2.498.439.558 1.102.837 1.99.837.88 0 1.585-.308 2.115-.926.53-.617.793-1.426.793-2.421V8.188H45.2zm8.908-.217c.247 0 .469.017.666.051.196.034.364.076.5.128v3.131c-.164-.12-.398-.232-.71-.338-.312-.107-.69-.16-1.133-.16-.761 0-1.405.32-1.93.958-.525.64-.788 1.624-.788 2.952v6.632h-3.1V8.188h3.1v2.07h.052c.28-.715.71-1.275 1.28-1.68.573-.404 1.26-.607 2.063-.607zm7.673-.05c1.82 0 3.236.554 4.25 1.667 1.01 1.111 1.516 2.648 1.516 4.607v1.494h-9.018c.138 1.32.562 2.243 1.276 2.768.71.524 1.633.785 2.759.785a6.64 6.64 0 002.126-.339 6.884 6.884 0 001.794-.887v2.544c-.512.323-1.182.585-2.011.784-.829.201-1.712.301-2.653.301-2.007 0-3.566-.592-4.68-1.776-1.116-1.185-1.672-2.832-1.672-4.945 0-2.036.595-3.714 1.787-5.03 1.19-1.316 2.7-1.974 4.526-1.974v.001zm0 2.363c-.741 0-1.407.267-1.996.8-.59.531-.983 1.312-1.18 2.343h5.893c0-1.021-.236-1.8-.71-2.338-.474-.536-1.142-.805-2.007-.805zM82.504 3l6.915 18.325H85.91l-1.625-4.548h-7.238l-1.562 4.548H72L78.916 3h3.588zm-1.806 3.4h-.09l-2.717 7.718h5.508l-2.7-7.719-.001.001zm13.015 14.925h-3.1V3h3.1v18.325z"})]}))});function A(e){"@babel/helpers - typeof";return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},A(e)}var Ur=["type"];function ze(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function Gr(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ze(Object(r),!0).forEach(function(n){kr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ze(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function kr(e,t,r){return t=Xr(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xr(e){var t=Yr(e,"string");return A(t)=="symbol"?t:String(t)}function Yr(e,t){if(A(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(A(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function qr(e,t){if(e==null)return{};var r=Qr(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Qr(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var Jr=c.memo(function(e){var t=e.type,r=t===void 0?"mono":t,n=qr(e,Ur),o=r==="color"?le:Fe;return i.jsx(G,Gr({Icon:o,Text:We,"aria-label":b,spaceMultiple:pr,textMultiple:fr},n))}),v=Fe;v.Color=le;v.Text=We;v.Combine=Jr;v.Avatar=Ar;v.colorPrimary=Ke;v.title=b;var g="Volcengine",Zr=.8,en=.2,Ue="#fff",tn=Ue,rn="#fff",nn=.75;function D(e){"@babel/helpers - typeof";return D=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},D(e)}var on=["size","style"];function Ee(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function te(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ee(Object(r),!0).forEach(function(n){sn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ee(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function sn(e,t,r){return t=cn(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cn(e){var t=ln(e,"string");return D(t)=="symbol"?t:String(t)}function ln(e,t){if(D(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(D(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function an(e,t){if(e==null)return{};var r=un(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function un(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var ae=c.memo(function(e){var t=e.size,r=t===void 0?"1em":t,n=e.style,o=an(e,on);return i.jsxs("svg",te(te({height:r,style:te({flex:"none",lineHeight:1},n),viewBox:"0 0 24 24",width:r,xmlns:"http://www.w3.org/2000/svg"},o),{},{children:[i.jsx("title",{children:g}),i.jsx("path",{d:"M19.44 10.153l-2.936 11.586a.215.215 0 00.214.261h5.87a.215.215 0 00.214-.261l-2.95-11.586a.214.214 0 00-.412 0zM3.28 12.778l-2.275 8.96A.214.214 0 001.22 22h4.532a.212.212 0 00.214-.165.214.214 0 000-.097l-2.276-8.96a.214.214 0 00-.41 0z",fill:"#00E5E5"}),i.jsx("path",{d:"M7.29 5.359L3.148 21.738a.215.215 0 00.203.261h8.29a.214.214 0 00.215-.261L7.7 5.358a.214.214 0 00-.41 0z",fill:"#006EFF"}),i.jsx("path",{d:"M14.44.15a.214.214 0 00-.41 0L8.366 21.739a.214.214 0 00.214.261H19.9a.216.216 0 00.171-.078.214.214 0 00.044-.183L14.439.15z",fill:"#006EFF"}),i.jsx("path",{d:"M10.278 7.741L6.685 21.736a.214.214 0 00.214.264h7.17a.215.215 0 00.214-.264L10.688 7.741a.214.214 0 00-.41 0z",fill:"#00E5E5"})]}))});function M(e){"@babel/helpers - typeof";return M=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(e)}function Ce(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function fn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ce(Object(r),!0).forEach(function(n){pn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ce(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function pn(e,t,r){return t=yn(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yn(e){var t=mn(e,"string");return M(t)=="symbol"?t:String(t)}function mn(e,t){if(M(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(M(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function bn(e){if(e==null)throw new TypeError("Cannot destructure "+e)}var vn=c.memo(function(e){var t=Object.assign({},(bn(e),e));return i.jsx(U,fn({Icon:ae,"aria-label":g,background:tn,color:rn,iconMultiple:nn},t))});function N(e){"@babel/helpers - typeof";return N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},N(e)}var hn=["size","style"];function Ie(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function re(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ie(Object(r),!0).forEach(function(n){jn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ie(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function jn(e,t,r){return t=dn(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dn(e){var t=gn(e,"string");return N(t)=="symbol"?t:String(t)}function gn(e,t){if(N(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(N(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function On(e,t){if(e==null)return{};var r=wn(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function wn(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var Ge=c.memo(function(e){var t=e.size,r=t===void 0?"1em":t,n=e.style,o=On(e,hn);return i.jsxs("svg",re(re({fill:"currentColor",fillRule:"evenodd",height:r,style:re({flex:"none",lineHeight:1},n),viewBox:"0 0 24 24",width:r,xmlns:"http://www.w3.org/2000/svg"},o),{},{children:[i.jsx("title",{children:g}),i.jsx("path",{d:"M7.29 5.36L3.148 21.737a.215.215 0 00.203.261h8.29a.214.214 0 00.215-.261L7.7 5.359a.214.214 0 00-.41 0z",fillOpacity:".5"}),i.jsx("path",{clipRule:"evenodd",d:"M4.553 16.18l-1.406 5.558a.214.214 0 00.203.261h2.42-4.551a.214.214 0 01-.214-.26l2.275-8.961a.214.214 0 01.409 0l.864 3.402z"}),i.jsx("path",{d:"M14.44.15a.214.214 0 00-.41 0L8.366 21.739a.214.214 0 00.214.261H19.9a.214.214 0 00.215-.261L14.44.151z",fillOpacity:".5"}),i.jsx("path",{clipRule:"evenodd",d:"M16.694 22h3.207a.215.215 0 00.214-.262l-1.839-6.993 1.164-4.592a.214.214 0 01.411 0l2.951 11.586a.214.214 0 01-.214.261h-5.894z"}),i.jsx("path",{d:"M10.278 7.741L6.685 21.736a.214.214 0 00.214.264h7.17a.216.216 0 00.214-.166.216.216 0 000-.098L10.687 7.742a.214.214 0 00-.409 0z"})]}))});function T(e){"@babel/helpers - typeof";return T=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},T(e)}var xn=["size","style"];function Ae(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function ne(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ae(Object(r),!0).forEach(function(n){Pn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ae(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Pn(e,t,r){return t=Sn(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Sn(e){var t=$n(e,"string");return T(t)=="symbol"?t:String(t)}function $n(e,t){if(T(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(T(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function _n(e,t){if(e==null)return{};var r=zn(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function zn(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var ke=c.memo(function(e){var t=e.size,r=t===void 0?"1em":t,n=e.style,o=_n(e,xn);return i.jsxs("svg",ne(ne({fill:"currentColor",fillRule:"evenodd",height:r,style:ne({flex:"none",lineHeight:1},n),viewBox:"0 0 86 24",xmlns:"http://www.w3.org/2000/svg"},o),{},{children:[i.jsx("title",{children:g}),i.jsx("path",{d:"M33.855 2.306h-2.031c-.123 0-.223.1-.223.222V21.07c0 .123.1.222.223.222h2.031c.123 0 .223-.1.223-.222V2.528c0-.122-.1-.222-.223-.222zM60.948 2.382h-2.134c-.124 0-.225.101-.225.226v19.16c0 .125.1.226.225.226h2.134c.125 0 .226-.101.226-.226V2.608c0-.125-.101-.226-.226-.226zM26.66 4.625h-2.027a.225.225 0 00-.225.226v16.78c0 .125.1.226.225.226h2.026a.225.225 0 00.225-.225V4.85c0-.125-.1-.226-.225-.226zM41.057 4.625h-2.026a.225.225 0 00-.225.226v16.78c0 .125.1.226.225.226h2.026a.225.225 0 00.226-.225V4.85c0-.125-.101-.226-.226-.226z"}),i.jsx("path",{d:"M41.166 19.383v2.06a.416.416 0 01-.417.417H24.933a.417.417 0 01-.417-.417v-2.06h16.65zM75.17 4.53V2.923a.111.111 0 00-.112-.112h-9.67a.111.111 0 00-.111.112V4.53c0 .061.05.111.111.111h9.67c.062 0 .111-.05.111-.111zM83.938 3.392V5.14a.112.112 0 01-.111.11h-6.175a.228.228 0 01-.228-.227V3.506a.228.228 0 01.228-.228h6.175a.112.112 0 01.111.111v.003zM16.846 5.074c-.037.973-.17 3.204-.63 6.717a.23.23 0 00.225.26h2.34a.232.232 0 00.225-.203c.454-3.539.571-5.773.617-6.757a.224.224 0 00-.229-.234h-2.32a.226.226 0 00-.228.217zM5.627 5.077c.04.978.171 3.227.633 6.765a.228.228 0 01-.225.263H3.678a.234.234 0 01-.228-.203c-.457-3.567-.585-5.818-.622-6.808a.228.228 0 01.23-.237h2.34a.227.227 0 01.229.22zM65.069 6.286a.118.118 0 00-.106.114v1.87a.11.11 0 00.123.114c1.675-.158 2.91-1.359 3.076-3.176a.112.112 0 00-.03-.087.114.114 0 00-.085-.036H66.17a.111.111 0 00-.111.1c-.135.964-.674 1.087-.99 1.101zM78.377 6.44h-1.932a.12.12 0 00-.089.043.118.118 0 00-.025.094c.628 3.536 3.672 5.764 7.861 5.756a.12.12 0 00.117-.117v-1.892a.114.114 0 00-.114-.114c-2.414 0-5.048-.97-5.707-3.681a.117.117 0 00-.111-.089z"}),i.jsx("path",{d:"M68.98 2.382h-2.157a.111.111 0 00-.11.112v2.25c0 .062.049.112.11.112h2.158c.061 0 .11-.05.11-.111V2.494a.111.111 0 00-.11-.112zM73.623 2.382h-2.157a.111.111 0 00-.111.112v2.25c0 .062.05.112.11.112h2.158c.062 0 .111-.05.111-.111V2.494a.111.111 0 00-.11-.112zM83.094 4.914v-.166h-2.175v.226c.02.733.066 2.448-1.101 3.66-.888.922-2.343 1.41-4.329 1.453a.114.114 0 00-.114.117l.023 1.937a.12.12 0 00.12.12c2.59-.051 4.565-.764 5.866-2.117 1.798-1.875 1.732-4.311 1.71-5.23zM79.124 2.357h-1.937a.114.114 0 00-.111.108 1.873 1.873 0 01-1.604 1.798.114.114 0 00-.097.111v1.94a.114.114 0 00.12.114 3.994 3.994 0 003.74-3.957.111.111 0 00-.11-.114zM45.186 3.053v2.03a.226.226 0 00.225.225h6.974a.108.108 0 01.109.108v2.283a.11.11 0 01-.109.111h-6.974a.226.226 0 00-.225.225v6.797a.225.225 0 00.225.225h6.974a.112.112 0 01.109.112v3.79a.224.224 0 01-.137.207.224.224 0 01-.086.018h-5.136a.226.226 0 00-.22.17l-.499 2.17a.112.112 0 00.108.136h7.77a.673.673 0 00.674-.673v-8.19a.225.225 0 00-.226-.225h-6.968a.108.108 0 01-.111-.111v-2.072a.111.111 0 01.111-.11h6.974a.225.225 0 00.225-.226v-7a.228.228 0 00-.225-.225H45.41a.226.226 0 00-.225.225zM74.57 5.632h-7.727a.112.112 0 00-.11.112v1.36a.106.106 0 00.03.08.109.109 0 00.08.032h5.739a.106.106 0 01.08.032.108.108 0 01.03.08v3.055a.112.112 0 01-.11.112h-.665a.11.11 0 00-.109.085l-.374 1.615a.113.113 0 00.022.095.112.112 0 00.087.042h2.337a.913.913 0 00.913-.913V5.858a.223.223 0 00-.223-.226zM73.358 13.62v6.417a.222.222 0 01-.226.223h-1.555a.226.226 0 00-.22.168l-.388 1.427a.114.114 0 00.112.145h4.029a.895.895 0 00.893-.896V13.62h-2.645z"}),i.jsx("path",{d:"M65.81 7.984v3.156a.224.224 0 00.226.222h5.025a.222.222 0 00.222-.222V7.984a.22.22 0 00-.064-.16.224.224 0 00-.158-.066h-5.025a.225.225 0 00-.225.226zm3.587 2.111h-1.712a.114.114 0 01-.111-.111v-.859a.114.114 0 01.111-.111h1.712a.111.111 0 01.111.111v.856a.112.112 0 01-.11.114zM83.564 14.321v-1.403a.111.111 0 00-.111-.112H65.907a.111.111 0 00-.11.112v1.403c0 .062.049.112.11.112h17.546c.061 0 .111-.05.111-.112zM83.29 16.744V15.34a.111.111 0 00-.111-.111H66.184a.111.111 0 00-.111.111v1.404c0 .061.05.111.111.111H83.18c.062 0 .111-.05.111-.111zM84.212 19.15v-1.404a.111.111 0 00-.111-.112H65.263a.111.111 0 00-.112.111v1.404c0 .062.05.112.112.112H84.1c.061 0 .111-.05.111-.112zM9.97 9.724c.1 5.678-4.823 9.476-7.756 9.673A.222.222 0 002 19.62v2.06a.224.224 0 00.143.208c.028.01.058.016.088.014 2.189-.13 4.671-1.312 6.549-3.138 1.689-1.635 3.71-4.531 3.71-9.043V2.223A.222.222 0 0012.266 2H10.2a.223.223 0 00-.223.223L9.97 9.724z"}),i.jsx("path",{d:"M12.475 9.724c-.103 5.678 4.83 9.476 7.753 9.673a.226.226 0 01.216.223v2.06a.225.225 0 01-.069.161.223.223 0 01-.165.061c-2.185-.13-4.67-1.312-6.545-3.138-1.69-1.635-3.71-4.531-3.71-9.043V2.223A.223.223 0 0110.178 2h2.066a.223.223 0 01.225.223l.006 7.501z"})]}))});function L(e){"@babel/helpers - typeof";return L=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},L(e)}var En=["type"];function De(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function Cn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?De(Object(r),!0).forEach(function(n){In(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):De(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function In(e,t,r){return t=An(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function An(e){var t=Dn(e,"string");return L(t)=="symbol"?t:String(t)}function Dn(e,t){if(L(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(L(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Mn(e,t){if(e==null)return{};var r=Nn(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Nn(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var Tn=c.memo(function(e){var t=e.type,r=t===void 0?"mono":t,n=Mn(e,En),o=r==="color"?ae:Ge;return i.jsx(G,Cn({Icon:o,Text:ke,"aria-label":g,spaceMultiple:en,textMultiple:Zr},n))}),h=Ge;h.Color=ae;h.Text=ke;h.Combine=Tn;h.Avatar=vn;h.colorPrimary=Ue;h.title=g;var O="Qingyan",Ln=.75,Vn=.2,Xe="#1041F3",Hn=Xe,Rn="#fff",Bn=.75;function V(e){"@babel/helpers - typeof";return V=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},V(e)}var Kn=["size","style"];function Me(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function oe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Me(Object(r),!0).forEach(function(n){Fn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Me(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Fn(e,t,r){return t=Wn(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Wn(e){var t=Un(e,"string");return V(t)=="symbol"?t:String(t)}function Un(e,t){if(V(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(V(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Gn(e,t){if(e==null)return{};var r=kn(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function kn(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var ue=c.memo(function(e){var t=e.size,r=t===void 0?"1em":t,n=e.style,o=Gn(e,Kn);return i.jsxs("svg",oe(oe({fill:"currentColor",fillRule:"evenodd",height:r,style:oe({flex:"none",lineHeight:1},n),viewBox:"0 0 24 24",width:r,xmlns:"http://www.w3.org/2000/svg"},o),{},{children:[i.jsx("title",{children:O}),i.jsx("path",{d:"M6.075 10.494C7.6 9.446 9.768 8.759 12.222 8.759c2.453 0 4.622.687 6.147 1.735.77.53 1.352 1.133 1.74 1.77C20 10 20 10 20.687 9.362a9.276 9.276 0 00-1.008-.8c-1.958-1.347-4.598-2.143-7.457-2.143-2.858 0-5.499.796-7.457 2.144-1.955 1.345-3.325 3.322-3.325 5.647 0 2.326 1.37 4.303 3.322 5.646C6.721 21.205 9.362 22 12.22 22c2.859 0 5.5-.795 7.457-2.144C21.63 18.513 23 16.538 23 14.21c0-1.48-.554-2.817-1.46-3.94-.046 1.036-.41 2.03-1.012 2.937.099.325.149.663.15 1.003 0 1.33-.782 2.664-2.313 3.717-1.524 1.048-3.692 1.735-6.146 1.735-2.453 0-4.623-.687-6.147-1.735C4.544 16.874 3.76 15.54 3.76 14.21c.003-1.33.785-2.663 2.315-3.716z"}),i.jsx("path",{d:"M3.747 11.494c-.62 1.77-.473 3.365.332 4.51.806 1.144 2.254 1.813 4.117 1.813 1.86 0 4.029-.68 6.021-2.1 1.993-1.42 3.35-3.251 3.967-5.017.62-1.769.473-3.364-.332-4.51-.806-1.143-2.254-1.812-4.117-1.812-1.86 0-4.029.68-6.021 2.099-1.993 1.42-3.35 3.252-3.967 5.017zm-2.228-.79c.8-2.28 2.487-4.498 4.83-6.167C8.691 2.866 11.33 2 13.734 2c2.4 0 4.678.874 6.045 2.817 1.366 1.943 1.431 4.394.633 6.674-.8 2.282-2.487 4.499-4.83 6.168-2.344 1.67-4.981 2.536-7.387 2.537-2.4 0-4.678-.874-6.045-2.817-1.368-1.943-1.431-4.396-.633-6.674h.002z"})]}))});function H(e){"@babel/helpers - typeof";return H=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},H(e)}function Ne(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function Xn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ne(Object(r),!0).forEach(function(n){Yn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ne(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Yn(e,t,r){return t=qn(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qn(e){var t=Qn(e,"string");return H(t)=="symbol"?t:String(t)}function Qn(e,t){if(H(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(H(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Jn(e){if(e==null)throw new TypeError("Cannot destructure "+e)}var Zn=c.memo(function(e){var t=Object.assign({},(Jn(e),e));return i.jsx(U,Xn({Icon:ue,"aria-label":O,background:Hn,color:Rn,iconMultiple:Bn},t))});function R(e){"@babel/helpers - typeof";return R=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},R(e)}var e1=["size","style"];function Te(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function ie(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Te(Object(r),!0).forEach(function(n){t1(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Te(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function t1(e,t,r){return t=r1(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function r1(e){var t=n1(e,"string");return R(t)=="symbol"?t:String(t)}function n1(e,t){if(R(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(R(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function o1(e,t){if(e==null)return{};var r=i1(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function i1(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var Ye=c.memo(function(e){var t=e.size,r=t===void 0?"1em":t,n=e.style,o=o1(e,e1);return i.jsxs("svg",ie(ie({height:r,style:ie({flex:"none",lineHeight:1},n),viewBox:"0 0 24 24",width:r,xmlns:"http://www.w3.org/2000/svg"},o),{},{children:[i.jsx("title",{children:O}),i.jsx("path",{d:"M6.075 10.494C7.6 9.446 9.768 8.759 12.222 8.759c2.453 0 4.622.687 6.147 1.735.77.53 1.352 1.133 1.74 1.77C20 10 20 10 20.687 9.362a9.276 9.276 0 00-1.008-.8c-1.958-1.347-4.598-2.143-7.457-2.143-2.858 0-5.499.796-7.457 2.144-1.955 1.345-3.325 3.322-3.325 5.647 0 2.326 1.37 4.303 3.322 5.646C6.721 21.205 9.362 22 12.22 22c2.859 0 5.5-.795 7.457-2.144C21.63 18.513 23 16.538 23 14.21c0-1.48-.554-2.817-1.46-3.94-.046 1.036-.41 2.03-1.012 2.937.099.325.149.663.15 1.003 0 1.33-.782 2.664-2.313 3.717-1.524 1.048-3.692 1.735-6.146 1.735-2.453 0-4.623-.687-6.147-1.735C4.544 16.874 3.76 15.54 3.76 14.21c.003-1.33.785-2.663 2.315-3.716z",fill:"#3762FF"}),i.jsx("path",{d:"M3.747 11.494c-.62 1.77-.473 3.365.332 4.51.806 1.144 2.254 1.813 4.117 1.813 1.86 0 4.029-.68 6.021-2.1 1.993-1.42 3.35-3.251 3.967-5.017.62-1.769.473-3.364-.332-4.51-.806-1.143-2.254-1.812-4.117-1.812-1.86 0-4.029.68-6.021 2.099-1.993 1.42-3.35 3.252-3.967 5.017zm-2.228-.79c.8-2.28 2.487-4.498 4.83-6.167C8.691 2.866 11.33 2 13.734 2c2.4 0 4.678.874 6.045 2.817 1.366 1.943 1.431 4.394.633 6.674-.8 2.282-2.487 4.499-4.83 6.168-2.344 1.67-4.981 2.536-7.387 2.537-2.4 0-4.678-.874-6.045-2.817-1.368-1.943-1.431-4.396-.633-6.674h.002z",fill:"#1041F3"})]}))});function B(e){"@babel/helpers - typeof";return B=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},B(e)}var s1=["size","style"];function Le(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function se(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Le(Object(r),!0).forEach(function(n){c1(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Le(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function c1(e,t,r){return t=l1(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l1(e){var t=a1(e,"string");return B(t)=="symbol"?t:String(t)}function a1(e,t){if(B(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(B(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function u1(e,t){if(e==null)return{};var r=f1(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function f1(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var qe=c.memo(function(e){var t=e.size,r=t===void 0?"1em":t,n=e.style,o=u1(e,s1);return i.jsxs("svg",se(se({fill:"currentColor",fillRule:"evenodd",height:r,style:se({flex:"none",lineHeight:1},n),viewBox:"0 0 86 24",xmlns:"http://www.w3.org/2000/svg"},o),{},{children:[i.jsx("title",{children:O}),i.jsx("path",{d:"M82.857 13.62v5.001c0 1.86-1.61 3.377-3.587 3.377H67.086V13.62h15.77zm-20.095-1.973v6.943c0 1.863-1.609 3.379-3.587 3.379h-.797v-2h.294c.655 0 1.212-.42 1.388-.991h-6.624v3.02h-2.628V11.647h11.954zm-43.606 1.236v5.738c0 1.86-1.61 3.376-3.587 3.376H3.132v-9.114h16.024zm22.387-.003v5.737c0 1.863-1.61 3.379-3.587 3.379h-8.62l.002-9.116h12.205zM26.95 6.317V18.33l1.337-.746v2.553l-3.755 1.858V8.478h-1.026v-2.16h3.444zm22.315 6.774c-.07.605-.166 1.27-.28 1.98a91.279 91.279 0 01-1.35 6.792H45.13a81.452 81.452 0 001.064-4.471 86.9 86.9 0 00.456-2.352 39.05 39.05 0 00.303-1.95h2.312zm30.95 2.527h-10.5v4.379h9.052c.797 0 1.447-.617 1.447-1.376v-3.003zm-63.701 2.644H5.762v1.735h9.557c.797 0 1.195-.618 1.195-1.376v-.359zM38.9 18.26h-6.934v1.734h5.486c.798 0 1.448-.618 1.448-1.376v-.358zm21.22-1.897h-6.682v.972h6.682v-.972zM16.51 14.884H5.76v1.736h10.752v-1.736zm22.39-.004h-6.935v1.737H38.9V14.88zm21.219-1.23h-6.682v1.068h6.682V13.65zM6.003 2.03l-.366.83h5.039v2.003h-2.91v1.501h2.987v2.001h-2.84a6.942 6.942 0 002.84 1.698v2.085a9.1 9.1 0 01-4.564-2.582 9.139 9.139 0 01-4.11 2.466l-.007-2.134A6.91 6.91 0 004.45 8.363H2.11V6.362h3.077V4.86H2l.67-1.654.147-.346h.029l.368-.83h2.79zM40.116 2c-.04.155-.096.325-.166.509-.082.216-.18.428-.296.65l-.099.188h2.832V5.21h-3.33v4.984h3.388v1.954H28.683v-1.955h3.593V5.212h-3.403V3.349h2.967l-.055-.17a10.132 10.132 0 00-.474-1.177h2.822c.116.2.215.393.306.59.1.22.191.444.272.672l.03.085h1.962l.037-.07A5.7 5.7 0 0037.212 2h2.904zm42.932 8.13v2.017h-16.15V10.13h16.15zM20.03 2.776V8.77c0 1.86-1.61 3.376-3.586 3.376H11.6v-9.37h8.43zm27.435 4.686c.32.589.643 1.286.964 2.076.304.748.593 1.502.867 2.262h-2.46a42.866 42.866 0 00-.811-2.284 27.453 27.453 0 00-.882-2.054h2.322zm10.727-5.397v.71h5.244v2h-5.244V5.79h4.439v2.003h-4.439v1.015h5.371v2H50.01v-2h5.371V7.793h-4.439V5.79h4.439V4.775h-5.244v-2h5.244v-.71h2.813zM36.821 5.21H34.49v4.982h2.331V5.21zM17.39 4.776h-3.16v5.368h1.712c.797 0 1.448-.618 1.448-1.376V4.776zm13.494 1.425c.38.928.689 1.945.917 3.028l-2.273.002a17.347 17.347 0 00-.359-1.55c-.14-.5-.301-.993-.482-1.48h2.197zm11.564-.048a13.645 13.645 0 01-.899 3.052h-2.211c.212-.489.393-.99.542-1.501.146-.51.266-1.028.359-1.55h2.209zm40.6.297v2.017H66.899V6.45h16.15zM47.507 2.065c.163.308.322.618.474.93.184.382.36.768.525 1.158a34.027 34.027 0 01.89 2.25H46.95c-.121-.356-.249-.722-.384-1.085a33.973 33.973 0 00-.987-2.403 33.74 33.74 0 00-.412-.85h2.339zm-20.921-.033c.166.447.318.9.455 1.356.171.574.329 1.152.472 1.734h-2.78a29.381 29.381 0 00-.406-1.71 28.974 28.974 0 00-.417-1.38h2.676zm49.792.033v.71H84v2H65.943v-2h7.62v-.71h2.814z"})]}))});function K(e){"@babel/helpers - typeof";return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},K(e)}var p1=["type"];function Ve(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function y1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ve(Object(r),!0).forEach(function(n){m1(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ve(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function m1(e,t,r){return t=b1(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function b1(e){var t=v1(e,"string");return K(t)=="symbol"?t:String(t)}function v1(e,t){if(K(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(K(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function h1(e,t){if(e==null)return{};var r=j1(e,t),n,o;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(o=0;o<s.length;o++)n=s[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function j1(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,s;for(s=0;s<n.length;s++)o=n[s],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}var d1=c.memo(function(e){var t=e.type,r=t===void 0?"mono":t,n=h1(e,p1),o=r==="color"?Ye:ue;return i.jsx(G,y1({Icon:o,Text:qe,"aria-label":O,spaceMultiple:Vn,textMultiple:Ln},n))}),j=ue;j.Color=Ye;j.Text=qe;j.Combine=d1;j.Avatar=Zn;j.colorPrimary=Xe;j.title=O;const{Text:g1}=He,_1=()=>{var me,be,ve,he;const{t:e,i18n:t}=Mt(),[r]=c.useContext(ft),[n,o]=c.useState(!1),[s,l]=c.useState(""),[p,u]=c.useState(!1),a=pt(),k=((me=r==null?void 0:r.status)==null?void 0:me.demo_site_enabled)||!1,fe=((be=r==null?void 0:r.status)==null?void 0:be.docs_link)||"",pe=((ve=r==null?void 0:r.status)==null?void 0:ve.server_address)||window.location.origin,X=yt.map(f=>({value:f})),[Qe,ye]=c.useState(0),Je=t.language.startsWith("zh"),Ze=async()=>{l(localStorage.getItem("home_page_content")||"");const f=await je.get("/api/home_page_content"),{success:w,message:Y,data:y}=f.data;if(w){let x=y;if(y.startsWith("https://")||(x=Nt.parse(y)),l(x),localStorage.setItem("home_page_content",x),y.startsWith("https://")){const m=document.querySelector("iframe");if(m){const tt=localStorage.getItem("theme-mode")||"light";m.onload=()=>{m.contentWindow.postMessage({themeMode:tt},"*"),m.contentWindow.postMessage({lang:t.language},"*")}}}}else It(Y),l("加载首页内容失败...");o(!0)},et=async()=>{await At(pe)&&Dt(e("已复制到剪切板"))};return c.useEffect(()=>{(async()=>{const w=localStorage.getItem("notice_close_date"),Y=new Date().toDateString();if(w!==Y)try{const y=await je.get("/api/notice"),{success:x,data:m}=y.data;x&&m&&m.trim()!==""&&u(!0)}catch(y){console.error("获取公告失败:",y)}})()},[]),c.useEffect(()=>{Ze().then()},[]),c.useEffect(()=>{const f=setInterval(()=>{ye(w=>(w+1)%X.length)},3e3);return()=>clearInterval(f)},[X.length]),i.jsxs("div",{className:"w-full overflow-x-hidden",children:[i.jsx(mt,{visible:p,onClose:()=>u(!1),isMobile:a}),n&&s===""?i.jsx("div",{className:"w-full overflow-x-hidden",children:i.jsxs("div",{className:"w-full border-b border-semi-color-border min-h-[500px] md:min-h-[600px] lg:min-h-[700px] relative overflow-x-hidden",children:[i.jsx("div",{className:"blur-ball blur-ball-indigo"}),i.jsx("div",{className:"blur-ball blur-ball-teal"}),i.jsx("div",{className:"flex items-center justify-center h-full px-4 py-20 md:py-24 lg:py-32 mt-10",children:i.jsxs("div",{className:"flex flex-col items-center justify-center text-center max-w-4xl mx-auto",children:[i.jsxs("div",{className:"flex flex-col items-center justify-center mb-6 md:mb-8",children:[i.jsx("h1",{className:`text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-semi-color-text-0 leading-tight ${Je?"tracking-wide md:tracking-wider":""}`,children:t.language==="en"?i.jsxs(i.Fragment,{children:["The Unified",i.jsx("br",{}),i.jsx("span",{className:"shine-text",children:"LLMs API Gateway"})]}):i.jsxs(i.Fragment,{children:["统一的",i.jsx("br",{}),i.jsx("span",{className:"shine-text",children:"大模型接口网关"})]})}),i.jsx("p",{className:"text-base md:text-lg lg:text-xl text-semi-color-text-1 mt-4 md:mt-6 max-w-xl",children:e("更好的价格，更好的稳定性，只需要将模型基址替换为：")}),i.jsx("div",{className:"flex flex-col md:flex-row items-center justify-center gap-4 w-full mt-4 md:mt-6 max-w-md",children:i.jsx(rt,{readonly:!0,value:pe,className:"flex-1 !rounded-full",size:a?"default":"large",suffix:i.jsxs("div",{className:"flex items-center gap-2",children:[i.jsx(nt,{bodyHeight:32,style:{border:"unset",boxShadow:"unset"},children:i.jsx(ot,{mode:"wheel",cycled:!0,list:X,selectedIndex:Qe,onSelect:({index:f})=>ye(f)})}),i.jsx(W,{type:"primary",onClick:et,icon:i.jsx(it,{}),className:"!rounded-full"})]})})})]}),i.jsxs("div",{className:"flex flex-row gap-4 justify-center items-center",children:[i.jsx(at,{to:"/console",children:i.jsx(W,{theme:"solid",type:"primary",size:a?"default":"large",className:"!rounded-3xl px-8 py-2",icon:i.jsx(st,{}),children:e("获取密钥")})}),k&&((he=r==null?void 0:r.status)!=null&&he.version)?i.jsx(W,{size:a?"default":"large",className:"flex items-center !rounded-3xl px-6 py-2",icon:i.jsx(ct,{}),onClick:()=>window.open("https://github.com/QuantumNous/new-api","_blank"),children:r.status.version}):fe&&i.jsx(W,{size:a?"default":"large",className:"flex items-center !rounded-3xl px-6 py-2",icon:i.jsx(lt,{}),onClick:()=>window.open(fe,"_blank"),children:e("文档")})]}),i.jsxs("div",{className:"mt-12 md:mt-16 lg:mt-20 w-full",children:[i.jsx("div",{className:"flex items-center mb-6 md:mb-8 justify-center",children:i.jsx(g1,{type:"tertiary",className:"text-lg md:text-xl lg:text-2xl font-light",children:e("支持众多的大模型供应商")})}),i.jsxs("div",{className:"flex flex-wrap items-center justify-center gap-3 sm:gap-4 md:gap-6 lg:gap-8 max-w-5xl mx-auto px-4",children:[i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(bt,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(vt,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(ht,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(jt.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(h.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(dt.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(gt.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(Ot.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(wt,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(xt.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(Pt.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(St.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(j.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx($t.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(_t.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(zt,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(d,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(v.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(Et.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(Ct.Color,{size:40})}),i.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 flex items-center justify-center",children:i.jsx(He.Text,{className:"!text-lg sm:!text-xl md:!text-2xl lg:!text-3xl font-bold",children:"30+"})})]})]})]})})]})}):i.jsx("div",{className:"overflow-x-hidden w-full",children:s.startsWith("https://")?i.jsx("iframe",{src:s,className:"w-full h-screen border-none"}):i.jsx("div",{className:"mt-[64px]",dangerouslySetInnerHTML:{__html:s}})})]})};export{_1 as default};
