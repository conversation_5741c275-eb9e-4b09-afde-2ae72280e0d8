# 添加机场渠道配置指南

## 方法一：通过 Web 界面添加

1. 访问 http://localhost:3000
2. 首次访问需要注册管理员账户
3. 登录后进入"渠道管理"页面
4. 点击"添加渠道"按钮

### 渠道配置参数：

- **渠道类型**: 选择 "自定义" (Custom - 类型8)
- **渠道名称**: "赚钱机场"
- **Base URL**: `https://dash.pqjc.site/api/v1/client/subscribe?token=19168228ef329e1edd063077c534ea14`
- **API Key**: 您的机场订阅token (19168228ef329e1edd063077c534ea14)
- **支持模型**: 根据您的需求配置
- **分组**: default
- **权重**: 1
- **状态**: 启用

## 方法二：通过 API 添加

使用以下 curl 命令添加渠道：

```bash
curl -X POST http://localhost:3000/api/channel/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "mode": "single",
    "channel": {
      "type": 8,
      "name": "赚钱机场",
      "key": "19168228ef329e1edd063077c534ea14",
      "base_url": "https://dash.pqjc.site/api/v1/client/subscribe",
      "models": "gpt-3.5-turbo,gpt-4",
      "group": "default",
      "weight": 1,
      "status": 1,
      "auto_ban": 1
    }
  }'
```

## 方法三：直接数据库操作

如果您想直接在数据库中添加，可以使用以下 SQL：

```sql
INSERT INTO channels (
  type, name, key, base_url, models, group, weight, status, 
  created_time, auto_ban
) VALUES (
  8, 
  '赚钱机场', 
  '19168228ef329e1edd063077c534ea14',
  'https://dash.pqjc.site/api/v1/client/subscribe',
  'gpt-3.5-turbo,gpt-4',
  'default',
  1,
  1,
  strftime('%s', 'now'),
  1
);
```

## 注意事项

1. **用途说明**: New API 主要用于 AI 模型 API 管理，不是 VPN/代理管理工具
2. **安全性**: 请确保您的订阅链接和 token 安全
3. **兼容性**: 需要确认您的机场服务是否提供 AI API 代理功能
4. **测试**: 添加后请测试渠道连通性

## 推荐配置

如果您的机场提供 AI API 代理服务，建议：

- 渠道类型：自定义 (Custom)
- 配置适当的模型列表
- 设置合理的权重和优先级
- 启用自动禁用功能以处理故障
