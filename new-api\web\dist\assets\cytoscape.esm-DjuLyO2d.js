function Bs(r,e){(e==null||e>r.length)&&(e=r.length);for(var t=0,a=Array(e);t<e;t++)a[t]=r[t];return a}function Qf(r){if(Array.isArray(r))return r}function Jf(r){if(Array.isArray(r))return Bs(r)}function dt(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function jf(r,e){for(var t=0;t<e.length;t++){var a=e[t];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(r,Ql(a.key),a)}}function ht(r,e,t){return e&&jf(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}function kr(r,e){var t=typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(!t){if(Array.isArray(r)||(t=Xs(r))||e){t&&(r=t);var a=0,n=function(){};return{s:n,n:function(){return a>=r.length?{done:!0}:{done:!1,value:r[a++]}},e:function(l){throw l},f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i,s=!0,o=!1;return{s:function(){t=t.call(r)},n:function(){var l=t.next();return s=l.done,l},e:function(l){o=!0,i=l},f:function(){try{s||t.return==null||t.return()}finally{if(o)throw i}}}}function Zl(r,e,t){return(e=Ql(e))in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function ec(r){if(typeof Symbol<"u"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function rc(r,e){var t=r==null?null:typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(t!=null){var a,n,i,s,o=[],l=!0,u=!1;try{if(i=(t=t.call(r)).next,e===0){if(Object(t)!==t)return;l=!1}else for(;!(l=(a=i.call(t)).done)&&(o.push(a.value),o.length!==e);l=!0);}catch(v){u=!0,n=v}finally{try{if(!l&&t.return!=null&&(s=t.return(),Object(s)!==s))return}finally{if(u)throw n}}return o}}function tc(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ac(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function rr(r,e){return Qf(r)||rc(r,e)||Xs(r,e)||tc()}function mn(r){return Jf(r)||ec(r)||Xs(r)||ac()}function nc(r,e){if(typeof r!="object"||!r)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var a=t.call(r,e);if(typeof a!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(r)}function Ql(r){var e=nc(r,"string");return typeof e=="symbol"?e:e+""}function ar(r){"@babel/helpers - typeof";return ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ar(r)}function Xs(r,e){if(r){if(typeof r=="string")return Bs(r,e);var t={}.toString.call(r).slice(8,-1);return t==="Object"&&r.constructor&&(t=r.constructor.name),t==="Map"||t==="Set"?Array.from(r):t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Bs(r,e):void 0}}var er=typeof window>"u"?null:window,To=er?er.navigator:null;er&&er.document;var ic=ar(""),Jl=ar({}),sc=ar(function(){}),oc=typeof HTMLElement>"u"?"undefined":ar(HTMLElement),La=function(e){return e&&e.instanceString&&$e(e.instanceString)?e.instanceString():null},de=function(e){return e!=null&&ar(e)==ic},$e=function(e){return e!=null&&ar(e)===sc},ze=function(e){return!Dr(e)&&(Array.isArray?Array.isArray(e):e!=null&&e instanceof Array)},Re=function(e){return e!=null&&ar(e)===Jl&&!ze(e)&&e.constructor===Object},uc=function(e){return e!=null&&ar(e)===Jl},te=function(e){return e!=null&&ar(e)===ar(1)&&!isNaN(e)},lc=function(e){return te(e)&&Math.floor(e)===e},bn=function(e){if(oc!=="undefined")return e!=null&&e instanceof HTMLElement},Dr=function(e){return Ia(e)||jl(e)},Ia=function(e){return La(e)==="collection"&&e._private.single},jl=function(e){return La(e)==="collection"&&!e._private.single},Ys=function(e){return La(e)==="core"},ev=function(e){return La(e)==="stylesheet"},vc=function(e){return La(e)==="event"},ot=function(e){return e==null?!0:!!(e===""||e.match(/^\s+$/))},fc=function(e){return typeof HTMLElement>"u"?!1:e instanceof HTMLElement},cc=function(e){return Re(e)&&te(e.x1)&&te(e.x2)&&te(e.y1)&&te(e.y2)},dc=function(e){return uc(e)&&$e(e.then)},hc=function(){return To&&To.userAgent.match(/msie|trident|edge/i)},Zt=function(e,t){t||(t=function(){if(arguments.length===1)return arguments[0];if(arguments.length===0)return"undefined";for(var i=[],s=0;s<arguments.length;s++)i.push(arguments[s]);return i.join("$")});var a=function(){var i=this,s=arguments,o,l=t.apply(i,s),u=a.cache;return(o=u[l])||(o=u[l]=e.apply(i,s)),o};return a.cache={},a},Zs=Zt(function(r){return r.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),Mn=Zt(function(r){return r.replace(/(-\w)/g,function(e){return e[1].toUpperCase()})}),rv=Zt(function(r,e){return r+e[0].toUpperCase()+e.substring(1)},function(r,e){return r+"$"+e}),So=function(e){return ot(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},at=function(e,t){return e.slice(-1*t.length)===t},tr="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",gc="rgb[a]?\\(("+tr+"[%]?)\\s*,\\s*("+tr+"[%]?)\\s*,\\s*("+tr+"[%]?)(?:\\s*,\\s*("+tr+"))?\\)",pc="rgb[a]?\\((?:"+tr+"[%]?)\\s*,\\s*(?:"+tr+"[%]?)\\s*,\\s*(?:"+tr+"[%]?)(?:\\s*,\\s*(?:"+tr+"))?\\)",yc="hsl[a]?\\(("+tr+")\\s*,\\s*("+tr+"[%])\\s*,\\s*("+tr+"[%])(?:\\s*,\\s*("+tr+"))?\\)",mc="hsl[a]?\\((?:"+tr+")\\s*,\\s*(?:"+tr+"[%])\\s*,\\s*(?:"+tr+"[%])(?:\\s*,\\s*(?:"+tr+"))?\\)",bc="\\#[0-9a-fA-F]{3}",wc="\\#[0-9a-fA-F]{6}",tv=function(e,t){return e<t?-1:e>t?1:0},xc=function(e,t){return-1*tv(e,t)},pe=Object.assign!=null?Object.assign.bind(Object):function(r){for(var e=arguments,t=1;t<e.length;t++){var a=e[t];if(a!=null)for(var n=Object.keys(a),i=0;i<n.length;i++){var s=n[i];r[s]=a[s]}}return r},Ec=function(e){if(!(!(e.length===4||e.length===7)||e[0]!=="#")){var t=e.length===4,a,n,i,s=16;return t?(a=parseInt(e[1]+e[1],s),n=parseInt(e[2]+e[2],s),i=parseInt(e[3]+e[3],s)):(a=parseInt(e[1]+e[2],s),n=parseInt(e[3]+e[4],s),i=parseInt(e[5]+e[6],s)),[a,n,i]}},Cc=function(e){var t,a,n,i,s,o,l,u;function v(d,m,g){return g<0&&(g+=1),g>1&&(g-=1),g<1/6?d+(m-d)*6*g:g<1/2?m:g<2/3?d+(m-d)*(2/3-g)*6:d}var f=new RegExp("^"+yc+"$").exec(e);if(f){if(a=parseInt(f[1]),a<0?a=(360- -1*a%360)%360:a>360&&(a=a%360),a/=360,n=parseFloat(f[2]),n<0||n>100||(n=n/100,i=parseFloat(f[3]),i<0||i>100)||(i=i/100,s=f[4],s!==void 0&&(s=parseFloat(s),s<0||s>1)))return;if(n===0)o=l=u=Math.round(i*255);else{var c=i<.5?i*(1+n):i+n-i*n,h=2*i-c;o=Math.round(255*v(h,c,a+1/3)),l=Math.round(255*v(h,c,a)),u=Math.round(255*v(h,c,a-1/3))}t=[o,l,u,s]}return t},Tc=function(e){var t,a=new RegExp("^"+gc+"$").exec(e);if(a){t=[];for(var n=[],i=1;i<=3;i++){var s=a[i];if(s[s.length-1]==="%"&&(n[i]=!0),s=parseFloat(s),n[i]&&(s=s/100*255),s<0||s>255)return;t.push(Math.floor(s))}var o=n[1]||n[2]||n[3],l=n[1]&&n[2]&&n[3];if(o&&!l)return;var u=a[4];if(u!==void 0){if(u=parseFloat(u),u<0||u>1)return;t.push(u)}}return t},Sc=function(e){return kc[e.toLowerCase()]},av=function(e){return(ze(e)?e:null)||Sc(e)||Ec(e)||Tc(e)||Cc(e)},kc={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},nv=function(e){for(var t=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(Re(s))throw Error("Tried to set map with object key");i<a.length-1?(t[s]==null&&(t[s]={}),t=t[s]):t[s]=e.value}},iv=function(e){for(var t=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(Re(s))throw Error("Tried to get map with object key");if(t=t[s],t==null)return t}return t},Ka=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Oa(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var Jn,ko;function Na(){if(ko)return Jn;ko=1;function r(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}return Jn=r,Jn}var jn,Do;function Dc(){if(Do)return jn;Do=1;var r=typeof Ka=="object"&&Ka&&Ka.Object===Object&&Ka;return jn=r,jn}var ei,Bo;function Ln(){if(Bo)return ei;Bo=1;var r=Dc(),e=typeof self=="object"&&self&&self.Object===Object&&self,t=r||e||Function("return this")();return ei=t,ei}var ri,Po;function Bc(){if(Po)return ri;Po=1;var r=Ln(),e=function(){return r.Date.now()};return ri=e,ri}var ti,Ao;function Pc(){if(Ao)return ti;Ao=1;var r=/\s/;function e(t){for(var a=t.length;a--&&r.test(t.charAt(a)););return a}return ti=e,ti}var ai,Ro;function Ac(){if(Ro)return ai;Ro=1;var r=Pc(),e=/^\s+/;function t(a){return a&&a.slice(0,r(a)+1).replace(e,"")}return ai=t,ai}var ni,Mo;function Qs(){if(Mo)return ni;Mo=1;var r=Ln(),e=r.Symbol;return ni=e,ni}var ii,Lo;function Rc(){if(Lo)return ii;Lo=1;var r=Qs(),e=Object.prototype,t=e.hasOwnProperty,a=e.toString,n=r?r.toStringTag:void 0;function i(s){var o=t.call(s,n),l=s[n];try{s[n]=void 0;var u=!0}catch{}var v=a.call(s);return u&&(o?s[n]=l:delete s[n]),v}return ii=i,ii}var si,Io;function Mc(){if(Io)return si;Io=1;var r=Object.prototype,e=r.toString;function t(a){return e.call(a)}return si=t,si}var oi,Oo;function sv(){if(Oo)return oi;Oo=1;var r=Qs(),e=Rc(),t=Mc(),a="[object Null]",n="[object Undefined]",i=r?r.toStringTag:void 0;function s(o){return o==null?o===void 0?n:a:i&&i in Object(o)?e(o):t(o)}return oi=s,oi}var ui,No;function Lc(){if(No)return ui;No=1;function r(e){return e!=null&&typeof e=="object"}return ui=r,ui}var li,zo;function za(){if(zo)return li;zo=1;var r=sv(),e=Lc(),t="[object Symbol]";function a(n){return typeof n=="symbol"||e(n)&&r(n)==t}return li=a,li}var vi,Fo;function Ic(){if(Fo)return vi;Fo=1;var r=Ac(),e=Na(),t=za(),a=NaN,n=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,s=/^0o[0-7]+$/i,o=parseInt;function l(u){if(typeof u=="number")return u;if(t(u))return a;if(e(u)){var v=typeof u.valueOf=="function"?u.valueOf():u;u=e(v)?v+"":v}if(typeof u!="string")return u===0?u:+u;u=r(u);var f=i.test(u);return f||s.test(u)?o(u.slice(2),f?2:8):n.test(u)?a:+u}return vi=l,vi}var fi,Vo;function Oc(){if(Vo)return fi;Vo=1;var r=Na(),e=Bc(),t=Ic(),a="Expected a function",n=Math.max,i=Math.min;function s(o,l,u){var v,f,c,h,d,m,g=0,p=!1,y=!1,b=!0;if(typeof o!="function")throw new TypeError(a);l=t(l)||0,r(u)&&(p=!!u.leading,y="maxWait"in u,c=y?n(t(u.maxWait)||0,l):c,b="trailing"in u?!!u.trailing:b);function w(B){var R=v,M=f;return v=f=void 0,g=B,h=o.apply(M,R),h}function E(B){return g=B,d=setTimeout(T,l),p?w(B):h}function C(B){var R=B-m,M=B-g,L=l-R;return y?i(L,c-M):L}function x(B){var R=B-m,M=B-g;return m===void 0||R>=l||R<0||y&&M>=c}function T(){var B=e();if(x(B))return S(B);d=setTimeout(T,C(B))}function S(B){return d=void 0,b&&v?w(B):(v=f=void 0,h)}function P(){d!==void 0&&clearTimeout(d),g=0,v=m=f=d=void 0}function D(){return d===void 0?h:S(e())}function A(){var B=e(),R=x(B);if(v=arguments,f=this,m=B,R){if(d===void 0)return E(m);if(y)return clearTimeout(d),d=setTimeout(T,l),w(m)}return d===void 0&&(d=setTimeout(T,l)),h}return A.cancel=P,A.flush=D,A}return fi=s,fi}var Nc=Oc(),Fa=Oa(Nc),ci=er?er.performance:null,ov=ci&&ci.now?function(){return ci.now()}:function(){return Date.now()},zc=function(){if(er){if(er.requestAnimationFrame)return function(r){er.requestAnimationFrame(r)};if(er.mozRequestAnimationFrame)return function(r){er.mozRequestAnimationFrame(r)};if(er.webkitRequestAnimationFrame)return function(r){er.webkitRequestAnimationFrame(r)};if(er.msRequestAnimationFrame)return function(r){er.msRequestAnimationFrame(r)}}return function(r){r&&setTimeout(function(){r(ov())},1e3/60)}}(),wn=function(e){return zc(e)},Yr=ov,Ct=9261,uv=65599,Gt=5381,lv=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ct,a=t,n;n=e.next(),!n.done;)a=a*uv+n.value|0;return a},Ca=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ct;return t*uv+e|0},Ta=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Gt;return(t<<5)+t+e|0},Fc=function(e,t){return e*2097152+t},et=function(e){return e[0]*2097152+e[1]},Xa=function(e,t){return[Ca(e[0],t[0]),Ta(e[1],t[1])]},qo=function(e,t){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e[n++]:a.done=!0,a}};return lv(s,t)},kt=function(e,t){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e.charCodeAt(n++):a.done=!0,a}};return lv(s,t)},vv=function(){return Vc(arguments)},Vc=function(e){for(var t,a=0;a<e.length;a++){var n=e[a];a===0?t=kt(n):t=kt(n,t)}return t},_o=!0,qc=console.warn!=null,_c=console.trace!=null,Js=Number.MAX_SAFE_INTEGER||9007199254740991,fv=function(){return!0},xn=function(){return!1},Go=function(){return 0},js=function(){},We=function(e){throw new Error(e)},cv=function(e){if(e!==void 0)_o=!!e;else return _o},Ie=function(e){cv()&&(qc?console.warn(e):(console.log(e),_c&&console.trace()))},Gc=function(e){return pe({},e)},qr=function(e){return e==null?e:ze(e)?e.slice():Re(e)?Gc(e):e},Hc=function(e){return e.slice()},dv=function(e,t){for(t=e="";e++<36;t+=e*51&52?(e^15?8^Math.random()*(e^20?16:4):4).toString(16):"-");return t},Wc={},hv=function(){return Wc},cr=function(e){var t=Object.keys(e);return function(a){for(var n={},i=0;i<t.length;i++){var s=t[i],o=a==null?void 0:a[s];n[s]=o===void 0?e[s]:o}return n}},ut=function(e,t,a){for(var n=e.length-1;n>=0;n--)e[n]===t&&e.splice(n,1)},eo=function(e){e.splice(0,e.length)},$c=function(e,t){for(var a=0;a<t.length;a++){var n=t[a];e.push(n)}},Tr=function(e,t,a){return a&&(t=rv(a,t)),e[t]},Kr=function(e,t,a,n){a&&(t=rv(a,t)),e[t]=n},Uc=function(){function r(){dt(this,r),this._obj={}}return ht(r,[{key:"set",value:function(t,a){return this._obj[t]=a,this}},{key:"delete",value:function(t){return this._obj[t]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(t){return this._obj[t]!==void 0}},{key:"get",value:function(t){return this._obj[t]}}])}(),Xr=typeof Map<"u"?Map:Uc,Kc="undefined",Xc=function(){function r(e){if(dt(this,r),this._obj=Object.create(null),this.size=0,e!=null){var t;e.instanceString!=null&&e.instanceString()===this.instanceString()?t=e.toArray():t=e;for(var a=0;a<t.length;a++)this.add(t[a])}}return ht(r,[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(t){var a=this._obj;a[t]!==1&&(a[t]=1,this.size++)}},{key:"delete",value:function(t){var a=this._obj;a[t]===1&&(a[t]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(t){return this._obj[t]===1}},{key:"toArray",value:function(){var t=this;return Object.keys(this._obj).filter(function(a){return t.has(a)})}},{key:"forEach",value:function(t,a){return this.toArray().forEach(t,a)}}])}(),ra=(typeof Set>"u"?"undefined":ar(Set))!==Kc?Set:Xc,In=function(e,t){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(e===void 0||t===void 0||!Ys(e)){We("An element must have a core reference and parameters set");return}var n=t.group;if(n==null&&(t.data&&t.data.source!=null&&t.data.target!=null?n="edges":n="nodes"),n!=="nodes"&&n!=="edges"){We("An element must be of type `nodes` or `edges`; you specified `"+n+"`");return}this.length=1,this[0]=this;var i=this._private={cy:e,single:!0,data:t.data||{},position:t.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:n,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!t.selected,selectable:t.selectable===void 0?!0:!!t.selectable,locked:!!t.locked,grabbed:!1,grabbable:t.grabbable===void 0?!0:!!t.grabbable,pannable:t.pannable===void 0?n==="edges":!!t.pannable,active:!1,classes:new ra,animation:{current:[],queue:[]},rscratch:{},scratch:t.scratch||{},edges:[],children:[],parent:t.parent&&t.parent.isNode()?t.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(i.position.x==null&&(i.position.x=0),i.position.y==null&&(i.position.y=0),t.renderedPosition){var s=t.renderedPosition,o=e.pan(),l=e.zoom();i.position={x:(s.x-o.x)/l,y:(s.y-o.y)/l}}var u=[];ze(t.classes)?u=t.classes:de(t.classes)&&(u=t.classes.split(/\s+/));for(var v=0,f=u.length;v<f;v++){var c=u[v];!c||c===""||i.classes.add(c)}this.createEmitter(),(a===void 0||a)&&this.restore();var h=t.style||t.css;h&&(Ie("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(h))},Ho=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(a,n,i){var s;Re(a)&&!Dr(a)&&(s=a,a=s.roots||s.root,n=s.visit,i=s.directed),i=arguments.length===2&&!$e(n)?n:i,n=$e(n)?n:function(){};for(var o=this._private.cy,l=a=de(a)?this.filter(a):a,u=[],v=[],f={},c={},h={},d=0,m,g=this.byGroup(),p=g.nodes,y=g.edges,b=0;b<l.length;b++){var w=l[b],E=w.id();w.isNode()&&(u.unshift(w),e.bfs&&(h[E]=!0,v.push(w)),c[E]=0)}for(var C=function(){var B=e.bfs?u.shift():u.pop(),R=B.id();if(e.dfs){if(h[R])return 0;h[R]=!0,v.push(B)}var M=c[R],L=f[R],I=L!=null?L.source():null,O=L!=null?L.target():null,F=L==null?void 0:B.same(I)?O[0]:I[0],_;if(_=n(B,L,F,d++,M),_===!0)return m=B,1;if(_===!1)return 1;for(var N=B.connectedEdges().filter(function(J){return(!i||J.source().same(B))&&y.has(J)}),q=0;q<N.length;q++){var U=N[q],X=U.connectedNodes().filter(function(J){return!J.same(B)&&p.has(J)}),j=X.id();X.length!==0&&!h[j]&&(X=X[0],u.push(X),e.bfs&&(h[j]=!0,v.push(X)),f[j]=U,c[j]=c[R]+1)}},x;u.length!==0&&(x=C(),!(x!==0&&x===1)););for(var T=o.collection(),S=0;S<v.length;S++){var P=v[S],D=f[P.id()];D!=null&&T.push(D),T.push(P)}return{path:o.collection(T),found:o.collection(m)}}},Sa={breadthFirstSearch:Ho({bfs:!0}),depthFirstSearch:Ho({dfs:!0})};Sa.bfs=Sa.breadthFirstSearch;Sa.dfs=Sa.depthFirstSearch;var on={exports:{}},Yc=on.exports,Wo;function Zc(){return Wo||(Wo=1,function(r,e){(function(){var t,a,n,i,s,o,l,u,v,f,c,h,d,m,g;n=Math.floor,f=Math.min,a=function(p,y){return p<y?-1:p>y?1:0},v=function(p,y,b,w,E){var C;if(b==null&&(b=0),E==null&&(E=a),b<0)throw new Error("lo must be non-negative");for(w==null&&(w=p.length);b<w;)C=n((b+w)/2),E(y,p[C])<0?w=C:b=C+1;return[].splice.apply(p,[b,b-b].concat(y)),y},o=function(p,y,b){return b==null&&(b=a),p.push(y),m(p,0,p.length-1,b)},s=function(p,y){var b,w;return y==null&&(y=a),b=p.pop(),p.length?(w=p[0],p[0]=b,g(p,0,y)):w=b,w},u=function(p,y,b){var w;return b==null&&(b=a),w=p[0],p[0]=y,g(p,0,b),w},l=function(p,y,b){var w;return b==null&&(b=a),p.length&&b(p[0],y)<0&&(w=[p[0],y],y=w[0],p[0]=w[1],g(p,0,b)),y},i=function(p,y){var b,w,E,C,x,T;for(y==null&&(y=a),C=(function(){T=[];for(var S=0,P=n(p.length/2);0<=P?S<P:S>P;0<=P?S++:S--)T.push(S);return T}).apply(this).reverse(),x=[],w=0,E=C.length;w<E;w++)b=C[w],x.push(g(p,b,y));return x},d=function(p,y,b){var w;if(b==null&&(b=a),w=p.indexOf(y),w!==-1)return m(p,0,w,b),g(p,w,b)},c=function(p,y,b){var w,E,C,x,T;if(b==null&&(b=a),E=p.slice(0,y),!E.length)return E;for(i(E,b),T=p.slice(y),C=0,x=T.length;C<x;C++)w=T[C],l(E,w,b);return E.sort(b).reverse()},h=function(p,y,b){var w,E,C,x,T,S,P,D,A;if(b==null&&(b=a),y*10<=p.length){if(C=p.slice(0,y).sort(b),!C.length)return C;for(E=C[C.length-1],P=p.slice(y),x=0,S=P.length;x<S;x++)w=P[x],b(w,E)<0&&(v(C,w,0,null,b),C.pop(),E=C[C.length-1]);return C}for(i(p,b),A=[],T=0,D=f(y,p.length);0<=D?T<D:T>D;0<=D?++T:--T)A.push(s(p,b));return A},m=function(p,y,b,w){var E,C,x;for(w==null&&(w=a),E=p[b];b>y;){if(x=b-1>>1,C=p[x],w(E,C)<0){p[b]=C,b=x;continue}break}return p[b]=E},g=function(p,y,b){var w,E,C,x,T;for(b==null&&(b=a),E=p.length,T=y,C=p[y],w=2*y+1;w<E;)x=w+1,x<E&&!(b(p[w],p[x])<0)&&(w=x),p[y]=p[w],y=w,w=2*y+1;return p[y]=C,m(p,T,y,b)},t=function(){p.push=o,p.pop=s,p.replace=u,p.pushpop=l,p.heapify=i,p.updateItem=d,p.nlargest=c,p.nsmallest=h;function p(y){this.cmp=y??a,this.nodes=[]}return p.prototype.push=function(y){return o(this.nodes,y,this.cmp)},p.prototype.pop=function(){return s(this.nodes,this.cmp)},p.prototype.peek=function(){return this.nodes[0]},p.prototype.contains=function(y){return this.nodes.indexOf(y)!==-1},p.prototype.replace=function(y){return u(this.nodes,y,this.cmp)},p.prototype.pushpop=function(y){return l(this.nodes,y,this.cmp)},p.prototype.heapify=function(){return i(this.nodes,this.cmp)},p.prototype.updateItem=function(y){return d(this.nodes,y,this.cmp)},p.prototype.clear=function(){return this.nodes=[]},p.prototype.empty=function(){return this.nodes.length===0},p.prototype.size=function(){return this.nodes.length},p.prototype.clone=function(){var y;return y=new p,y.nodes=this.nodes.slice(0),y},p.prototype.toArray=function(){return this.nodes.slice(0)},p.prototype.insert=p.prototype.push,p.prototype.top=p.prototype.peek,p.prototype.front=p.prototype.peek,p.prototype.has=p.prototype.contains,p.prototype.copy=p.prototype.clone,p}(),function(p,y){return r.exports=y()}(this,function(){return t})}).call(Yc)}(on)),on.exports}var di,$o;function Qc(){return $o||($o=1,di=Zc()),di}var Jc=Qc(),Va=Oa(Jc),jc=cr({root:null,weight:function(e){return 1},directed:!1}),ed={dijkstra:function(e){if(!Re(e)){var t=arguments;e={root:t[0],weight:t[1],directed:t[2]}}var a=jc(e),n=a.root,i=a.weight,s=a.directed,o=this,l=i,u=de(n)?this.filter(n)[0]:n[0],v={},f={},c={},h=this.byGroup(),d=h.nodes,m=h.edges;m.unmergeBy(function(M){return M.isLoop()});for(var g=function(L){return v[L.id()]},p=function(L,I){v[L.id()]=I,y.updateItem(L)},y=new Va(function(M,L){return g(M)-g(L)}),b=0;b<d.length;b++){var w=d[b];v[w.id()]=w.same(u)?0:1/0,y.push(w)}for(var E=function(L,I){for(var O=(s?L.edgesTo(I):L.edgesWith(I)).intersect(m),F=1/0,_,N=0;N<O.length;N++){var q=O[N],U=l(q);(U<F||!_)&&(F=U,_=q)}return{edge:_,dist:F}};y.size()>0;){var C=y.pop(),x=g(C),T=C.id();if(c[T]=x,x!==1/0)for(var S=C.neighborhood().intersect(d),P=0;P<S.length;P++){var D=S[P],A=D.id(),B=E(C,D),R=x+B.dist;R<g(D)&&(p(D,R),f[A]={node:C,edge:B.edge})}}return{distanceTo:function(L){var I=de(L)?d.filter(L)[0]:L[0];return c[I.id()]},pathTo:function(L){var I=de(L)?d.filter(L)[0]:L[0],O=[],F=I,_=F.id();if(I.length>0)for(O.unshift(I);f[_];){var N=f[_];O.unshift(N.edge),O.unshift(N.node),F=N.node,_=F.id()}return o.spawn(O)}}}},rd={kruskal:function(e){e=e||function(b){return 1};for(var t=this.byGroup(),a=t.nodes,n=t.edges,i=a.length,s=new Array(i),o=a,l=function(w){for(var E=0;E<s.length;E++){var C=s[E];if(C.has(w))return E}},u=0;u<i;u++)s[u]=this.spawn(a[u]);for(var v=n.sort(function(b,w){return e(b)-e(w)}),f=0;f<v.length;f++){var c=v[f],h=c.source()[0],d=c.target()[0],m=l(h),g=l(d),p=s[m],y=s[g];m!==g&&(o.merge(c),p.merge(y),s.splice(g,1))}return o}},td=cr({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),ad={aStar:function(e){var t=this.cy(),a=td(e),n=a.root,i=a.goal,s=a.heuristic,o=a.directed,l=a.weight;n=t.collection(n)[0],i=t.collection(i)[0];var u=n.id(),v=i.id(),f={},c={},h={},d=new Va(function(_,N){return c[_.id()]-c[N.id()]}),m=new ra,g={},p={},y=function(N,q){d.push(N),m.add(q)},b,w,E=function(){b=d.pop(),w=b.id(),m.delete(w)},C=function(N){return m.has(N)};y(n,u),f[u]=0,c[u]=s(n);for(var x=0;d.size()>0;){if(E(),x++,w===v){for(var T=[],S=i,P=v,D=p[P];T.unshift(S),D!=null&&T.unshift(D),S=g[P],S!=null;)P=S.id(),D=p[P];return{found:!0,distance:f[w],path:this.spawn(T),steps:x}}h[w]=!0;for(var A=b._private.edges,B=0;B<A.length;B++){var R=A[B];if(this.hasElementWithId(R.id())&&!(o&&R.data("source")!==w)){var M=R.source(),L=R.target(),I=M.id()!==w?M:L,O=I.id();if(this.hasElementWithId(O)&&!h[O]){var F=f[w]+l(R);if(!C(O)){f[O]=F,c[O]=F+s(I),y(I,O),g[O]=b,p[O]=R;continue}F<f[O]&&(f[O]=F,c[O]=F+s(I),g[O]=b,p[O]=R)}}}}return{found:!1,distance:void 0,path:void 0,steps:x}}},nd=cr({weight:function(e){return 1},directed:!1}),id={floydWarshall:function(e){for(var t=this.cy(),a=nd(e),n=a.weight,i=a.directed,s=n,o=this.byGroup(),l=o.nodes,u=o.edges,v=l.length,f=v*v,c=function(U){return l.indexOf(U)},h=function(U){return l[U]},d=new Array(f),m=0;m<f;m++){var g=m%v,p=(m-g)/v;p===g?d[m]=0:d[m]=1/0}for(var y=new Array(f),b=new Array(f),w=0;w<u.length;w++){var E=u[w],C=E.source()[0],x=E.target()[0];if(C!==x){var T=c(C),S=c(x),P=T*v+S,D=s(E);if(d[P]>D&&(d[P]=D,y[P]=S,b[P]=E),!i){var A=S*v+T;!i&&d[A]>D&&(d[A]=D,y[A]=T,b[A]=E)}}}for(var B=0;B<v;B++)for(var R=0;R<v;R++)for(var M=R*v+B,L=0;L<v;L++){var I=R*v+L,O=B*v+L;d[M]+d[O]<d[I]&&(d[I]=d[M]+d[O],y[I]=y[M])}var F=function(U){return(de(U)?t.filter(U):U)[0]},_=function(U){return c(F(U))},N={distance:function(U,X){var j=_(U),J=_(X);return d[j*v+J]},path:function(U,X){var j=_(U),J=_(X),re=h(j);if(j===J)return re.collection();if(y[j*v+J]==null)return t.collection();var ae=t.collection(),Z=j,z;for(ae.merge(re);j!==J;)Z=j,j=y[j*v+J],z=b[Z*v+j],ae.merge(z),ae.merge(h(j));return ae}};return N}},sd=cr({weight:function(e){return 1},directed:!1,root:null}),od={bellmanFord:function(e){var t=this,a=sd(e),n=a.weight,i=a.directed,s=a.root,o=n,l=this,u=this.cy(),v=this.byGroup(),f=v.edges,c=v.nodes,h=c.length,d=new Xr,m=!1,g=[];s=u.collection(s)[0],f.unmergeBy(function(Be){return Be.isLoop()});for(var p=f.length,y=function(oe){var ve=d.get(oe.id());return ve||(ve={},d.set(oe.id(),ve)),ve},b=function(oe){return(de(oe)?u.$(oe):oe)[0]},w=function(oe){return y(b(oe)).dist},E=function(oe){for(var ve=arguments.length>1&&arguments[1]!==void 0?arguments[1]:s,he=b(oe),ye=[],me=he;;){if(me==null)return t.spawn();var we=y(me),xe=we.edge,Pe=we.pred;if(ye.unshift(me[0]),me.same(ve)&&ye.length>0)break;xe!=null&&ye.unshift(xe),me=Pe}return l.spawn(ye)},C=0;C<h;C++){var x=c[C],T=y(x);x.same(s)?T.dist=0:T.dist=1/0,T.pred=null,T.edge=null}for(var S=!1,P=function(oe,ve,he,ye,me,we){var xe=ye.dist+we;xe<me.dist&&!he.same(ye.edge)&&(me.dist=xe,me.pred=oe,me.edge=he,S=!0)},D=1;D<h;D++){S=!1;for(var A=0;A<p;A++){var B=f[A],R=B.source(),M=B.target(),L=o(B),I=y(R),O=y(M);P(R,M,B,I,O,L),i||P(M,R,B,O,I,L)}if(!S)break}if(S)for(var F=[],_=0;_<p;_++){var N=f[_],q=N.source(),U=N.target(),X=o(N),j=y(q).dist,J=y(U).dist;if(j+X<J||!i&&J+X<j)if(m||(Ie("Graph contains a negative weight cycle for Bellman-Ford"),m=!0),e.findNegativeWeightCycles!==!1){var re=[];j+X<J&&re.push(q),!i&&J+X<j&&re.push(U);for(var ae=re.length,Z=0;Z<ae;Z++){var z=re[Z],G=[z];G.push(y(z).edge);for(var H=y(z).pred;G.indexOf(H)===-1;)G.push(H),G.push(y(H).edge),H=y(H).pred;G=G.slice(G.indexOf(H));for(var Q=G[0].id(),ne=0,be=2;be<G.length;be+=2)G[be].id()<Q&&(Q=G[be].id(),ne=be);G=G.slice(ne).concat(G.slice(0,ne)),G.push(G[0]);var Fe=G.map(function(Be){return Be.id()}).join(",");F.indexOf(Fe)===-1&&(g.push(l.spawn(G)),F.push(Fe))}}else break}return{distanceTo:w,pathTo:E,hasNegativeWeightCycle:m,negativeWeightCycles:g}}},ud=Math.sqrt(2),ld=function(e,t,a){a.length===0&&We("Karger-Stein must be run on a connected (sub)graph");for(var n=a[e],i=n[1],s=n[2],o=t[i],l=t[s],u=a,v=u.length-1;v>=0;v--){var f=u[v],c=f[1],h=f[2];(t[c]===o&&t[h]===l||t[c]===l&&t[h]===o)&&u.splice(v,1)}for(var d=0;d<u.length;d++){var m=u[d];m[1]===l?(u[d]=m.slice(),u[d][1]=o):m[2]===l&&(u[d]=m.slice(),u[d][2]=o)}for(var g=0;g<t.length;g++)t[g]===l&&(t[g]=o);return u},hi=function(e,t,a,n){for(;a>n;){var i=Math.floor(Math.random()*t.length);t=ld(i,e,t),a--}return t},vd={kargerStein:function(){var e=this,t=this.byGroup(),a=t.nodes,n=t.edges;n.unmergeBy(function(O){return O.isLoop()});var i=a.length,s=n.length,o=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2)),l=Math.floor(i/ud);if(i<2){We("At least 2 nodes are required for Karger-Stein algorithm");return}for(var u=[],v=0;v<s;v++){var f=n[v];u.push([v,a.indexOf(f.source()),a.indexOf(f.target())])}for(var c=1/0,h=[],d=new Array(i),m=new Array(i),g=new Array(i),p=function(F,_){for(var N=0;N<i;N++)_[N]=F[N]},y=0;y<=o;y++){for(var b=0;b<i;b++)m[b]=b;var w=hi(m,u.slice(),i,l),E=w.slice();p(m,g);var C=hi(m,w,l,2),x=hi(g,E,l,2);C.length<=x.length&&C.length<c?(c=C.length,h=C,p(m,d)):x.length<=C.length&&x.length<c&&(c=x.length,h=x,p(g,d))}for(var T=this.spawn(h.map(function(O){return n[O[0]]})),S=this.spawn(),P=this.spawn(),D=d[0],A=0;A<d.length;A++){var B=d[A],R=a[A];B===D?S.merge(R):P.merge(R)}var M=function(F){var _=e.spawn();return F.forEach(function(N){_.merge(N),N.connectedEdges().forEach(function(q){e.contains(q)&&!T.contains(q)&&_.merge(q)})}),_},L=[M(S),M(P)],I={cut:T,components:L,partition1:S,partition2:P};return I}},gi,fd=function(e){return{x:e.x,y:e.y}},On=function(e,t,a){return{x:e.x*t+a.x,y:e.y*t+a.y}},gv=function(e,t,a){return{x:(e.x-a.x)/t,y:(e.y-a.y)/t}},Ht=function(e){return{x:e[0],y:e[1]}},cd=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=1/0,i=t;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.min(s,n))}return n},dd=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=-1/0,i=t;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.max(s,n))}return n},hd=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=0,i=0,s=t;s<a;s++){var o=e[s];isFinite(o)&&(n+=o,i++)}return n/i},gd=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0;n?e=e.slice(t,a):(a<e.length&&e.splice(a,e.length-a),t>0&&e.splice(0,t));for(var o=0,l=e.length-1;l>=0;l--){var u=e[l];s?isFinite(u)||(e[l]=-1/0,o++):e.splice(l,1)}i&&e.sort(function(c,h){return c-h});var v=e.length,f=Math.floor(v/2);return v%2!==0?e[f+1+o]:(e[f-1+o]+e[f+o])/2},pd=function(e){return Math.PI*e/180},Ya=function(e,t){return Math.atan2(t,e)-Math.PI/2},ro=Math.log2||function(r){return Math.log(r)/Math.log(2)},to=function(e){return e>0?1:e<0?-1:0},Dt=function(e,t){return Math.sqrt(xt(e,t))},xt=function(e,t){var a=t.x-e.x,n=t.y-e.y;return a*a+n*n},yd=function(e){for(var t=e.length,a=0,n=0;n<t;n++)a+=e[n];for(var i=0;i<t;i++)e[i]=e[i]/a;return e},sr=function(e,t,a,n){return(1-n)*(1-n)*e+2*(1-n)*n*t+n*n*a},Ut=function(e,t,a,n){return{x:sr(e.x,t.x,a.x,n),y:sr(e.y,t.y,a.y,n)}},md=function(e,t,a,n){var i={x:t.x-e.x,y:t.y-e.y},s=Dt(e,t),o={x:i.x/s,y:i.y/s};return a=a??0,n=n??a*s,{x:e.x+o.x*n,y:e.y+o.y*n}},ka=function(e,t,a){return Math.max(e,Math.min(a,t))},wr=function(e){if(e==null)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(e.x1!=null&&e.y1!=null){if(e.x2!=null&&e.y2!=null&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(e.w!=null&&e.h!=null&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},bd=function(e){return{x1:e.x1,x2:e.x2,w:e.w,y1:e.y1,y2:e.y2,h:e.h}},wd=function(e){e.x1=1/0,e.y1=1/0,e.x2=-1/0,e.y2=-1/0,e.w=0,e.h=0},xd=function(e,t){e.x1=Math.min(e.x1,t.x1),e.x2=Math.max(e.x2,t.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,t.y1),e.y2=Math.max(e.y2,t.y2),e.h=e.y2-e.y1},pv=function(e,t,a){e.x1=Math.min(e.x1,t),e.x2=Math.max(e.x2,t),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,a),e.y2=Math.max(e.y2,a),e.h=e.y2-e.y1},un=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.x1-=t,e.x2+=t,e.y1-=t,e.y2+=t,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},ln=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[0],a,n,i,s;if(t.length===1)a=n=i=s=t[0];else if(t.length===2)a=i=t[0],s=n=t[1];else if(t.length===4){var o=rr(t,4);a=o[0],n=o[1],i=o[2],s=o[3]}return e.x1-=s,e.x2+=n,e.y1-=a,e.y2+=i,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Uo=function(e,t){e.x1=t.x1,e.y1=t.y1,e.x2=t.x2,e.y2=t.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},ao=function(e,t){return!(e.x1>t.x2||t.x1>e.x2||e.x2<t.x1||t.x2<e.x1||e.y2<t.y1||t.y2<e.y1||e.y1>t.y2||t.y1>e.y2)},Qt=function(e,t,a){return e.x1<=t&&t<=e.x2&&e.y1<=a&&a<=e.y2},Ed=function(e,t){return Qt(e,t.x,t.y)},Cd=function(e,t){return Qt(e,t.x1,t.y1)&&Qt(e,t.x2,t.y2)},Td=(gi=Math.hypot)!==null&&gi!==void 0?gi:function(r,e){return Math.sqrt(r*r+e*e)};function Sd(r,e){if(r.length<3)throw new Error("Need at least 3 vertices");var t=function(T,S){return{x:T.x+S.x,y:T.y+S.y}},a=function(T,S){return{x:T.x-S.x,y:T.y-S.y}},n=function(T,S){return{x:T.x*S,y:T.y*S}},i=function(T,S){return T.x*S.y-T.y*S.x},s=function(T){var S=Td(T.x,T.y);return S===0?{x:0,y:0}:{x:T.x/S,y:T.y/S}},o=function(T){for(var S=0,P=0;P<T.length;P++){var D=T[P],A=T[(P+1)%T.length];S+=D.x*A.y-A.x*D.y}return S/2},l=function(T,S,P,D){var A=a(S,T),B=a(D,P),R=i(A,B);if(Math.abs(R)<1e-9)return t(T,n(A,.5));var M=i(a(P,T),B)/R;return t(T,n(A,M))},u=r.map(function(x){return{x:x.x,y:x.y}});o(u)<0&&u.reverse();for(var v=u.length,f=[],c=0;c<v;c++){var h=u[c],d=u[(c+1)%v],m=a(d,h),g=s({x:m.y,y:-m.x});f.push(g)}for(var p=f.map(function(x,T){var S=t(u[T],n(x,e)),P=t(u[(T+1)%v],n(x,e));return{p1:S,p2:P}}),y=[],b=0;b<v;b++){var w=p[(b-1+v)%v],E=p[b],C=l(w.p1,w.p2,E.p1,E.p2);y.push(C)}return y}function kd(r,e,t,a,n,i){var s=Od(r,e,t,a,n),o=Sd(s,i),l=wr();return o.forEach(function(u){return pv(l,u.x,u.y)}),l}var yv=function(e,t,a,n,i,s,o){var l=arguments.length>7&&arguments[7]!==void 0?arguments[7]:"auto",u=l==="auto"?lt(i,s):l,v=i/2,f=s/2;u=Math.min(u,v,f);var c=u!==v,h=u!==f,d;if(c){var m=a-v+u-o,g=n-f-o,p=a+v-u+o,y=g;if(d=nt(e,t,a,n,m,g,p,y,!1),d.length>0)return d}if(h){var b=a+v+o,w=n-f+u-o,E=b,C=n+f-u+o;if(d=nt(e,t,a,n,b,w,E,C,!1),d.length>0)return d}if(c){var x=a-v+u-o,T=n+f+o,S=a+v-u+o,P=T;if(d=nt(e,t,a,n,x,T,S,P,!1),d.length>0)return d}if(h){var D=a-v-o,A=n-f+u-o,B=D,R=n+f-u+o;if(d=nt(e,t,a,n,D,A,B,R,!1),d.length>0)return d}var M;{var L=a-v+u,I=n-f+u;if(M=ya(e,t,a,n,L,I,u+o),M.length>0&&M[0]<=L&&M[1]<=I)return[M[0],M[1]]}{var O=a+v-u,F=n-f+u;if(M=ya(e,t,a,n,O,F,u+o),M.length>0&&M[0]>=O&&M[1]<=F)return[M[0],M[1]]}{var _=a+v-u,N=n+f-u;if(M=ya(e,t,a,n,_,N,u+o),M.length>0&&M[0]>=_&&M[1]>=N)return[M[0],M[1]]}{var q=a-v+u,U=n+f-u;if(M=ya(e,t,a,n,q,U,u+o),M.length>0&&M[0]<=q&&M[1]>=U)return[M[0],M[1]]}return[]},Dd=function(e,t,a,n,i,s,o){var l=o,u=Math.min(a,i),v=Math.max(a,i),f=Math.min(n,s),c=Math.max(n,s);return u-l<=e&&e<=v+l&&f-l<=t&&t<=c+l},Bd=function(e,t,a,n,i,s,o,l,u){var v={x1:Math.min(a,o,i)-u,x2:Math.max(a,o,i)+u,y1:Math.min(n,l,s)-u,y2:Math.max(n,l,s)+u};return!(e<v.x1||e>v.x2||t<v.y1||t>v.y2)},Pd=function(e,t,a,n){a-=n;var i=t*t-4*e*a;if(i<0)return[];var s=Math.sqrt(i),o=2*e,l=(-t+s)/o,u=(-t-s)/o;return[l,u]},Ad=function(e,t,a,n,i){var s=1e-5;e===0&&(e=s),t/=e,a/=e,n/=e;var o,l,u,v,f,c,h,d;if(l=(3*a-t*t)/9,u=-(27*n)+t*(9*a-2*(t*t)),u/=54,o=l*l*l+u*u,i[1]=0,h=t/3,o>0){f=u+Math.sqrt(o),f=f<0?-Math.pow(-f,1/3):Math.pow(f,1/3),c=u-Math.sqrt(o),c=c<0?-Math.pow(-c,1/3):Math.pow(c,1/3),i[0]=-h+f+c,h+=(f+c)/2,i[4]=i[2]=-h,h=Math.sqrt(3)*(-c+f)/2,i[3]=h,i[5]=-h;return}if(i[5]=i[3]=0,o===0){d=u<0?-Math.pow(-u,1/3):Math.pow(u,1/3),i[0]=-h+2*d,i[4]=i[2]=-(d+h);return}l=-l,v=l*l*l,v=Math.acos(u/Math.sqrt(v)),d=2*Math.sqrt(l),i[0]=-h+d*Math.cos(v/3),i[2]=-h+d*Math.cos((v+2*Math.PI)/3),i[4]=-h+d*Math.cos((v+4*Math.PI)/3)},Rd=function(e,t,a,n,i,s,o,l){var u=1*a*a-4*a*i+2*a*o+4*i*i-4*i*o+o*o+n*n-4*n*s+2*n*l+4*s*s-4*s*l+l*l,v=1*9*a*i-3*a*a-3*a*o-6*i*i+3*i*o+9*n*s-3*n*n-3*n*l-6*s*s+3*s*l,f=1*3*a*a-6*a*i+a*o-a*e+2*i*i+2*i*e-o*e+3*n*n-6*n*s+n*l-n*t+2*s*s+2*s*t-l*t,c=1*a*i-a*a+a*e-i*e+n*s-n*n+n*t-s*t,h=[];Ad(u,v,f,c,h);for(var d=1e-7,m=[],g=0;g<6;g+=2)Math.abs(h[g+1])<d&&h[g]>=0&&h[g]<=1&&m.push(h[g]);m.push(1),m.push(0);for(var p=-1,y,b,w,E=0;E<m.length;E++)y=Math.pow(1-m[E],2)*a+2*(1-m[E])*m[E]*i+m[E]*m[E]*o,b=Math.pow(1-m[E],2)*n+2*(1-m[E])*m[E]*s+m[E]*m[E]*l,w=Math.pow(y-e,2)+Math.pow(b-t,2),p>=0?w<p&&(p=w):p=w;return p},Md=function(e,t,a,n,i,s){var o=[e-a,t-n],l=[i-a,s-n],u=l[0]*l[0]+l[1]*l[1],v=o[0]*o[0]+o[1]*o[1],f=o[0]*l[0]+o[1]*l[1],c=f*f/u;return f<0?v:c>u?(e-i)*(e-i)+(t-s)*(t-s):v-c},Sr=function(e,t,a){for(var n,i,s,o,l,u=0,v=0;v<a.length/2;v++)if(n=a[v*2],i=a[v*2+1],v+1<a.length/2?(s=a[(v+1)*2],o=a[(v+1)*2+1]):(s=a[(v+1-a.length/2)*2],o=a[(v+1-a.length/2)*2+1]),!(n==e&&s==e))if(n>=e&&e>=s||n<=e&&e<=s)l=(e-n)/(s-n)*(o-i)+i,l>t&&u++;else continue;return u%2!==0},Zr=function(e,t,a,n,i,s,o,l,u){var v=new Array(a.length),f;l[0]!=null?(f=Math.atan(l[1]/l[0]),l[0]<0?f=f+Math.PI/2:f=-f-Math.PI/2):f=l;for(var c=Math.cos(-f),h=Math.sin(-f),d=0;d<v.length/2;d++)v[d*2]=s/2*(a[d*2]*c-a[d*2+1]*h),v[d*2+1]=o/2*(a[d*2+1]*c+a[d*2]*h),v[d*2]+=n,v[d*2+1]+=i;var m;if(u>0){var g=Cn(v,-u);m=En(g)}else m=v;return Sr(e,t,m)},Ld=function(e,t,a,n,i,s,o,l){for(var u=new Array(a.length*2),v=0;v<l.length;v++){var f=l[v];u[v*4+0]=f.startX,u[v*4+1]=f.startY,u[v*4+2]=f.stopX,u[v*4+3]=f.stopY;var c=Math.pow(f.cx-e,2)+Math.pow(f.cy-t,2);if(c<=Math.pow(f.radius,2))return!0}return Sr(e,t,u)},En=function(e){for(var t=new Array(e.length/2),a,n,i,s,o,l,u,v,f=0;f<e.length/4;f++){a=e[f*4],n=e[f*4+1],i=e[f*4+2],s=e[f*4+3],f<e.length/4-1?(o=e[(f+1)*4],l=e[(f+1)*4+1],u=e[(f+1)*4+2],v=e[(f+1)*4+3]):(o=e[0],l=e[1],u=e[2],v=e[3]);var c=nt(a,n,i,s,o,l,u,v,!0);t[f*2]=c[0],t[f*2+1]=c[1]}return t},Cn=function(e,t){for(var a=new Array(e.length*2),n,i,s,o,l=0;l<e.length/2;l++){n=e[l*2],i=e[l*2+1],l<e.length/2-1?(s=e[(l+1)*2],o=e[(l+1)*2+1]):(s=e[0],o=e[1]);var u=o-i,v=-(s-n),f=Math.sqrt(u*u+v*v),c=u/f,h=v/f;a[l*4]=n+c*t,a[l*4+1]=i+h*t,a[l*4+2]=s+c*t,a[l*4+3]=o+h*t}return a},Id=function(e,t,a,n,i,s){var o=a-e,l=n-t;o/=i,l/=s;var u=Math.sqrt(o*o+l*l),v=u-1;if(v<0)return[];var f=v/u;return[(a-e)*f+e,(n-t)*f+t]},St=function(e,t,a,n,i,s,o){return e-=i,t-=s,e/=a/2+o,t/=n/2+o,e*e+t*t<=1},ya=function(e,t,a,n,i,s,o){var l=[a-e,n-t],u=[e-i,t-s],v=l[0]*l[0]+l[1]*l[1],f=2*(u[0]*l[0]+u[1]*l[1]),c=u[0]*u[0]+u[1]*u[1]-o*o,h=f*f-4*v*c;if(h<0)return[];var d=(-f+Math.sqrt(h))/(2*v),m=(-f-Math.sqrt(h))/(2*v),g=Math.min(d,m),p=Math.max(d,m),y=[];if(g>=0&&g<=1&&y.push(g),p>=0&&p<=1&&y.push(p),y.length===0)return[];var b=y[0]*l[0]+e,w=y[0]*l[1]+t;if(y.length>1){if(y[0]==y[1])return[b,w];var E=y[1]*l[0]+e,C=y[1]*l[1]+t;return[b,w,E,C]}else return[b,w]},pi=function(e,t,a){return t<=e&&e<=a||a<=e&&e<=t?e:e<=t&&t<=a||a<=t&&t<=e?t:a},nt=function(e,t,a,n,i,s,o,l,u){var v=e-i,f=a-e,c=o-i,h=t-s,d=n-t,m=l-s,g=c*h-m*v,p=f*h-d*v,y=m*f-c*d;if(y!==0){var b=g/y,w=p/y,E=.001,C=0-E,x=1+E;return C<=b&&b<=x&&C<=w&&w<=x?[e+b*f,t+b*d]:u?[e+b*f,t+b*d]:[]}else return g===0||p===0?pi(e,a,o)===o?[o,l]:pi(e,a,i)===i?[i,s]:pi(i,o,a)===a?[a,n]:[]:[]},Od=function(e,t,a,n,i){var s=[],o=n/2,l=i/2,u=t,v=a;s.push({x:u+o*e[0],y:v+l*e[1]});for(var f=1;f<e.length/2;f++)s.push({x:u+o*e[f*2],y:v+l*e[f*2+1]});return s},Da=function(e,t,a,n,i,s,o,l){var u=[],v,f=new Array(a.length),c=!0;s==null&&(c=!1);var h;if(c){for(var d=0;d<f.length/2;d++)f[d*2]=a[d*2]*s+n,f[d*2+1]=a[d*2+1]*o+i;if(l>0){var m=Cn(f,-l);h=En(m)}else h=f}else h=a;for(var g,p,y,b,w=0;w<h.length/2;w++)g=h[w*2],p=h[w*2+1],w<h.length/2-1?(y=h[(w+1)*2],b=h[(w+1)*2+1]):(y=h[0],b=h[1]),v=nt(e,t,n,i,g,p,y,b),v.length!==0&&u.push(v[0],v[1]);return u},Nd=function(e,t,a,n,i,s,o,l,u){var v=[],f,c=new Array(a.length*2);u.forEach(function(y,b){b===0?(c[c.length-2]=y.startX,c[c.length-1]=y.startY):(c[b*4-2]=y.startX,c[b*4-1]=y.startY),c[b*4]=y.stopX,c[b*4+1]=y.stopY,f=ya(e,t,n,i,y.cx,y.cy,y.radius),f.length!==0&&v.push(f[0],f[1])});for(var h=0;h<c.length/4;h++)f=nt(e,t,n,i,c[h*4],c[h*4+1],c[h*4+2],c[h*4+3],!1),f.length!==0&&v.push(f[0],f[1]);if(v.length>2){for(var d=[v[0],v[1]],m=Math.pow(d[0]-e,2)+Math.pow(d[1]-t,2),g=1;g<v.length/2;g++){var p=Math.pow(v[g*2]-e,2)+Math.pow(v[g*2+1]-t,2);p<=m&&(d[0]=v[g*2],d[1]=v[g*2+1],m=p)}return d}return v},Za=function(e,t,a){var n=[e[0]-t[0],e[1]-t[1]],i=Math.sqrt(n[0]*n[0]+n[1]*n[1]),s=(i-a)/i;return s<0&&(s=1e-5),[t[0]+s*n[0],t[1]+s*n[1]]},br=function(e,t){var a=Ps(e,t);return a=mv(a),a},mv=function(e){for(var t,a,n=e.length/2,i=1/0,s=1/0,o=-1/0,l=-1/0,u=0;u<n;u++)t=e[2*u],a=e[2*u+1],i=Math.min(i,t),o=Math.max(o,t),s=Math.min(s,a),l=Math.max(l,a);for(var v=2/(o-i),f=2/(l-s),c=0;c<n;c++)t=e[2*c]=e[2*c]*v,a=e[2*c+1]=e[2*c+1]*f,i=Math.min(i,t),o=Math.max(o,t),s=Math.min(s,a),l=Math.max(l,a);if(s<-1)for(var h=0;h<n;h++)a=e[2*h+1]=e[2*h+1]+(-1-s);return e},Ps=function(e,t){var a=1/e*2*Math.PI,n=e%2===0?Math.PI/2+a/2:Math.PI/2;n+=t;for(var i=new Array(e*2),s,o=0;o<e;o++)s=o*a+n,i[2*o]=Math.cos(s),i[2*o+1]=Math.sin(-s);return i},lt=function(e,t){return Math.min(e/4,t/4,8)},bv=function(e,t){return Math.min(e/10,t/10,8)},no=function(){return 8},zd=function(e,t,a){return[e-2*t+a,2*(t-e),e]},As=function(e,t){return{heightOffset:Math.min(15,.05*t),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}};function Fd(r,e){function t(f){for(var c=[],h=0;h<f.length;h++){var d=f[h],m=f[(h+1)%f.length],g={x:m.x-d.x,y:m.y-d.y},p={x:-g.y,y:g.x},y=Math.sqrt(p.x*p.x+p.y*p.y);c.push({x:p.x/y,y:p.y/y})}return c}function a(f,c){var h=1/0,d=-1/0,m=kr(f),g;try{for(m.s();!(g=m.n()).done;){var p=g.value,y=p.x*c.x+p.y*c.y;h=Math.min(h,y),d=Math.max(d,y)}}catch(b){m.e(b)}finally{m.f()}return{min:h,max:d}}function n(f,c){return!(f.max<c.min||c.max<f.min)}var i=[].concat(mn(t(r)),mn(t(e))),s=kr(i),o;try{for(s.s();!(o=s.n()).done;){var l=o.value,u=a(r,l),v=a(e,l);if(!n(u,v))return!1}}catch(f){s.e(f)}finally{s.f()}return!0}var Vd=cr({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),qd={pageRank:function(e){for(var t=Vd(e),a=t.dampingFactor,n=t.precision,i=t.iterations,s=t.weight,o=this._private.cy,l=this.byGroup(),u=l.nodes,v=l.edges,f=u.length,c=f*f,h=v.length,d=new Array(c),m=new Array(f),g=(1-a)/f,p=0;p<f;p++){for(var y=0;y<f;y++){var b=p*f+y;d[b]=0}m[p]=0}for(var w=0;w<h;w++){var E=v[w],C=E.data("source"),x=E.data("target");if(C!==x){var T=u.indexOfId(C),S=u.indexOfId(x),P=s(E),D=S*f+T;d[D]+=P,m[T]+=P}}for(var A=1/f+g,B=0;B<f;B++)if(m[B]===0)for(var R=0;R<f;R++){var M=R*f+B;d[M]=A}else for(var L=0;L<f;L++){var I=L*f+B;d[I]=d[I]/m[B]+g}for(var O=new Array(f),F=new Array(f),_,N=0;N<f;N++)O[N]=1;for(var q=0;q<i;q++){for(var U=0;U<f;U++)F[U]=0;for(var X=0;X<f;X++)for(var j=0;j<f;j++){var J=X*f+j;F[X]+=d[J]*O[j]}yd(F),_=O,O=F,F=_;for(var re=0,ae=0;ae<f;ae++){var Z=_[ae]-O[ae];re+=Z*Z}if(re<n)break}var z={rank:function(H){return H=o.collection(H)[0],O[u.indexOf(H)]}};return z}},Ko=cr({root:null,weight:function(e){return 1},directed:!1,alpha:0}),Kt={degreeCentralityNormalized:function(e){e=Ko(e);var t=this.cy(),a=this.nodes(),n=a.length;if(e.directed){for(var v={},f={},c=0,h=0,d=0;d<n;d++){var m=a[d],g=m.id();e.root=m;var p=this.degreeCentrality(e);c<p.indegree&&(c=p.indegree),h<p.outdegree&&(h=p.outdegree),v[g]=p.indegree,f[g]=p.outdegree}return{indegree:function(b){return c==0?0:(de(b)&&(b=t.filter(b)),v[b.id()]/c)},outdegree:function(b){return h===0?0:(de(b)&&(b=t.filter(b)),f[b.id()]/h)}}}else{for(var i={},s=0,o=0;o<n;o++){var l=a[o];e.root=l;var u=this.degreeCentrality(e);s<u.degree&&(s=u.degree),i[l.id()]=u.degree}return{degree:function(b){return s===0?0:(de(b)&&(b=t.filter(b)),i[b.id()]/s)}}}},degreeCentrality:function(e){e=Ko(e);var t=this.cy(),a=this,n=e,i=n.root,s=n.weight,o=n.directed,l=n.alpha;if(i=t.collection(i)[0],o){for(var h=i.connectedEdges(),d=h.filter(function(C){return C.target().same(i)&&a.has(C)}),m=h.filter(function(C){return C.source().same(i)&&a.has(C)}),g=d.length,p=m.length,y=0,b=0,w=0;w<d.length;w++)y+=s(d[w]);for(var E=0;E<m.length;E++)b+=s(m[E]);return{indegree:Math.pow(g,1-l)*Math.pow(y,l),outdegree:Math.pow(p,1-l)*Math.pow(b,l)}}else{for(var u=i.connectedEdges().intersection(a),v=u.length,f=0,c=0;c<u.length;c++)f+=s(u[c]);return{degree:Math.pow(v,1-l)*Math.pow(f,l)}}}};Kt.dc=Kt.degreeCentrality;Kt.dcn=Kt.degreeCentralityNormalised=Kt.degreeCentralityNormalized;var Xo=cr({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),Xt={closenessCentralityNormalized:function(e){for(var t=Xo(e),a=t.harmonic,n=t.weight,i=t.directed,s=this.cy(),o={},l=0,u=this.nodes(),v=this.floydWarshall({weight:n,directed:i}),f=0;f<u.length;f++){for(var c=0,h=u[f],d=0;d<u.length;d++)if(f!==d){var m=v.distance(h,u[d]);a?c+=1/m:c+=m}a||(c=1/c),l<c&&(l=c),o[h.id()]=c}return{closeness:function(p){return l==0?0:(de(p)?p=s.filter(p)[0].id():p=p.id(),o[p]/l)}}},closenessCentrality:function(e){var t=Xo(e),a=t.root,n=t.weight,i=t.directed,s=t.harmonic;a=this.filter(a)[0];for(var o=this.dijkstra({root:a,weight:n,directed:i}),l=0,u=this.nodes(),v=0;v<u.length;v++){var f=u[v];if(!f.same(a)){var c=o.distanceTo(f);s?l+=1/c:l+=c}}return s?l:1/l}};Xt.cc=Xt.closenessCentrality;Xt.ccn=Xt.closenessCentralityNormalised=Xt.closenessCentralityNormalized;var _d=cr({weight:null,directed:!1}),Rs={betweennessCentrality:function(e){for(var t=_d(e),a=t.directed,n=t.weight,i=n!=null,s=this.cy(),o=this.nodes(),l={},u={},v=0,f={set:function(b,w){u[b]=w,w>v&&(v=w)},get:function(b){return u[b]}},c=0;c<o.length;c++){var h=o[c],d=h.id();a?l[d]=h.outgoers().nodes():l[d]=h.openNeighborhood().nodes(),f.set(d,0)}for(var m=function(){for(var b=o[g].id(),w=[],E={},C={},x={},T=new Va(function(X,j){return x[X]-x[j]}),S=0;S<o.length;S++){var P=o[S].id();E[P]=[],C[P]=0,x[P]=1/0}for(C[b]=1,x[b]=0,T.push(b);!T.empty();){var D=T.pop();if(w.push(D),i)for(var A=0;A<l[D].length;A++){var B=l[D][A],R=s.getElementById(D),M=void 0;R.edgesTo(B).length>0?M=R.edgesTo(B)[0]:M=B.edgesTo(R)[0];var L=n(M);B=B.id(),x[B]>x[D]+L&&(x[B]=x[D]+L,T.nodes.indexOf(B)<0?T.push(B):T.updateItem(B),C[B]=0,E[B]=[]),x[B]==x[D]+L&&(C[B]=C[B]+C[D],E[B].push(D))}else for(var I=0;I<l[D].length;I++){var O=l[D][I].id();x[O]==1/0&&(T.push(O),x[O]=x[D]+1),x[O]==x[D]+1&&(C[O]=C[O]+C[D],E[O].push(D))}}for(var F={},_=0;_<o.length;_++)F[o[_].id()]=0;for(;w.length>0;){for(var N=w.pop(),q=0;q<E[N].length;q++){var U=E[N][q];F[U]=F[U]+C[U]/C[N]*(1+F[N])}N!=o[g].id()&&f.set(N,f.get(N)+F[N])}},g=0;g<o.length;g++)m();var p={betweenness:function(b){var w=s.collection(b).id();return f.get(w)},betweennessNormalized:function(b){if(v==0)return 0;var w=s.collection(b).id();return f.get(w)/v}};return p.betweennessNormalised=p.betweennessNormalized,p}};Rs.bc=Rs.betweennessCentrality;var Gd=cr({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(r){return 1}]}),Hd=function(e){return Gd(e)},Wd=function(e,t){for(var a=0,n=0;n<t.length;n++)a+=t[n](e);return a},$d=function(e,t,a){for(var n=0;n<t;n++)e[n*t+n]=a},wv=function(e,t){for(var a,n=0;n<t;n++){a=0;for(var i=0;i<t;i++)a+=e[i*t+n];for(var s=0;s<t;s++)e[s*t+n]=e[s*t+n]/a}},Ud=function(e,t,a){for(var n=new Array(a*a),i=0;i<a;i++){for(var s=0;s<a;s++)n[i*a+s]=0;for(var o=0;o<a;o++)for(var l=0;l<a;l++)n[i*a+l]+=e[i*a+o]*t[o*a+l]}return n},Kd=function(e,t,a){for(var n=e.slice(0),i=1;i<a;i++)e=Ud(e,n,t);return e},Xd=function(e,t,a){for(var n=new Array(t*t),i=0;i<t*t;i++)n[i]=Math.pow(e[i],a);return wv(n,t),n},Yd=function(e,t,a,n){for(var i=0;i<a;i++){var s=Math.round(e[i]*Math.pow(10,n))/Math.pow(10,n),o=Math.round(t[i]*Math.pow(10,n))/Math.pow(10,n);if(s!==o)return!1}return!0},Zd=function(e,t,a,n){for(var i=[],s=0;s<t;s++){for(var o=[],l=0;l<t;l++)Math.round(e[s*t+l]*1e3)/1e3>0&&o.push(a[l]);o.length!==0&&i.push(n.collection(o))}return i},Qd=function(e,t){for(var a=0;a<e.length;a++)if(!t[a]||e[a].id()!==t[a].id())return!1;return!0},Jd=function(e){for(var t=0;t<e.length;t++)for(var a=0;a<e.length;a++)t!=a&&Qd(e[t],e[a])&&e.splice(a,1);return e},Yo=function(e){for(var t=this.nodes(),a=this.edges(),n=this.cy(),i=Hd(e),s={},o=0;o<t.length;o++)s[t[o].id()]=o;for(var l=t.length,u=l*l,v=new Array(u),f,c=0;c<u;c++)v[c]=0;for(var h=0;h<a.length;h++){var d=a[h],m=s[d.source().id()],g=s[d.target().id()],p=Wd(d,i.attributes);v[m*l+g]+=p,v[g*l+m]+=p}$d(v,l,i.multFactor),wv(v,l);for(var y=!0,b=0;y&&b<i.maxIterations;)y=!1,f=Kd(v,l,i.expandFactor),v=Xd(f,l,i.inflateFactor),Yd(v,f,u,4)||(y=!0),b++;var w=Zd(v,l,t,n);return w=Jd(w),w},jd={markovClustering:Yo,mcl:Yo},eh=function(e){return e},xv=function(e,t){return Math.abs(t-e)},Zo=function(e,t,a){return e+xv(t,a)},Qo=function(e,t,a){return e+Math.pow(a-t,2)},rh=function(e){return Math.sqrt(e)},th=function(e,t,a){return Math.max(e,xv(t,a))},va=function(e,t,a,n,i){for(var s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:eh,o=n,l,u,v=0;v<e;v++)l=t(v),u=a(v),o=i(o,l,u);return s(o)},Jt={euclidean:function(e,t,a){return e>=2?va(e,t,a,0,Qo,rh):va(e,t,a,0,Zo)},squaredEuclidean:function(e,t,a){return va(e,t,a,0,Qo)},manhattan:function(e,t,a){return va(e,t,a,0,Zo)},max:function(e,t,a){return va(e,t,a,-1/0,th)}};Jt["squared-euclidean"]=Jt.squaredEuclidean;Jt.squaredeuclidean=Jt.squaredEuclidean;function Nn(r,e,t,a,n,i){var s;return $e(r)?s=r:s=Jt[r]||Jt.euclidean,e===0&&$e(r)?s(n,i):s(e,t,a,n,i)}var ah=cr({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),io=function(e){return ah(e)},Tn=function(e,t,a,n,i){var s=i!=="kMedoids",o=s?function(f){return a[f]}:function(f){return n[f](a)},l=function(c){return n[c](t)},u=a,v=t;return Nn(e,n.length,o,l,u,v)},yi=function(e,t,a){for(var n=a.length,i=new Array(n),s=new Array(n),o=new Array(t),l=null,u=0;u<n;u++)i[u]=e.min(a[u]).value,s[u]=e.max(a[u]).value;for(var v=0;v<t;v++){l=[];for(var f=0;f<n;f++)l[f]=Math.random()*(s[f]-i[f])+i[f];o[v]=l}return o},Ev=function(e,t,a,n,i){for(var s=1/0,o=0,l=0;l<t.length;l++){var u=Tn(a,e,t[l],n,i);u<s&&(s=u,o=l)}return o},Cv=function(e,t,a){for(var n=[],i=null,s=0;s<t.length;s++)i=t[s],a[i.id()]===e&&n.push(i);return n},nh=function(e,t,a){return Math.abs(t-e)<=a},ih=function(e,t,a){for(var n=0;n<e.length;n++)for(var i=0;i<e[n].length;i++){var s=Math.abs(e[n][i]-t[n][i]);if(s>a)return!1}return!0},sh=function(e,t,a){for(var n=0;n<a;n++)if(e===t[n])return!0;return!1},Jo=function(e,t){var a=new Array(t);if(e.length<50)for(var n=0;n<t;n++){for(var i=e[Math.floor(Math.random()*e.length)];sh(i,a,n);)i=e[Math.floor(Math.random()*e.length)];a[n]=i}else for(var s=0;s<t;s++)a[s]=e[Math.floor(Math.random()*e.length)];return a},jo=function(e,t,a){for(var n=0,i=0;i<t.length;i++)n+=Tn("manhattan",t[i],e,a,"kMedoids");return n},oh=function(e){var t=this.cy(),a=this.nodes(),n=null,i=io(e),s=new Array(i.k),o={},l;i.testMode?typeof i.testCentroids=="number"?(i.testCentroids,l=yi(a,i.k,i.attributes)):ar(i.testCentroids)==="object"?l=i.testCentroids:l=yi(a,i.k,i.attributes):l=yi(a,i.k,i.attributes);for(var u=!0,v=0;u&&v<i.maxIterations;){for(var f=0;f<a.length;f++)n=a[f],o[n.id()]=Ev(n,l,i.distance,i.attributes,"kMeans");u=!1;for(var c=0;c<i.k;c++){var h=Cv(c,a,o);if(h.length!==0){for(var d=i.attributes.length,m=l[c],g=new Array(d),p=new Array(d),y=0;y<d;y++){p[y]=0;for(var b=0;b<h.length;b++)n=h[b],p[y]+=i.attributes[y](n);g[y]=p[y]/h.length,nh(g[y],m[y],i.sensitivityThreshold)||(u=!0)}l[c]=g,s[c]=t.collection(h)}}v++}return s},uh=function(e){var t=this.cy(),a=this.nodes(),n=null,i=io(e),s=new Array(i.k),o,l={},u,v=new Array(i.k);i.testMode?typeof i.testCentroids=="number"||(ar(i.testCentroids)==="object"?o=i.testCentroids:o=Jo(a,i.k)):o=Jo(a,i.k);for(var f=!0,c=0;f&&c<i.maxIterations;){for(var h=0;h<a.length;h++)n=a[h],l[n.id()]=Ev(n,o,i.distance,i.attributes,"kMedoids");f=!1;for(var d=0;d<o.length;d++){var m=Cv(d,a,l);if(m.length!==0){v[d]=jo(o[d],m,i.attributes);for(var g=0;g<m.length;g++)u=jo(m[g],m,i.attributes),u<v[d]&&(v[d]=u,o[d]=m[g],f=!0);s[d]=t.collection(m)}}c++}return s},lh=function(e,t,a,n,i){for(var s,o,l=0;l<t.length;l++)for(var u=0;u<e.length;u++)n[l][u]=Math.pow(a[l][u],i.m);for(var v=0;v<e.length;v++)for(var f=0;f<i.attributes.length;f++){s=0,o=0;for(var c=0;c<t.length;c++)s+=n[c][v]*i.attributes[f](t[c]),o+=n[c][v];e[v][f]=s/o}},vh=function(e,t,a,n,i){for(var s=0;s<e.length;s++)t[s]=e[s].slice();for(var o,l,u,v=2/(i.m-1),f=0;f<a.length;f++)for(var c=0;c<n.length;c++){o=0;for(var h=0;h<a.length;h++)l=Tn(i.distance,n[c],a[f],i.attributes,"cmeans"),u=Tn(i.distance,n[c],a[h],i.attributes,"cmeans"),o+=Math.pow(l/u,v);e[c][f]=1/o}},fh=function(e,t,a,n){for(var i=new Array(a.k),s=0;s<i.length;s++)i[s]=[];for(var o,l,u=0;u<t.length;u++){o=-1/0,l=-1;for(var v=0;v<t[0].length;v++)t[u][v]>o&&(o=t[u][v],l=v);i[l].push(e[u])}for(var f=0;f<i.length;f++)i[f]=n.collection(i[f]);return i},eu=function(e){var t=this.cy(),a=this.nodes(),n=io(e),i,s,o,l,u;l=new Array(a.length);for(var v=0;v<a.length;v++)l[v]=new Array(n.k);o=new Array(a.length);for(var f=0;f<a.length;f++)o[f]=new Array(n.k);for(var c=0;c<a.length;c++){for(var h=0,d=0;d<n.k;d++)o[c][d]=Math.random(),h+=o[c][d];for(var m=0;m<n.k;m++)o[c][m]=o[c][m]/h}s=new Array(n.k);for(var g=0;g<n.k;g++)s[g]=new Array(n.attributes.length);u=new Array(a.length);for(var p=0;p<a.length;p++)u[p]=new Array(n.k);for(var y=!0,b=0;y&&b<n.maxIterations;)y=!1,lh(s,a,o,u,n),vh(o,l,s,a,n),ih(o,l,n.sensitivityThreshold)||(y=!0),b++;return i=fh(a,o,n,t),{clusters:i,degreeOfMembership:o}},ch={kMeans:oh,kMedoids:uh,fuzzyCMeans:eu,fcm:eu},dh=cr({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),hh={single:"min",complete:"max"},gh=function(e){var t=dh(e),a=hh[t.linkage];return a!=null&&(t.linkage=a),t},ru=function(e,t,a,n,i){for(var s=0,o=1/0,l,u=i.attributes,v=function(S,P){return Nn(i.distance,u.length,function(D){return u[D](S)},function(D){return u[D](P)},S,P)},f=0;f<e.length;f++){var c=e[f].key,h=a[c][n[c]];h<o&&(s=c,o=h)}if(i.mode==="threshold"&&o>=i.threshold||i.mode==="dendrogram"&&e.length===1)return!1;var d=t[s],m=t[n[s]],g;i.mode==="dendrogram"?g={left:d,right:m,key:d.key}:g={value:d.value.concat(m.value),key:d.key},e[d.index]=g,e.splice(m.index,1),t[d.key]=g;for(var p=0;p<e.length;p++){var y=e[p];d.key===y.key?l=1/0:i.linkage==="min"?(l=a[d.key][y.key],a[d.key][y.key]>a[m.key][y.key]&&(l=a[m.key][y.key])):i.linkage==="max"?(l=a[d.key][y.key],a[d.key][y.key]<a[m.key][y.key]&&(l=a[m.key][y.key])):i.linkage==="mean"?l=(a[d.key][y.key]*d.size+a[m.key][y.key]*m.size)/(d.size+m.size):i.mode==="dendrogram"?l=v(y.value,d.value):l=v(y.value[0],d.value[0]),a[d.key][y.key]=a[y.key][d.key]=l}for(var b=0;b<e.length;b++){var w=e[b].key;if(n[w]===d.key||n[w]===m.key){for(var E=w,C=0;C<e.length;C++){var x=e[C].key;a[w][x]<a[w][E]&&(E=x)}n[w]=E}e[b].index=b}return d.key=m.key=d.index=m.index=null,!0},Wt=function(e,t,a){e&&(e.value?t.push(e.value):(e.left&&Wt(e.left,t),e.right&&Wt(e.right,t)))},Ms=function(e,t){if(!e)return"";if(e.left&&e.right){var a=Ms(e.left,t),n=Ms(e.right,t),i=t.add({group:"nodes",data:{id:a+","+n}});return t.add({group:"edges",data:{source:a,target:i.id()}}),t.add({group:"edges",data:{source:n,target:i.id()}}),i.id()}else if(e.value)return e.value.id()},Ls=function(e,t,a){if(!e)return[];var n=[],i=[],s=[];return t===0?(e.left&&Wt(e.left,n),e.right&&Wt(e.right,i),s=n.concat(i),[a.collection(s)]):t===1?e.value?[a.collection(e.value)]:(e.left&&Wt(e.left,n),e.right&&Wt(e.right,i),[a.collection(n),a.collection(i)]):e.value?[a.collection(e.value)]:(e.left&&(n=Ls(e.left,t-1,a)),e.right&&(i=Ls(e.right,t-1,a)),n.concat(i))},tu=function(e){for(var t=this.cy(),a=this.nodes(),n=gh(e),i=n.attributes,s=function(b,w){return Nn(n.distance,i.length,function(E){return i[E](b)},function(E){return i[E](w)},b,w)},o=[],l=[],u=[],v=[],f=0;f<a.length;f++){var c={value:n.mode==="dendrogram"?a[f]:[a[f]],key:f,index:f};o[f]=c,v[f]=c,l[f]=[],u[f]=0}for(var h=0;h<o.length;h++)for(var d=0;d<=h;d++){var m=void 0;n.mode==="dendrogram"?m=h===d?1/0:s(o[h].value,o[d].value):m=h===d?1/0:s(o[h].value[0],o[d].value[0]),l[h][d]=m,l[d][h]=m,m<l[h][u[h]]&&(u[h]=d)}for(var g=ru(o,v,l,u,n);g;)g=ru(o,v,l,u,n);var p;return n.mode==="dendrogram"?(p=Ls(o[0],n.dendrogramDepth,t),n.addDendrogram&&Ms(o[0],t)):(p=new Array(o.length),o.forEach(function(y,b){y.key=y.index=null,p[b]=t.collection(y.value)})),p},ph={hierarchicalClustering:tu,hca:tu},yh=cr({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),mh=function(e){var t=e.damping,a=e.preference;.5<=t&&t<1||We("Damping must range on [0.5, 1).  Got: ".concat(t));var n=["median","mean","min","max"];return n.some(function(i){return i===a})||te(a)||We("Preference must be one of [".concat(n.map(function(i){return"'".concat(i,"'")}).join(", "),"] or a number.  Got: ").concat(a)),yh(e)},bh=function(e,t,a,n){var i=function(o,l){return n[l](o)};return-Nn(e,n.length,function(s){return i(t,s)},function(s){return i(a,s)},t,a)},wh=function(e,t){var a=null;return t==="median"?a=gd(e):t==="mean"?a=hd(e):t==="min"?a=cd(e):t==="max"?a=dd(e):a=t,a},xh=function(e,t,a){for(var n=[],i=0;i<e;i++)t[i*e+i]+a[i*e+i]>0&&n.push(i);return n},au=function(e,t,a){for(var n=[],i=0;i<e;i++){for(var s=-1,o=-1/0,l=0;l<a.length;l++){var u=a[l];t[i*e+u]>o&&(s=u,o=t[i*e+u])}s>0&&n.push(s)}for(var v=0;v<a.length;v++)n[a[v]]=a[v];return n},Eh=function(e,t,a){for(var n=au(e,t,a),i=0;i<a.length;i++){for(var s=[],o=0;o<n.length;o++)n[o]===a[i]&&s.push(o);for(var l=-1,u=-1/0,v=0;v<s.length;v++){for(var f=0,c=0;c<s.length;c++)f+=t[s[c]*e+s[v]];f>u&&(l=v,u=f)}a[i]=s[l]}return n=au(e,t,a),n},nu=function(e){for(var t=this.cy(),a=this.nodes(),n=mh(e),i={},s=0;s<a.length;s++)i[a[s].id()]=s;var o,l,u,v,f,c;o=a.length,l=o*o,u=new Array(l);for(var h=0;h<l;h++)u[h]=-1/0;for(var d=0;d<o;d++)for(var m=0;m<o;m++)d!==m&&(u[d*o+m]=bh(n.distance,a[d],a[m],n.attributes));v=wh(u,n.preference);for(var g=0;g<o;g++)u[g*o+g]=v;f=new Array(l);for(var p=0;p<l;p++)f[p]=0;c=new Array(l);for(var y=0;y<l;y++)c[y]=0;for(var b=new Array(o),w=new Array(o),E=new Array(o),C=0;C<o;C++)b[C]=0,w[C]=0,E[C]=0;for(var x=new Array(o*n.minIterations),T=0;T<x.length;T++)x[T]=0;var S;for(S=0;S<n.maxIterations;S++){for(var P=0;P<o;P++){for(var D=-1/0,A=-1/0,B=-1,R=0,M=0;M<o;M++)b[M]=f[P*o+M],R=c[P*o+M]+u[P*o+M],R>=D?(A=D,D=R,B=M):R>A&&(A=R);for(var L=0;L<o;L++)f[P*o+L]=(1-n.damping)*(u[P*o+L]-D)+n.damping*b[L];f[P*o+B]=(1-n.damping)*(u[P*o+B]-A)+n.damping*b[B]}for(var I=0;I<o;I++){for(var O=0,F=0;F<o;F++)b[F]=c[F*o+I],w[F]=Math.max(0,f[F*o+I]),O+=w[F];O-=w[I],w[I]=f[I*o+I],O+=w[I];for(var _=0;_<o;_++)c[_*o+I]=(1-n.damping)*Math.min(0,O-w[_])+n.damping*b[_];c[I*o+I]=(1-n.damping)*(O-w[I])+n.damping*b[I]}for(var N=0,q=0;q<o;q++){var U=c[q*o+q]+f[q*o+q]>0?1:0;x[S%n.minIterations*o+q]=U,N+=U}if(N>0&&(S>=n.minIterations-1||S==n.maxIterations-1)){for(var X=0,j=0;j<o;j++){E[j]=0;for(var J=0;J<n.minIterations;J++)E[j]+=x[J*o+j];(E[j]===0||E[j]===n.minIterations)&&X++}if(X===o)break}}for(var re=xh(o,f,c),ae=Eh(o,u,re),Z={},z=0;z<re.length;z++)Z[re[z]]=[];for(var G=0;G<a.length;G++){var H=i[a[G].id()],Q=ae[H];Q!=null&&Z[Q].push(a[G])}for(var ne=new Array(re.length),be=0;be<re.length;be++)ne[be]=t.collection(Z[re[be]]);return ne},Ch={affinityPropagation:nu,ap:nu},Th=cr({root:void 0,directed:!1}),Sh={hierholzer:function(e){if(!Re(e)){var t=arguments;e={root:t[0],directed:t[1]}}var a=Th(e),n=a.root,i=a.directed,s=this,o=!1,l,u,v;n&&(v=de(n)?this.filter(n)[0].id():n[0].id());var f={},c={};i?s.forEach(function(y){var b=y.id();if(y.isNode()){var w=y.indegree(!0),E=y.outdegree(!0),C=w-E,x=E-w;C==1?l?o=!0:l=b:x==1?u?o=!0:u=b:(x>1||C>1)&&(o=!0),f[b]=[],y.outgoers().forEach(function(T){T.isEdge()&&f[b].push(T.id())})}else c[b]=[void 0,y.target().id()]}):s.forEach(function(y){var b=y.id();if(y.isNode()){var w=y.degree(!0);w%2&&(l?u?o=!0:u=b:l=b),f[b]=[],y.connectedEdges().forEach(function(E){return f[b].push(E.id())})}else c[b]=[y.source().id(),y.target().id()]});var h={found:!1,trail:void 0};if(o)return h;if(u&&l)if(i){if(v&&u!=v)return h;v=u}else{if(v&&u!=v&&l!=v)return h;v||(v=u)}else v||(v=s[0].id());var d=function(b){for(var w=b,E=[b],C,x,T;f[w].length;)C=f[w].shift(),x=c[C][0],T=c[C][1],w!=T?(f[T]=f[T].filter(function(S){return S!=C}),w=T):!i&&w!=x&&(f[x]=f[x].filter(function(S){return S!=C}),w=x),E.unshift(C),E.unshift(w);return E},m=[],g=[];for(g=d(v);g.length!=1;)f[g[0]].length==0?(m.unshift(s.getElementById(g.shift())),m.unshift(s.getElementById(g.shift()))):g=d(g.shift()).concat(g);m.unshift(s.getElementById(g.shift()));for(var p in f)if(f[p].length)return h;return h.found=!0,h.trail=this.spawn(m,!0),h}},Qa=function(){var e=this,t={},a=0,n=0,i=[],s=[],o={},l=function(c,h){for(var d=s.length-1,m=[],g=e.spawn();s[d].x!=c||s[d].y!=h;)m.push(s.pop().edge),d--;m.push(s.pop().edge),m.forEach(function(p){var y=p.connectedNodes().intersection(e);g.merge(p),y.forEach(function(b){var w=b.id(),E=b.connectedEdges().intersection(e);g.merge(b),t[w].cutVertex?g.merge(E.filter(function(C){return C.isLoop()})):g.merge(E)})}),i.push(g)},u=function(c,h,d){c===d&&(n+=1),t[h]={id:a,low:a++,cutVertex:!1};var m=e.getElementById(h).connectedEdges().intersection(e);if(m.size()===0)i.push(e.spawn(e.getElementById(h)));else{var g,p,y,b;m.forEach(function(w){g=w.source().id(),p=w.target().id(),y=g===h?p:g,y!==d&&(b=w.id(),o[b]||(o[b]=!0,s.push({x:h,y,edge:w})),y in t?t[h].low=Math.min(t[h].low,t[y].id):(u(c,y,h),t[h].low=Math.min(t[h].low,t[y].low),t[h].id<=t[y].low&&(t[h].cutVertex=!0,l(h,y))))})}};e.forEach(function(f){if(f.isNode()){var c=f.id();c in t||(n=0,u(c,c),t[c].cutVertex=n>1)}});var v=Object.keys(t).filter(function(f){return t[f].cutVertex}).map(function(f){return e.getElementById(f)});return{cut:e.spawn(v),components:i}},kh={hopcroftTarjanBiconnected:Qa,htbc:Qa,htb:Qa,hopcroftTarjanBiconnectedComponents:Qa},Ja=function(){var e=this,t={},a=0,n=[],i=[],s=e.spawn(e),o=function(u){i.push(u),t[u]={index:a,low:a++,explored:!1};var v=e.getElementById(u).connectedEdges().intersection(e);if(v.forEach(function(m){var g=m.target().id();g!==u&&(g in t||o(g),t[g].explored||(t[u].low=Math.min(t[u].low,t[g].low)))}),t[u].index===t[u].low){for(var f=e.spawn();;){var c=i.pop();if(f.merge(e.getElementById(c)),t[c].low=t[u].index,t[c].explored=!0,c===u)break}var h=f.edgesWith(f),d=f.merge(h);n.push(d),s=s.difference(d)}};return e.forEach(function(l){if(l.isNode()){var u=l.id();u in t||o(u)}}),{cut:s,components:n}},Dh={tarjanStronglyConnected:Ja,tsc:Ja,tscc:Ja,tarjanStronglyConnectedComponents:Ja},Tv={};[Sa,ed,rd,ad,id,od,vd,qd,Kt,Xt,Rs,jd,ch,ph,Ch,Sh,kh,Dh].forEach(function(r){pe(Tv,r)});/*!
Embeddable Minimum Strictly-Compliant Promises/A+ 1.1.1 Thenable
Copyright (c) 2013-2014 Ralf S. Engelschall (http://engelschall.com)
Licensed under The MIT License (http://opensource.org/licenses/MIT)
*/var Sv=0,kv=1,Dv=2,Nr=function(e){if(!(this instanceof Nr))return new Nr(e);this.id="Thenable/1.0.7",this.state=Sv,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},typeof e=="function"&&e.call(this,this.fulfill.bind(this),this.reject.bind(this))};Nr.prototype={fulfill:function(e){return iu(this,kv,"fulfillValue",e)},reject:function(e){return iu(this,Dv,"rejectReason",e)},then:function(e,t){var a=this,n=new Nr;return a.onFulfilled.push(ou(e,n,"fulfill")),a.onRejected.push(ou(t,n,"reject")),Bv(a),n.proxy}};var iu=function(e,t,a,n){return e.state===Sv&&(e.state=t,e[a]=n,Bv(e)),e},Bv=function(e){e.state===kv?su(e,"onFulfilled",e.fulfillValue):e.state===Dv&&su(e,"onRejected",e.rejectReason)},su=function(e,t,a){if(e[t].length!==0){var n=e[t];e[t]=[];var i=function(){for(var o=0;o<n.length;o++)n[o](a)};typeof setImmediate=="function"?setImmediate(i):setTimeout(i,0)}},ou=function(e,t,a){return function(n){if(typeof e!="function")t[a].call(t,n);else{var i;try{i=e(n)}catch(s){t.reject(s);return}Pv(t,i)}}},Pv=function(e,t){if(e===t||e.proxy===t){e.reject(new TypeError("cannot resolve promise with itself"));return}var a;if(ar(t)==="object"&&t!==null||typeof t=="function")try{a=t.then}catch(i){e.reject(i);return}if(typeof a=="function"){var n=!1;try{a.call(t,function(i){n||(n=!0,i===t?e.reject(new TypeError("circular thenable chain")):Pv(e,i))},function(i){n||(n=!0,e.reject(i))})}catch(i){n||e.reject(i)}return}e.fulfill(t)};Nr.all=function(r){return new Nr(function(e,t){for(var a=new Array(r.length),n=0,i=function(l,u){a[l]=u,n++,n===r.length&&e(a)},s=0;s<r.length;s++)(function(o){var l=r[o],u=l!=null&&l.then!=null;if(u)l.then(function(f){i(o,f)},function(f){t(f)});else{var v=l;i(o,v)}})(s)})};Nr.resolve=function(r){return new Nr(function(e,t){e(r)})};Nr.reject=function(r){return new Nr(function(e,t){t(r)})};var ta=typeof Promise<"u"?Promise:Nr,Is=function(e,t,a){var n=Ys(e),i=!n,s=this._private=pe({duration:1e3},t,a);if(s.target=e,s.style=s.style||s.css,s.started=!1,s.playing=!1,s.hooked=!1,s.applying=!1,s.progress=0,s.completes=[],s.frames=[],s.complete&&$e(s.complete)&&s.completes.push(s.complete),i){var o=e.position();s.startPosition=s.startPosition||{x:o.x,y:o.y},s.startStyle=s.startStyle||e.cy().style().getAnimationStartStyle(e,s.style)}if(n){var l=e.pan();s.startPan={x:l.x,y:l.y},s.startZoom=e.zoom()}this.length=1,this[0]=this},Bt=Is.prototype;pe(Bt,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var t,a=e.target._private.animation;e.queue?t=a.queue:t=a.current,t.push(this),Dr(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return e.progress===1&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var t=this._private;return e===void 0?t.progress*t.duration:this.progress(e/t.duration)},progress:function(e){var t=this._private,a=t.playing;return e===void 0?t.progress:(a&&this.pause(),t.progress=e,t.started=!1,a&&this.play(),this)},completed:function(){return this._private.progress===1},reverse:function(){var e=this._private,t=e.playing;t&&this.pause(),e.progress=1-e.progress,e.started=!1;var a=function(u,v){var f=e[u];f!=null&&(e[u]=e[v],e[v]=f)};if(a("zoom","startZoom"),a("pan","startPan"),a("position","startPosition"),e.style)for(var n=0;n<e.style.length;n++){var i=e.style[n],s=i.name,o=e.startStyle[s];e.startStyle[s]=i,e.style[n]=o}return t&&this.play(),this},promise:function(e){var t=this._private,a;switch(e){case"frame":a=t.frames;break;default:case"complete":case"completed":a=t.completes}return new ta(function(n,i){a.push(function(){n()})})}});Bt.complete=Bt.completed;Bt.run=Bt.play;Bt.running=Bt.playing;var Bh={animated:function(){return function(){var t=this,a=t.length!==void 0,n=a?t:[t],i=this._private.cy||this;if(!i.styleEnabled())return!1;var s=n[0];if(s)return s._private.animation.current.length>0}},clearQueue:function(){return function(){var t=this,a=t.length!==void 0,n=a?t:[t],i=this._private.cy||this;if(!i.styleEnabled())return this;for(var s=0;s<n.length;s++){var o=n[s];o._private.animation.queue=[]}return this}},delay:function(){return function(t,a){var n=this._private.cy||this;return n.styleEnabled()?this.animate({delay:t,duration:t,complete:a}):this}},delayAnimation:function(){return function(t,a){var n=this._private.cy||this;return n.styleEnabled()?this.animation({delay:t,duration:t,complete:a}):this}},animation:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this,l=!i,u=!l;if(!o.styleEnabled())return this;var v=o.style();t=pe({},t,a);var f=Object.keys(t).length===0;if(f)return new Is(s[0],t);switch(t.duration===void 0&&(t.duration=400),t.duration){case"slow":t.duration=600;break;case"fast":t.duration=200;break}if(u&&(t.style=v.getPropsList(t.style||t.css),t.css=void 0),u&&t.renderedPosition!=null){var c=t.renderedPosition,h=o.pan(),d=o.zoom();t.position=gv(c,d,h)}if(l&&t.panBy!=null){var m=t.panBy,g=o.pan();t.pan={x:g.x+m.x,y:g.y+m.y}}var p=t.center||t.centre;if(l&&p!=null){var y=o.getCenterPan(p.eles,t.zoom);y!=null&&(t.pan=y)}if(l&&t.fit!=null){var b=t.fit,w=o.getFitViewport(b.eles||b.boundingBox,b.padding);w!=null&&(t.pan=w.pan,t.zoom=w.zoom)}if(l&&Re(t.zoom)){var E=o.getZoomedViewport(t.zoom);E!=null?(E.zoomed&&(t.zoom=E.zoom),E.panned&&(t.pan=E.pan)):t.zoom=null}return new Is(s[0],t)}},animate:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;a&&(t=pe({},t,a));for(var l=0;l<s.length;l++){var u=s[l],v=u.animated()&&(t.queue===void 0||t.queue),f=u.animation(t,v?{queue:!0}:void 0);f.play()}return this}},stop:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;for(var l=0;l<s.length;l++){for(var u=s[l],v=u._private,f=v.animation.current,c=0;c<f.length;c++){var h=f[c],d=h._private;a&&(d.duration=0)}t&&(v.animation.queue=[]),a||(v.animation.current=[])}return o.notify("draw"),this}}},mi,uu;function zn(){if(uu)return mi;uu=1;var r=Array.isArray;return mi=r,mi}var bi,lu;function Ph(){if(lu)return bi;lu=1;var r=zn(),e=za(),t=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;function n(i,s){if(r(i))return!1;var o=typeof i;return o=="number"||o=="symbol"||o=="boolean"||i==null||e(i)?!0:a.test(i)||!t.test(i)||s!=null&&i in Object(s)}return bi=n,bi}var wi,vu;function Ah(){if(vu)return wi;vu=1;var r=sv(),e=Na(),t="[object AsyncFunction]",a="[object Function]",n="[object GeneratorFunction]",i="[object Proxy]";function s(o){if(!e(o))return!1;var l=r(o);return l==a||l==n||l==t||l==i}return wi=s,wi}var xi,fu;function Rh(){if(fu)return xi;fu=1;var r=Ln(),e=r["__core-js_shared__"];return xi=e,xi}var Ei,cu;function Mh(){if(cu)return Ei;cu=1;var r=Rh(),e=function(){var a=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||"");return a?"Symbol(src)_1."+a:""}();function t(a){return!!e&&e in a}return Ei=t,Ei}var Ci,du;function Lh(){if(du)return Ci;du=1;var r=Function.prototype,e=r.toString;function t(a){if(a!=null){try{return e.call(a)}catch{}try{return a+""}catch{}}return""}return Ci=t,Ci}var Ti,hu;function Ih(){if(hu)return Ti;hu=1;var r=Ah(),e=Mh(),t=Na(),a=Lh(),n=/[\\^$.*+?()[\]{}|]/g,i=/^\[object .+?Constructor\]$/,s=Function.prototype,o=Object.prototype,l=s.toString,u=o.hasOwnProperty,v=RegExp("^"+l.call(u).replace(n,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function f(c){if(!t(c)||e(c))return!1;var h=r(c)?v:i;return h.test(a(c))}return Ti=f,Ti}var Si,gu;function Oh(){if(gu)return Si;gu=1;function r(e,t){return e==null?void 0:e[t]}return Si=r,Si}var ki,pu;function so(){if(pu)return ki;pu=1;var r=Ih(),e=Oh();function t(a,n){var i=e(a,n);return r(i)?i:void 0}return ki=t,ki}var Di,yu;function Fn(){if(yu)return Di;yu=1;var r=so(),e=r(Object,"create");return Di=e,Di}var Bi,mu;function Nh(){if(mu)return Bi;mu=1;var r=Fn();function e(){this.__data__=r?r(null):{},this.size=0}return Bi=e,Bi}var Pi,bu;function zh(){if(bu)return Pi;bu=1;function r(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}return Pi=r,Pi}var Ai,wu;function Fh(){if(wu)return Ai;wu=1;var r=Fn(),e="__lodash_hash_undefined__",t=Object.prototype,a=t.hasOwnProperty;function n(i){var s=this.__data__;if(r){var o=s[i];return o===e?void 0:o}return a.call(s,i)?s[i]:void 0}return Ai=n,Ai}var Ri,xu;function Vh(){if(xu)return Ri;xu=1;var r=Fn(),e=Object.prototype,t=e.hasOwnProperty;function a(n){var i=this.__data__;return r?i[n]!==void 0:t.call(i,n)}return Ri=a,Ri}var Mi,Eu;function qh(){if(Eu)return Mi;Eu=1;var r=Fn(),e="__lodash_hash_undefined__";function t(a,n){var i=this.__data__;return this.size+=this.has(a)?0:1,i[a]=r&&n===void 0?e:n,this}return Mi=t,Mi}var Li,Cu;function _h(){if(Cu)return Li;Cu=1;var r=Nh(),e=zh(),t=Fh(),a=Vh(),n=qh();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=r,i.prototype.delete=e,i.prototype.get=t,i.prototype.has=a,i.prototype.set=n,Li=i,Li}var Ii,Tu;function Gh(){if(Tu)return Ii;Tu=1;function r(){this.__data__=[],this.size=0}return Ii=r,Ii}var Oi,Su;function Av(){if(Su)return Oi;Su=1;function r(e,t){return e===t||e!==e&&t!==t}return Oi=r,Oi}var Ni,ku;function Vn(){if(ku)return Ni;ku=1;var r=Av();function e(t,a){for(var n=t.length;n--;)if(r(t[n][0],a))return n;return-1}return Ni=e,Ni}var zi,Du;function Hh(){if(Du)return zi;Du=1;var r=Vn(),e=Array.prototype,t=e.splice;function a(n){var i=this.__data__,s=r(i,n);if(s<0)return!1;var o=i.length-1;return s==o?i.pop():t.call(i,s,1),--this.size,!0}return zi=a,zi}var Fi,Bu;function Wh(){if(Bu)return Fi;Bu=1;var r=Vn();function e(t){var a=this.__data__,n=r(a,t);return n<0?void 0:a[n][1]}return Fi=e,Fi}var Vi,Pu;function $h(){if(Pu)return Vi;Pu=1;var r=Vn();function e(t){return r(this.__data__,t)>-1}return Vi=e,Vi}var qi,Au;function Uh(){if(Au)return qi;Au=1;var r=Vn();function e(t,a){var n=this.__data__,i=r(n,t);return i<0?(++this.size,n.push([t,a])):n[i][1]=a,this}return qi=e,qi}var _i,Ru;function Kh(){if(Ru)return _i;Ru=1;var r=Gh(),e=Hh(),t=Wh(),a=$h(),n=Uh();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=r,i.prototype.delete=e,i.prototype.get=t,i.prototype.has=a,i.prototype.set=n,_i=i,_i}var Gi,Mu;function Xh(){if(Mu)return Gi;Mu=1;var r=so(),e=Ln(),t=r(e,"Map");return Gi=t,Gi}var Hi,Lu;function Yh(){if(Lu)return Hi;Lu=1;var r=_h(),e=Kh(),t=Xh();function a(){this.size=0,this.__data__={hash:new r,map:new(t||e),string:new r}}return Hi=a,Hi}var Wi,Iu;function Zh(){if(Iu)return Wi;Iu=1;function r(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}return Wi=r,Wi}var $i,Ou;function qn(){if(Ou)return $i;Ou=1;var r=Zh();function e(t,a){var n=t.__data__;return r(a)?n[typeof a=="string"?"string":"hash"]:n.map}return $i=e,$i}var Ui,Nu;function Qh(){if(Nu)return Ui;Nu=1;var r=qn();function e(t){var a=r(this,t).delete(t);return this.size-=a?1:0,a}return Ui=e,Ui}var Ki,zu;function Jh(){if(zu)return Ki;zu=1;var r=qn();function e(t){return r(this,t).get(t)}return Ki=e,Ki}var Xi,Fu;function jh(){if(Fu)return Xi;Fu=1;var r=qn();function e(t){return r(this,t).has(t)}return Xi=e,Xi}var Yi,Vu;function eg(){if(Vu)return Yi;Vu=1;var r=qn();function e(t,a){var n=r(this,t),i=n.size;return n.set(t,a),this.size+=n.size==i?0:1,this}return Yi=e,Yi}var Zi,qu;function rg(){if(qu)return Zi;qu=1;var r=Yh(),e=Qh(),t=Jh(),a=jh(),n=eg();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=r,i.prototype.delete=e,i.prototype.get=t,i.prototype.has=a,i.prototype.set=n,Zi=i,Zi}var Qi,_u;function tg(){if(_u)return Qi;_u=1;var r=rg(),e="Expected a function";function t(a,n){if(typeof a!="function"||n!=null&&typeof n!="function")throw new TypeError(e);var i=function(){var s=arguments,o=n?n.apply(this,s):s[0],l=i.cache;if(l.has(o))return l.get(o);var u=a.apply(this,s);return i.cache=l.set(o,u)||l,u};return i.cache=new(t.Cache||r),i}return t.Cache=r,Qi=t,Qi}var Ji,Gu;function ag(){if(Gu)return Ji;Gu=1;var r=tg(),e=500;function t(a){var n=r(a,function(s){return i.size===e&&i.clear(),s}),i=n.cache;return n}return Ji=t,Ji}var ji,Hu;function Rv(){if(Hu)return ji;Hu=1;var r=ag(),e=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,t=/\\(\\)?/g,a=r(function(n){var i=[];return n.charCodeAt(0)===46&&i.push(""),n.replace(e,function(s,o,l,u){i.push(l?u.replace(t,"$1"):o||s)}),i});return ji=a,ji}var es,Wu;function Mv(){if(Wu)return es;Wu=1;function r(e,t){for(var a=-1,n=e==null?0:e.length,i=Array(n);++a<n;)i[a]=t(e[a],a,e);return i}return es=r,es}var rs,$u;function ng(){if($u)return rs;$u=1;var r=Qs(),e=Mv(),t=zn(),a=za(),n=r?r.prototype:void 0,i=n?n.toString:void 0;function s(o){if(typeof o=="string")return o;if(t(o))return e(o,s)+"";if(a(o))return i?i.call(o):"";var l=o+"";return l=="0"&&1/o==-1/0?"-0":l}return rs=s,rs}var ts,Uu;function Lv(){if(Uu)return ts;Uu=1;var r=ng();function e(t){return t==null?"":r(t)}return ts=e,ts}var as,Ku;function Iv(){if(Ku)return as;Ku=1;var r=zn(),e=Ph(),t=Rv(),a=Lv();function n(i,s){return r(i)?i:e(i,s)?[i]:t(a(i))}return as=n,as}var ns,Xu;function oo(){if(Xu)return ns;Xu=1;var r=za();function e(t){if(typeof t=="string"||r(t))return t;var a=t+"";return a=="0"&&1/t==-1/0?"-0":a}return ns=e,ns}var is,Yu;function ig(){if(Yu)return is;Yu=1;var r=Iv(),e=oo();function t(a,n){n=r(n,a);for(var i=0,s=n.length;a!=null&&i<s;)a=a[e(n[i++])];return i&&i==s?a:void 0}return is=t,is}var ss,Zu;function sg(){if(Zu)return ss;Zu=1;var r=ig();function e(t,a,n){var i=t==null?void 0:r(t,a);return i===void 0?n:i}return ss=e,ss}var og=sg(),ug=Oa(og),os,Qu;function lg(){if(Qu)return os;Qu=1;var r=so(),e=function(){try{var t=r(Object,"defineProperty");return t({},"",{}),t}catch{}}();return os=e,os}var us,Ju;function vg(){if(Ju)return us;Ju=1;var r=lg();function e(t,a,n){a=="__proto__"&&r?r(t,a,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[a]=n}return us=e,us}var ls,ju;function fg(){if(ju)return ls;ju=1;var r=vg(),e=Av(),t=Object.prototype,a=t.hasOwnProperty;function n(i,s,o){var l=i[s];(!(a.call(i,s)&&e(l,o))||o===void 0&&!(s in i))&&r(i,s,o)}return ls=n,ls}var vs,el;function cg(){if(el)return vs;el=1;var r=9007199254740991,e=/^(?:0|[1-9]\d*)$/;function t(a,n){var i=typeof a;return n=n??r,!!n&&(i=="number"||i!="symbol"&&e.test(a))&&a>-1&&a%1==0&&a<n}return vs=t,vs}var fs,rl;function dg(){if(rl)return fs;rl=1;var r=fg(),e=Iv(),t=cg(),a=Na(),n=oo();function i(s,o,l,u){if(!a(s))return s;o=e(o,s);for(var v=-1,f=o.length,c=f-1,h=s;h!=null&&++v<f;){var d=n(o[v]),m=l;if(d==="__proto__"||d==="constructor"||d==="prototype")return s;if(v!=c){var g=h[d];m=u?u(g,d,h):void 0,m===void 0&&(m=a(g)?g:t(o[v+1])?[]:{})}r(h,d,m),h=h[d]}return s}return fs=i,fs}var cs,tl;function hg(){if(tl)return cs;tl=1;var r=dg();function e(t,a,n){return t==null?t:r(t,a,n)}return cs=e,cs}var gg=hg(),pg=Oa(gg),ds,al;function yg(){if(al)return ds;al=1;function r(e,t){var a=-1,n=e.length;for(t||(t=Array(n));++a<n;)t[a]=e[a];return t}return ds=r,ds}var hs,nl;function mg(){if(nl)return hs;nl=1;var r=Mv(),e=yg(),t=zn(),a=za(),n=Rv(),i=oo(),s=Lv();function o(l){return t(l)?r(l,i):a(l)?[l]:e(n(s(l)))}return hs=o,hs}var bg=mg(),wg=Oa(bg),xg={data:function(e){var t={field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(n){},beforeSet:function(n,i){},onSet:function(n){},canSet:function(n){return!0}};return e=pe({},t,e),function(n,i){var s=e,o=this,l=o.length!==void 0,u=l?o:[o],v=l?o[0]:o;if(de(n)){var f=n.indexOf(".")!==-1,c=f&&wg(n);if(s.allowGetting&&i===void 0){var h;return v&&(s.beforeGet(v),c&&v._private[s.field][n]===void 0?h=ug(v._private[s.field],c):h=v._private[s.field][n]),h}else if(s.allowSetting&&i!==void 0){var d=!s.immutableKeys[n];if(d){var m=Zl({},n,i);s.beforeSet(o,m);for(var g=0,p=u.length;g<p;g++){var y=u[g];s.canSet(y)&&(c&&v._private[s.field][n]===void 0?pg(y._private[s.field],c,i):y._private[s.field][n]=i)}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}}}else if(s.allowSetting&&Re(n)){var b=n,w,E,C=Object.keys(b);s.beforeSet(o,b);for(var x=0;x<C.length;x++){w=C[x],E=b[w];var T=!s.immutableKeys[w];if(T)for(var S=0;S<u.length;S++){var P=u[S];s.canSet(P)&&(P._private[s.field][w]=E)}}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}else if(s.allowBinding&&$e(n)){var D=n;o.on(s.bindingEvent,D)}else if(s.allowGetting&&n===void 0){var A;return v&&(s.beforeGet(v),A=v._private[s.field]),A}return o}},removeData:function(e){var t={field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}};return e=pe({},t,e),function(n){var i=e,s=this,o=s.length!==void 0,l=o?s:[s];if(de(n)){for(var u=n.split(/\s+/),v=u.length,f=0;f<v;f++){var c=u[f];if(!ot(c)){var h=!i.immutableKeys[c];if(h)for(var d=0,m=l.length;d<m;d++)l[d]._private[i.field][c]=void 0}}i.triggerEvent&&s[i.triggerFnName](i.event)}else if(n===void 0){for(var g=0,p=l.length;g<p;g++)for(var y=l[g]._private[i.field],b=Object.keys(y),w=0;w<b.length;w++){var E=b[w],C=!i.immutableKeys[E];C&&(y[E]=void 0)}i.triggerEvent&&s[i.triggerFnName](i.event)}return s}}},Eg={eventAliasesOn:function(e){var t=e;t.addListener=t.listen=t.bind=t.on,t.unlisten=t.unbind=t.off=t.removeListener,t.trigger=t.emit,t.pon=t.promiseOn=function(a,n){var i=this,s=Array.prototype.slice.call(arguments,0);return new ta(function(o,l){var u=function(h){i.off.apply(i,f),o(h)},v=s.concat([u]),f=v.concat([]);i.on.apply(i,v)})}}},Le={};[Bh,xg,Eg].forEach(function(r){pe(Le,r)});var Cg={animate:Le.animate(),animation:Le.animation(),animated:Le.animated(),clearQueue:Le.clearQueue(),delay:Le.delay(),delayAnimation:Le.delayAnimation(),stop:Le.stop()},vn={classes:function(e){var t=this;if(e===void 0){var a=[];return t[0]._private.classes.forEach(function(d){return a.push(d)}),a}else ze(e)||(e=(e||"").match(/\S+/g)||[]);for(var n=[],i=new ra(e),s=0;s<t.length;s++){for(var o=t[s],l=o._private,u=l.classes,v=!1,f=0;f<e.length;f++){var c=e[f],h=u.has(c);if(!h){v=!0;break}}v||(v=u.size!==e.length),v&&(l.classes=i,n.push(o))}return n.length>0&&this.spawn(n).updateStyle().emit("class"),t},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var t=this[0];return t!=null&&t._private.classes.has(e)},toggleClass:function(e,t){ze(e)||(e=e.match(/\S+/g)||[]);for(var a=this,n=t===void 0,i=[],s=0,o=a.length;s<o;s++)for(var l=a[s],u=l._private.classes,v=!1,f=0;f<e.length;f++){var c=e[f],h=u.has(c),d=!1;t||n&&!h?(u.add(c),d=!0):(!t||n&&h)&&(u.delete(c),d=!0),!v&&d&&(i.push(l),v=!0)}return i.length>0&&this.spawn(i).updateStyle().emit("class"),a},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,t){var a=this;if(t==null)t=250;else if(t===0)return a;return a.addClass(e),setTimeout(function(){a.removeClass(e)},t),a}};vn.className=vn.classNames=vn.classes;var Ae={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:`"(?:\\\\"|[^"])*"|'(?:\\\\'|[^'])*'`,number:tr,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};Ae.variable="(?:[\\w-.]|(?:\\\\"+Ae.metaChar+"))+";Ae.className="(?:[\\w-]|(?:\\\\"+Ae.metaChar+"))+";Ae.value=Ae.string+"|"+Ae.number;Ae.id=Ae.variable;(function(){var r,e,t;for(r=Ae.comparatorOp.split("|"),t=0;t<r.length;t++)e=r[t],Ae.comparatorOp+="|@"+e;for(r=Ae.comparatorOp.split("|"),t=0;t<r.length;t++)e=r[t],!(e.indexOf("!")>=0)&&e!=="="&&(Ae.comparatorOp+="|\\!"+e)})();var Ne=function(){return{checks:[]}},ie={GROUP:0,COLLECTION:1,FILTER:2,DATA_COMPARE:3,DATA_EXIST:4,DATA_BOOL:5,META_COMPARE:6,STATE:7,ID:8,CLASS:9,UNDIRECTED_EDGE:10,DIRECTED_EDGE:11,NODE_SOURCE:12,NODE_TARGET:13,NODE_NEIGHBOR:14,CHILD:15,DESCENDANT:16,PARENT:17,ANCESTOR:18,COMPOUND_SPLIT:19,TRUE:20},Os=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort(function(r,e){return xc(r.selector,e.selector)}),Tg=function(){for(var r={},e,t=0;t<Os.length;t++)e=Os[t],r[e.selector]=e.matches;return r}(),Sg=function(e,t){return Tg[e](t)},kg="("+Os.map(function(r){return r.selector}).join("|")+")",It=function(e){return e.replace(new RegExp("\\\\("+Ae.metaChar+")","g"),function(t,a){return a})},rt=function(e,t,a){e[e.length-1]=a},Ns=[{name:"group",query:!0,regex:"("+Ae.group+")",populate:function(e,t,a){var n=rr(a,1),i=n[0];t.checks.push({type:ie.GROUP,value:i==="*"?i:i+"s"})}},{name:"state",query:!0,regex:kg,populate:function(e,t,a){var n=rr(a,1),i=n[0];t.checks.push({type:ie.STATE,value:i})}},{name:"id",query:!0,regex:"\\#("+Ae.id+")",populate:function(e,t,a){var n=rr(a,1),i=n[0];t.checks.push({type:ie.ID,value:It(i)})}},{name:"className",query:!0,regex:"\\.("+Ae.className+")",populate:function(e,t,a){var n=rr(a,1),i=n[0];t.checks.push({type:ie.CLASS,value:It(i)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+Ae.variable+")\\s*\\]",populate:function(e,t,a){var n=rr(a,1),i=n[0];t.checks.push({type:ie.DATA_EXIST,field:It(i)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+Ae.variable+")\\s*("+Ae.comparatorOp+")\\s*("+Ae.value+")\\s*\\]",populate:function(e,t,a){var n=rr(a,3),i=n[0],s=n[1],o=n[2],l=new RegExp("^"+Ae.string+"$").exec(o)!=null;l?o=o.substring(1,o.length-1):o=parseFloat(o),t.checks.push({type:ie.DATA_COMPARE,field:It(i),operator:s,value:o})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+Ae.boolOp+")\\s*("+Ae.variable+")\\s*\\]",populate:function(e,t,a){var n=rr(a,2),i=n[0],s=n[1];t.checks.push({type:ie.DATA_BOOL,field:It(s),operator:i})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+Ae.meta+")\\s*("+Ae.comparatorOp+")\\s*("+Ae.number+")\\s*\\]\\]",populate:function(e,t,a){var n=rr(a,3),i=n[0],s=n[1],o=n[2];t.checks.push({type:ie.META_COMPARE,field:It(i),operator:s,value:parseFloat(o)})}},{name:"nextQuery",separator:!0,regex:Ae.separator,populate:function(e,t){var a=e.currentSubject,n=e.edgeCount,i=e.compoundCount,s=e[e.length-1];a!=null&&(s.subject=a,e.currentSubject=null),s.edgeCount=n,s.compoundCount=i,e.edgeCount=0,e.compoundCount=0;var o=e[e.length++]=Ne();return o}},{name:"directedEdge",separator:!0,regex:Ae.directedEdge,populate:function(e,t){if(e.currentSubject==null){var a=Ne(),n=t,i=Ne();return a.checks.push({type:ie.DIRECTED_EDGE,source:n,target:i}),rt(e,t,a),e.edgeCount++,i}else{var s=Ne(),o=t,l=Ne();return s.checks.push({type:ie.NODE_SOURCE,source:o,target:l}),rt(e,t,s),e.edgeCount++,l}}},{name:"undirectedEdge",separator:!0,regex:Ae.undirectedEdge,populate:function(e,t){if(e.currentSubject==null){var a=Ne(),n=t,i=Ne();return a.checks.push({type:ie.UNDIRECTED_EDGE,nodes:[n,i]}),rt(e,t,a),e.edgeCount++,i}else{var s=Ne(),o=t,l=Ne();return s.checks.push({type:ie.NODE_NEIGHBOR,node:o,neighbor:l}),rt(e,t,s),l}}},{name:"child",separator:!0,regex:Ae.child,populate:function(e,t){if(e.currentSubject==null){var a=Ne(),n=Ne(),i=e[e.length-1];return a.checks.push({type:ie.CHILD,parent:i,child:n}),rt(e,t,a),e.compoundCount++,n}else if(e.currentSubject===t){var s=Ne(),o=e[e.length-1],l=Ne(),u=Ne(),v=Ne(),f=Ne();return s.checks.push({type:ie.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=t.checks,t.checks=[{type:ie.TRUE}],f.checks.push({type:ie.TRUE}),l.checks.push({type:ie.PARENT,parent:f,child:v}),rt(e,o,s),e.currentSubject=u,e.compoundCount++,v}else{var c=Ne(),h=Ne(),d=[{type:ie.PARENT,parent:c,child:h}];return c.checks=t.checks,t.checks=d,e.compoundCount++,h}}},{name:"descendant",separator:!0,regex:Ae.descendant,populate:function(e,t){if(e.currentSubject==null){var a=Ne(),n=Ne(),i=e[e.length-1];return a.checks.push({type:ie.DESCENDANT,ancestor:i,descendant:n}),rt(e,t,a),e.compoundCount++,n}else if(e.currentSubject===t){var s=Ne(),o=e[e.length-1],l=Ne(),u=Ne(),v=Ne(),f=Ne();return s.checks.push({type:ie.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=t.checks,t.checks=[{type:ie.TRUE}],f.checks.push({type:ie.TRUE}),l.checks.push({type:ie.ANCESTOR,ancestor:f,descendant:v}),rt(e,o,s),e.currentSubject=u,e.compoundCount++,v}else{var c=Ne(),h=Ne(),d=[{type:ie.ANCESTOR,ancestor:c,descendant:h}];return c.checks=t.checks,t.checks=d,e.compoundCount++,h}}},{name:"subject",modifier:!0,regex:Ae.subject,populate:function(e,t){if(e.currentSubject!=null&&e.currentSubject!==t)return Ie("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=t;var a=e[e.length-1],n=a.checks[0],i=n==null?null:n.type;i===ie.DIRECTED_EDGE?n.type=ie.NODE_TARGET:i===ie.UNDIRECTED_EDGE&&(n.type=ie.NODE_NEIGHBOR,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];Ns.forEach(function(r){return r.regexObj=new RegExp("^"+r.regex)});var Dg=function(e){for(var t,a,n,i=0;i<Ns.length;i++){var s=Ns[i],o=s.name,l=e.match(s.regexObj);if(l!=null){a=l,t=s,n=o;var u=l[0];e=e.substring(u.length);break}}return{expr:t,match:a,name:n,remaining:e}},Bg=function(e){var t=e.match(/^\s+/);if(t){var a=t[0];e=e.substring(a.length)}return e},Pg=function(e){var t=this,a=t.inputText=e,n=t[0]=Ne();for(t.length=1,a=Bg(a);;){var i=Dg(a);if(i.expr==null)return Ie("The selector `"+e+"`is invalid"),!1;var s=i.match.slice(1),o=i.expr.populate(t,n,s);if(o===!1)return!1;if(o!=null&&(n=o),a=i.remaining,a.match(/^\s*$/))break}var l=t[t.length-1];t.currentSubject!=null&&(l.subject=t.currentSubject),l.edgeCount=t.edgeCount,l.compoundCount=t.compoundCount;for(var u=0;u<t.length;u++){var v=t[u];if(v.compoundCount>0&&v.edgeCount>0)return Ie("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(v.edgeCount>1)return Ie("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;v.edgeCount===1&&Ie("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},Ag=function(){if(this.toStringCache!=null)return this.toStringCache;for(var e=function(v){return v??""},t=function(v){return de(v)?'"'+v+'"':e(v)},a=function(v){return" "+v+" "},n=function(v,f){var c=v.type,h=v.value;switch(c){case ie.GROUP:{var d=e(h);return d.substring(0,d.length-1)}case ie.DATA_COMPARE:{var m=v.field,g=v.operator;return"["+m+a(e(g))+t(h)+"]"}case ie.DATA_BOOL:{var p=v.operator,y=v.field;return"["+e(p)+y+"]"}case ie.DATA_EXIST:{var b=v.field;return"["+b+"]"}case ie.META_COMPARE:{var w=v.operator,E=v.field;return"[["+E+a(e(w))+t(h)+"]]"}case ie.STATE:return h;case ie.ID:return"#"+h;case ie.CLASS:return"."+h;case ie.PARENT:case ie.CHILD:return i(v.parent,f)+a(">")+i(v.child,f);case ie.ANCESTOR:case ie.DESCENDANT:return i(v.ancestor,f)+" "+i(v.descendant,f);case ie.COMPOUND_SPLIT:{var C=i(v.left,f),x=i(v.subject,f),T=i(v.right,f);return C+(C.length>0?" ":"")+x+T}case ie.TRUE:return""}},i=function(v,f){return v.checks.reduce(function(c,h,d){return c+(f===v&&d===0?"$":"")+n(h,f)},"")},s="",o=0;o<this.length;o++){var l=this[o];s+=i(l,l.subject),this.length>1&&o<this.length-1&&(s+=", ")}return this.toStringCache=s,s},Rg={parse:Pg,toString:Ag},Ov=function(e,t,a){var n,i=de(e),s=te(e),o=de(a),l,u,v=!1,f=!1,c=!1;switch(t.indexOf("!")>=0&&(t=t.replace("!",""),f=!0),t.indexOf("@")>=0&&(t=t.replace("@",""),v=!0),(i||o||v)&&(l=!i&&!s?"":""+e,u=""+a),v&&(e=l=l.toLowerCase(),a=u=u.toLowerCase()),t){case"*=":n=l.indexOf(u)>=0;break;case"$=":n=l.indexOf(u,l.length-u.length)>=0;break;case"^=":n=l.indexOf(u)===0;break;case"=":n=e===a;break;case">":c=!0,n=e>a;break;case">=":c=!0,n=e>=a;break;case"<":c=!0,n=e<a;break;case"<=":c=!0,n=e<=a;break;default:n=!1;break}return f&&(e!=null||!c)&&(n=!n),n},Mg=function(e,t){switch(t){case"?":return!!e;case"!":return!e;case"^":return e===void 0}},Lg=function(e){return e!==void 0},uo=function(e,t){return e.data(t)},Ig=function(e,t){return e[t]()},Ke=[],Ge=function(e,t){return e.checks.every(function(a){return Ke[a.type](a,t)})};Ke[ie.GROUP]=function(r,e){var t=r.value;return t==="*"||t===e.group()};Ke[ie.STATE]=function(r,e){var t=r.value;return Sg(t,e)};Ke[ie.ID]=function(r,e){var t=r.value;return e.id()===t};Ke[ie.CLASS]=function(r,e){var t=r.value;return e.hasClass(t)};Ke[ie.META_COMPARE]=function(r,e){var t=r.field,a=r.operator,n=r.value;return Ov(Ig(e,t),a,n)};Ke[ie.DATA_COMPARE]=function(r,e){var t=r.field,a=r.operator,n=r.value;return Ov(uo(e,t),a,n)};Ke[ie.DATA_BOOL]=function(r,e){var t=r.field,a=r.operator;return Mg(uo(e,t),a)};Ke[ie.DATA_EXIST]=function(r,e){var t=r.field;return r.operator,Lg(uo(e,t))};Ke[ie.UNDIRECTED_EDGE]=function(r,e){var t=r.nodes[0],a=r.nodes[1],n=e.source(),i=e.target();return Ge(t,n)&&Ge(a,i)||Ge(a,n)&&Ge(t,i)};Ke[ie.NODE_NEIGHBOR]=function(r,e){return Ge(r.node,e)&&e.neighborhood().some(function(t){return t.isNode()&&Ge(r.neighbor,t)})};Ke[ie.DIRECTED_EDGE]=function(r,e){return Ge(r.source,e.source())&&Ge(r.target,e.target())};Ke[ie.NODE_SOURCE]=function(r,e){return Ge(r.source,e)&&e.outgoers().some(function(t){return t.isNode()&&Ge(r.target,t)})};Ke[ie.NODE_TARGET]=function(r,e){return Ge(r.target,e)&&e.incomers().some(function(t){return t.isNode()&&Ge(r.source,t)})};Ke[ie.CHILD]=function(r,e){return Ge(r.child,e)&&Ge(r.parent,e.parent())};Ke[ie.PARENT]=function(r,e){return Ge(r.parent,e)&&e.children().some(function(t){return Ge(r.child,t)})};Ke[ie.DESCENDANT]=function(r,e){return Ge(r.descendant,e)&&e.ancestors().some(function(t){return Ge(r.ancestor,t)})};Ke[ie.ANCESTOR]=function(r,e){return Ge(r.ancestor,e)&&e.descendants().some(function(t){return Ge(r.descendant,t)})};Ke[ie.COMPOUND_SPLIT]=function(r,e){return Ge(r.subject,e)&&Ge(r.left,e)&&Ge(r.right,e)};Ke[ie.TRUE]=function(){return!0};Ke[ie.COLLECTION]=function(r,e){var t=r.value;return t.has(e)};Ke[ie.FILTER]=function(r,e){var t=r.value;return t(e)};var Og=function(e){var t=this;if(t.length===1&&t[0].checks.length===1&&t[0].checks[0].type===ie.ID)return e.getElementById(t[0].checks[0].value).collection();var a=function(i){for(var s=0;s<t.length;s++){var o=t[s];if(Ge(o,i))return!0}return!1};return t.text()==null&&(a=function(){return!0}),e.filter(a)},Ng=function(e){for(var t=this,a=0;a<t.length;a++){var n=t[a];if(Ge(n,e))return!0}return!1},zg={matches:Ng,filter:Og},vt=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,e==null||de(e)&&e.match(/^\s*$/)||(Dr(e)?this.addQuery({checks:[{type:ie.COLLECTION,value:e.collection()}]}):$e(e)?this.addQuery({checks:[{type:ie.FILTER,value:e}]}):de(e)?this.parse(e)||(this.invalid=!0):We("A selector must be created from a string; found "))},ft=vt.prototype;[Rg,zg].forEach(function(r){return pe(ft,r)});ft.text=function(){return this.inputText};ft.size=function(){return this.length};ft.eq=function(r){return this[r]};ft.sameText=function(r){return!this.invalid&&!r.invalid&&this.text()===r.text()};ft.addQuery=function(r){this[this.length++]=r};ft.selector=ft.toString;var it={allAre:function(e){var t=new vt(e);return this.every(function(a){return t.matches(a)})},is:function(e){var t=new vt(e);return this.some(function(a){return t.matches(a)})},some:function(e,t){for(var a=0;a<this.length;a++){var n=t?e.apply(t,[this[a],a,this]):e(this[a],a,this);if(n)return!0}return!1},every:function(e,t){for(var a=0;a<this.length;a++){var n=t?e.apply(t,[this[a],a,this]):e(this[a],a,this);if(!n)return!1}return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var t=this.length,a=e.length;return t!==a?!1:t===1?this[0]===e[0]:this.every(function(n){return e.hasElementWithId(n.id())})},anySame:function(e){return e=this.cy().collection(e),this.some(function(t){return e.hasElementWithId(t.id())})},allAreNeighbors:function(e){e=this.cy().collection(e);var t=this.neighborhood();return e.every(function(a){return t.hasElementWithId(a.id())})},contains:function(e){e=this.cy().collection(e);var t=this;return e.every(function(a){return t.hasElementWithId(a.id())})}};it.allAreNeighbours=it.allAreNeighbors;it.has=it.contains;it.equal=it.equals=it.same;var Rr=function(e,t){return function(n,i,s,o){var l=n,u=this,v;if(l==null?v="":Dr(l)&&l.length===1&&(v=l.id()),u.length===1&&v){var f=u[0]._private,c=f.traversalCache=f.traversalCache||{},h=c[t]=c[t]||[],d=kt(v),m=h[d];return m||(h[d]=e.call(u,n,i,s,o))}else return e.call(u,n,i,s,o)}},jt={parent:function(e){var t=[];if(this.length===1){var a=this[0]._private.parent;if(a)return a}for(var n=0;n<this.length;n++){var i=this[n],s=i._private.parent;s&&t.push(s)}return this.spawn(t,!0).filter(e)},parents:function(e){for(var t=[],a=this.parent();a.nonempty();){for(var n=0;n<a.length;n++){var i=a[n];t.push(i)}a=a.parent()}return this.spawn(t,!0).filter(e)},commonAncestors:function(e){for(var t,a=0;a<this.length;a++){var n=this[a],i=n.parents();t=t||i,t=t.intersect(i)}return t.filter(e)},orphans:function(e){return this.stdFilter(function(t){return t.isOrphan()}).filter(e)},nonorphans:function(e){return this.stdFilter(function(t){return t.isChild()}).filter(e)},children:Rr(function(r){for(var e=[],t=0;t<this.length;t++)for(var a=this[t],n=a._private.children,i=0;i<n.length;i++)e.push(n[i]);return this.spawn(e,!0).filter(r)},"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length!==0},isChildless:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length===0},isChild:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent!=null},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent==null},descendants:function(e){var t=[];function a(n){for(var i=0;i<n.length;i++){var s=n[i];t.push(s),s.children().nonempty()&&a(s.children())}}return a(this.children()),this.spawn(t,!0).filter(e)}};function lo(r,e,t,a){for(var n=[],i=new ra,s=r.cy(),o=s.hasCompoundNodes(),l=0;l<r.length;l++){var u=r[l];t?n.push(u):o&&a(n,i,u)}for(;n.length>0;){var v=n.shift();e(v),i.add(v.id()),o&&a(n,i,v)}return r}function Nv(r,e,t){if(t.isParent())for(var a=t._private.children,n=0;n<a.length;n++){var i=a[n];e.has(i.id())||r.push(i)}}jt.forEachDown=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return lo(this,r,e,Nv)};function zv(r,e,t){if(t.isChild()){var a=t._private.parent;e.has(a.id())||r.push(a)}}jt.forEachUp=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return lo(this,r,e,zv)};function Fg(r,e,t){zv(r,e,t),Nv(r,e,t)}jt.forEachUpAndDown=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return lo(this,r,e,Fg)};jt.ancestors=jt.parents;var Ba,Fv;Ba=Fv={data:Le.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:Le.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:Le.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Le.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:Le.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:Le.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}};Ba.attr=Ba.data;Ba.removeAttr=Ba.removeData;var Vg=Fv,_n={};function gs(r){return function(e){var t=this;if(e===void 0&&(e=!0),t.length!==0)if(t.isNode()&&!t.removed()){for(var a=0,n=t[0],i=n._private.edges,s=0;s<i.length;s++){var o=i[s];!e&&o.isLoop()||(a+=r(n,o))}return a}else return}}pe(_n,{degree:gs(function(r,e){return e.source().same(e.target())?2:1}),indegree:gs(function(r,e){return e.target().same(r)?1:0}),outdegree:gs(function(r,e){return e.source().same(r)?1:0})});function Ot(r,e){return function(t){for(var a,n=this.nodes(),i=0;i<n.length;i++){var s=n[i],o=s[r](t);o!==void 0&&(a===void 0||e(o,a))&&(a=o)}return a}}pe(_n,{minDegree:Ot("degree",function(r,e){return r<e}),maxDegree:Ot("degree",function(r,e){return r>e}),minIndegree:Ot("indegree",function(r,e){return r<e}),maxIndegree:Ot("indegree",function(r,e){return r>e}),minOutdegree:Ot("outdegree",function(r,e){return r<e}),maxOutdegree:Ot("outdegree",function(r,e){return r>e})});pe(_n,{totalDegree:function(e){for(var t=0,a=this.nodes(),n=0;n<a.length;n++)t+=a[n].degree(e);return t}});var Or,Vv,qv=function(e,t,a){for(var n=0;n<e.length;n++){var i=e[n];if(!i.locked()){var s=i._private.position,o={x:t.x!=null?t.x-s.x:0,y:t.y!=null?t.y-s.y:0};i.isParent()&&!(o.x===0&&o.y===0)&&i.children().shift(o,a),i.dirtyBoundingBoxCache()}}},il={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,t){qv(e,t,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};Or=Vv={position:Le.data(il),silentPosition:Le.data(pe({},il,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,t){qv(e,t,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,t){if(Re(e))t?this.silentPosition(e):this.position(e);else if($e(e)){var a=e,n=this.cy();n.startBatch();for(var i=0;i<this.length;i++){var s=this[i],o=void 0;(o=a(s,i))&&(t?s.silentPosition(o):s.position(o))}n.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,t,a){var n;if(Re(e)?(n={x:te(e.x)?e.x:0,y:te(e.y)?e.y:0},a=t):de(e)&&te(t)&&(n={x:0,y:0},n[e]=t),n!=null){var i=this.cy();i.startBatch();for(var s=0;s<this.length;s++){var o=this[s];if(!(i.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var l=o.position(),u={x:l.x+n.x,y:l.y+n.y};a?o.silentPosition(u):o.position(u)}}i.endBatch()}return this},silentShift:function(e,t){return Re(e)?this.shift(e,!0):de(e)&&te(t)&&this.shift(e,t,!0),this},renderedPosition:function(e,t){var a=this[0],n=this.cy(),i=n.zoom(),s=n.pan(),o=Re(e)?e:void 0,l=o!==void 0||t!==void 0&&de(e);if(a&&a.isNode())if(l)for(var u=0;u<this.length;u++){var v=this[u];t!==void 0?v.position(e,(t-s[e])/i):o!==void 0&&v.position(gv(o,i,s))}else{var f=a.position();return o=On(f,i,s),e===void 0?o:o[e]}else if(!l)return;return this},relativePosition:function(e,t){var a=this[0],n=this.cy(),i=Re(e)?e:void 0,s=i!==void 0||t!==void 0&&de(e),o=n.hasCompoundNodes();if(a&&a.isNode())if(s)for(var l=0;l<this.length;l++){var u=this[l],v=o?u.parent():null,f=v&&v.length>0,c=f;f&&(v=v[0]);var h=c?v.position():{x:0,y:0};t!==void 0?u.position(e,t+h[e]):i!==void 0&&u.position({x:i.x+h.x,y:i.y+h.y})}else{var d=a.position(),m=o?a.parent():null,g=m&&m.length>0,p=g;g&&(m=m[0]);var y=p?m.position():{x:0,y:0};return i={x:d.x-y.x,y:d.y-y.y},e===void 0?i:i[e]}else if(!s)return;return this}};Or.modelPosition=Or.point=Or.position;Or.modelPositions=Or.points=Or.positions;Or.renderedPoint=Or.renderedPosition;Or.relativePoint=Or.relativePosition;var qg=Vv,Yt,gt;Yt=gt={};gt.renderedBoundingBox=function(r){var e=this.boundingBox(r),t=this.cy(),a=t.zoom(),n=t.pan(),i=e.x1*a+n.x,s=e.x2*a+n.x,o=e.y1*a+n.y,l=e.y2*a+n.y;return{x1:i,x2:s,y1:o,y2:l,w:s-i,h:l-o}};gt.dirtyCompoundBoundsCache=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();return!e.styleEnabled()||!e.hasCompoundNodes()?this:(this.forEachUp(function(t){if(t.isParent()){var a=t._private;a.compoundBoundsClean=!1,a.bbCache=null,r||t.emitAndNotify("bounds")}}),this)};gt.updateCompoundBounds=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();if(!e.styleEnabled()||!e.hasCompoundNodes())return this;if(!r&&e.batching())return this;function t(s){if(!s.isParent())return;var o=s._private,l=s.children(),u=s.pstyle("compound-sizing-wrt-labels").value==="include",v={width:{val:s.pstyle("min-width").pfValue,left:s.pstyle("min-width-bias-left"),right:s.pstyle("min-width-bias-right")},height:{val:s.pstyle("min-height").pfValue,top:s.pstyle("min-height-bias-top"),bottom:s.pstyle("min-height-bias-bottom")}},f=l.boundingBox({includeLabels:u,includeOverlays:!1,useCache:!1}),c=o.position;(f.w===0||f.h===0)&&(f={w:s.pstyle("width").pfValue,h:s.pstyle("height").pfValue},f.x1=c.x-f.w/2,f.x2=c.x+f.w/2,f.y1=c.y-f.h/2,f.y2=c.y+f.h/2);function h(S,P,D){var A=0,B=0,R=P+D;return S>0&&R>0&&(A=P/R*S,B=D/R*S),{biasDiff:A,biasComplementDiff:B}}function d(S,P,D,A){if(D.units==="%")switch(A){case"width":return S>0?D.pfValue*S:0;case"height":return P>0?D.pfValue*P:0;case"average":return S>0&&P>0?D.pfValue*(S+P)/2:0;case"min":return S>0&&P>0?S>P?D.pfValue*P:D.pfValue*S:0;case"max":return S>0&&P>0?S>P?D.pfValue*S:D.pfValue*P:0;default:return 0}else return D.units==="px"?D.pfValue:0}var m=v.width.left.value;v.width.left.units==="px"&&v.width.val>0&&(m=m*100/v.width.val);var g=v.width.right.value;v.width.right.units==="px"&&v.width.val>0&&(g=g*100/v.width.val);var p=v.height.top.value;v.height.top.units==="px"&&v.height.val>0&&(p=p*100/v.height.val);var y=v.height.bottom.value;v.height.bottom.units==="px"&&v.height.val>0&&(y=y*100/v.height.val);var b=h(v.width.val-f.w,m,g),w=b.biasDiff,E=b.biasComplementDiff,C=h(v.height.val-f.h,p,y),x=C.biasDiff,T=C.biasComplementDiff;o.autoPadding=d(f.w,f.h,s.pstyle("padding"),s.pstyle("padding-relative-to").value),o.autoWidth=Math.max(f.w,v.width.val),c.x=(-w+f.x1+f.x2+E)/2,o.autoHeight=Math.max(f.h,v.height.val),c.y=(-x+f.y1+f.y2+T)/2}for(var a=0;a<this.length;a++){var n=this[a],i=n._private;(!i.compoundBoundsClean||r)&&(t(n),e.batching()||(i.compoundBoundsClean=!0))}return this};var Ar=function(e){return e===1/0||e===-1/0?0:e},Ir=function(e,t,a,n,i){n-t===0||i-a===0||t==null||a==null||n==null||i==null||(e.x1=t<e.x1?t:e.x1,e.x2=n>e.x2?n:e.x2,e.y1=a<e.y1?a:e.y1,e.y2=i>e.y2?i:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},tt=function(e,t){return t==null?e:Ir(e,t.x1,t.y1,t.x2,t.y2)},fa=function(e,t,a){return Tr(e,t,a)},ja=function(e,t,a){if(!t.cy().headless()){var n=t._private,i=n.rstyle,s=i.arrowWidth/2,o=t.pstyle(a+"-arrow-shape").value,l,u;if(o!=="none"){a==="source"?(l=i.srcX,u=i.srcY):a==="target"?(l=i.tgtX,u=i.tgtY):(l=i.midX,u=i.midY);var v=n.arrowBounds=n.arrowBounds||{},f=v[a]=v[a]||{};f.x1=l-s,f.y1=u-s,f.x2=l+s,f.y2=u+s,f.w=f.x2-f.x1,f.h=f.y2-f.y1,un(f,1),Ir(e,f.x1,f.y1,f.x2,f.y2)}}},ps=function(e,t,a){if(!t.cy().headless()){var n;a?n=a+"-":n="";var i=t._private,s=i.rstyle,o=t.pstyle(n+"label").strValue;if(o){var l=t.pstyle("text-halign"),u=t.pstyle("text-valign"),v=fa(s,"labelWidth",a),f=fa(s,"labelHeight",a),c=fa(s,"labelX",a),h=fa(s,"labelY",a),d=t.pstyle(n+"text-margin-x").pfValue,m=t.pstyle(n+"text-margin-y").pfValue,g=t.isEdge(),p=t.pstyle(n+"text-rotation"),y=t.pstyle("text-outline-width").pfValue,b=t.pstyle("text-border-width").pfValue,w=b/2,E=t.pstyle("text-background-padding").pfValue,C=2,x=f,T=v,S=T/2,P=x/2,D,A,B,R;if(g)D=c-S,A=c+S,B=h-P,R=h+P;else{switch(l.value){case"left":D=c-T,A=c;break;case"center":D=c-S,A=c+S;break;case"right":D=c,A=c+T;break}switch(u.value){case"top":B=h-x,R=h;break;case"center":B=h-P,R=h+P;break;case"bottom":B=h,R=h+x;break}}var M=d-Math.max(y,w)-E-C,L=d+Math.max(y,w)+E+C,I=m-Math.max(y,w)-E-C,O=m+Math.max(y,w)+E+C;D+=M,A+=L,B+=I,R+=O;var F=a||"main",_=i.labelBounds,N=_[F]=_[F]||{};N.x1=D,N.y1=B,N.x2=A,N.y2=R,N.w=A-D,N.h=R-B,N.leftPad=M,N.rightPad=L,N.topPad=I,N.botPad=O;var q=g&&p.strValue==="autorotate",U=p.pfValue!=null&&p.pfValue!==0;if(q||U){var X=q?fa(i.rstyle,"labelAngle",a):p.pfValue,j=Math.cos(X),J=Math.sin(X),re=(D+A)/2,ae=(B+R)/2;if(!g){switch(l.value){case"left":re=A;break;case"right":re=D;break}switch(u.value){case"top":ae=R;break;case"bottom":ae=B;break}}var Z=function(Be,oe){return Be=Be-re,oe=oe-ae,{x:Be*j-oe*J+re,y:Be*J+oe*j+ae}},z=Z(D,B),G=Z(D,R),H=Z(A,B),Q=Z(A,R);D=Math.min(z.x,G.x,H.x,Q.x),A=Math.max(z.x,G.x,H.x,Q.x),B=Math.min(z.y,G.y,H.y,Q.y),R=Math.max(z.y,G.y,H.y,Q.y)}var ne=F+"Rot",be=_[ne]=_[ne]||{};be.x1=D,be.y1=B,be.x2=A,be.y2=R,be.w=A-D,be.h=R-B,Ir(e,D,B,A,R),Ir(i.labelBounds.all,D,B,A,R)}return e}},sl=function(e,t){if(!t.cy().headless()){var a=t.pstyle("outline-opacity").value,n=t.pstyle("outline-width").value,i=t.pstyle("outline-offset").value,s=n+i;_v(e,t,a,s,"outside",s/2)}},_v=function(e,t,a,n,i,s){if(!(a===0||n<=0||i==="inside")){var o=t.cy(),l=t.pstyle("shape").value,u=o.renderer().nodeShapes[l],v=t.position(),f=v.x,c=v.y,h=t.width(),d=t.height();if(u.hasMiterBounds){i==="center"&&(n/=2);var m=u.miterBounds(f,c,h,d,n);tt(e,m)}else s!=null&&s>0&&ln(e,[s,s,s,s])}},_g=function(e,t){if(!t.cy().headless()){var a=t.pstyle("border-opacity").value,n=t.pstyle("border-width").pfValue,i=t.pstyle("border-position").value;_v(e,t,a,n,i)}},Gg=function(e,t){var a=e._private.cy,n=a.styleEnabled(),i=a.headless(),s=wr(),o=e._private,l=e.isNode(),u=e.isEdge(),v,f,c,h,d,m,g=o.rstyle,p=l&&n?e.pstyle("bounds-expansion").pfValue:[0],y=function(Fe){return Fe.pstyle("display").value!=="none"},b=!n||y(e)&&(!u||y(e.source())&&y(e.target()));if(b){var w=0,E=0;n&&t.includeOverlays&&(w=e.pstyle("overlay-opacity").value,w!==0&&(E=e.pstyle("overlay-padding").value));var C=0,x=0;n&&t.includeUnderlays&&(C=e.pstyle("underlay-opacity").value,C!==0&&(x=e.pstyle("underlay-padding").value));var T=Math.max(E,x),S=0,P=0;if(n&&(S=e.pstyle("width").pfValue,P=S/2),l&&t.includeNodes){var D=e.position();d=D.x,m=D.y;var A=e.outerWidth(),B=A/2,R=e.outerHeight(),M=R/2;v=d-B,f=d+B,c=m-M,h=m+M,Ir(s,v,c,f,h),n&&sl(s,e),n&&t.includeOutlines&&!i&&sl(s,e),n&&_g(s,e)}else if(u&&t.includeEdges)if(n&&!i){var L=e.pstyle("curve-style").strValue;if(v=Math.min(g.srcX,g.midX,g.tgtX),f=Math.max(g.srcX,g.midX,g.tgtX),c=Math.min(g.srcY,g.midY,g.tgtY),h=Math.max(g.srcY,g.midY,g.tgtY),v-=P,f+=P,c-=P,h+=P,Ir(s,v,c,f,h),L==="haystack"){var I=g.haystackPts;if(I&&I.length===2){if(v=I[0].x,c=I[0].y,f=I[1].x,h=I[1].y,v>f){var O=v;v=f,f=O}if(c>h){var F=c;c=h,h=F}Ir(s,v-P,c-P,f+P,h+P)}}else if(L==="bezier"||L==="unbundled-bezier"||at(L,"segments")||at(L,"taxi")){var _;switch(L){case"bezier":case"unbundled-bezier":_=g.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":_=g.linePts;break}if(_!=null)for(var N=0;N<_.length;N++){var q=_[N];v=q.x-P,f=q.x+P,c=q.y-P,h=q.y+P,Ir(s,v,c,f,h)}}}else{var U=e.source(),X=U.position(),j=e.target(),J=j.position();if(v=X.x,f=J.x,c=X.y,h=J.y,v>f){var re=v;v=f,f=re}if(c>h){var ae=c;c=h,h=ae}v-=P,f+=P,c-=P,h+=P,Ir(s,v,c,f,h)}if(n&&t.includeEdges&&u&&(ja(s,e,"mid-source"),ja(s,e,"mid-target"),ja(s,e,"source"),ja(s,e,"target")),n){var Z=e.pstyle("ghost").value==="yes";if(Z){var z=e.pstyle("ghost-offset-x").pfValue,G=e.pstyle("ghost-offset-y").pfValue;Ir(s,s.x1+z,s.y1+G,s.x2+z,s.y2+G)}}var H=o.bodyBounds=o.bodyBounds||{};Uo(H,s),ln(H,p),un(H,1),n&&(v=s.x1,f=s.x2,c=s.y1,h=s.y2,Ir(s,v-T,c-T,f+T,h+T));var Q=o.overlayBounds=o.overlayBounds||{};Uo(Q,s),ln(Q,p),un(Q,1);var ne=o.labelBounds=o.labelBounds||{};ne.all!=null?wd(ne.all):ne.all=wr(),n&&t.includeLabels&&(t.includeMainLabels&&ps(s,e,null),u&&(t.includeSourceLabels&&ps(s,e,"source"),t.includeTargetLabels&&ps(s,e,"target")))}return s.x1=Ar(s.x1),s.y1=Ar(s.y1),s.x2=Ar(s.x2),s.y2=Ar(s.y2),s.w=Ar(s.x2-s.x1),s.h=Ar(s.y2-s.y1),s.w>0&&s.h>0&&b&&(ln(s,p),un(s,1)),s},Gv=function(e){var t=0,a=function(s){return(s?1:0)<<t++},n=0;return n+=a(e.incudeNodes),n+=a(e.includeEdges),n+=a(e.includeLabels),n+=a(e.includeMainLabels),n+=a(e.includeSourceLabels),n+=a(e.includeTargetLabels),n+=a(e.includeOverlays),n+=a(e.includeOutlines),n},Hv=function(e){var t=function(o){return Math.round(o)};if(e.isEdge()){var a=e.source().position(),n=e.target().position();return qo([t(a.x),t(a.y),t(n.x),t(n.y)])}else{var i=e.position();return qo([t(i.x),t(i.y)])}},ol=function(e,t){var a=e._private,n,i=e.isEdge(),s=t==null?ul:Gv(t),o=s===ul;if(a.bbCache==null?(n=Gg(e,Pa),a.bbCache=n,a.bbCachePosKey=Hv(e)):n=a.bbCache,!o){var l=e.isNode();n=wr(),(t.includeNodes&&l||t.includeEdges&&!l)&&(t.includeOverlays?tt(n,a.overlayBounds):tt(n,a.bodyBounds)),t.includeLabels&&(t.includeMainLabels&&(!i||t.includeSourceLabels&&t.includeTargetLabels)?tt(n,a.labelBounds.all):(t.includeMainLabels&&tt(n,a.labelBounds.mainRot),t.includeSourceLabels&&tt(n,a.labelBounds.sourceRot),t.includeTargetLabels&&tt(n,a.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},Pa={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,includeOutlines:!0,useCache:!0},ul=Gv(Pa),ll=cr(Pa);gt.boundingBox=function(r){var e,t=r===void 0||r.useCache===void 0||r.useCache===!0,a=Zt(function(v){var f=v._private;return f.bbCache==null||f.styleDirty||f.bbCachePosKey!==Hv(v)},function(v){return v.id()});if(t&&this.length===1&&!a(this[0]))r===void 0?r=Pa:r=ll(r),e=ol(this[0],r);else{e=wr(),r=r||Pa;var n=ll(r),i=this,s=i.cy(),o=s.styleEnabled();this.edges().forEach(a),this.nodes().forEach(a),o&&this.recalculateRenderedStyle(t),this.updateCompoundBounds(!t);for(var l=0;l<i.length;l++){var u=i[l];a(u)&&u.dirtyBoundingBoxCache(),tt(e,ol(u,n))}}return e.x1=Ar(e.x1),e.y1=Ar(e.y1),e.x2=Ar(e.x2),e.y2=Ar(e.y2),e.w=Ar(e.x2-e.x1),e.h=Ar(e.y2-e.y1),e};gt.dirtyBoundingBoxCache=function(){for(var r=0;r<this.length;r++){var e=this[r]._private;e.bbCache=null,e.bbCachePosKey=null,e.bodyBounds=null,e.overlayBounds=null,e.labelBounds.all=null,e.labelBounds.source=null,e.labelBounds.target=null,e.labelBounds.main=null,e.labelBounds.sourceRot=null,e.labelBounds.targetRot=null,e.labelBounds.mainRot=null,e.arrowBounds.source=null,e.arrowBounds.target=null,e.arrowBounds["mid-source"]=null,e.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this};gt.boundingBoxAt=function(r){var e=this.nodes(),t=this.cy(),a=t.hasCompoundNodes(),n=t.collection();if(a&&(n=e.filter(function(u){return u.isParent()}),e=e.not(n)),Re(r)){var i=r;r=function(){return i}}var s=function(v,f){return v._private.bbAtOldPos=r(v,f)},o=function(v){return v._private.bbAtOldPos};t.startBatch(),e.forEach(s).silentPositions(r),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0));var l=bd(this.boundingBox({useCache:!1}));return e.silentPositions(o),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0)),t.endBatch(),l};Yt.boundingbox=Yt.bb=Yt.boundingBox;Yt.renderedBoundingbox=Yt.renderedBoundingBox;var Hg=gt,ma,qa;ma=qa={};var Wv=function(e){e.uppercaseName=So(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=So(e.outerName),ma[e.name]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){if(a.isParent())return a.updateCompoundBounds(),n[e.autoName]||0;var o=a.pstyle(e.name);switch(o.strValue){case"label":return a.recalculateRenderedStyle(),n.rstyle[e.labelName]||0;default:return o.pfValue}}else return 1},ma["outer"+e.uppercaseName]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){var o=a[e.name](),l=a.pstyle("border-position").value,u;l==="center"?u=a.pstyle("border-width").pfValue:l==="outside"?u=2*a.pstyle("border-width").pfValue:u=0;var v=2*a.padding();return o+u+v}else return 1},ma["rendered"+e.uppercaseName]=function(){var a=this[0];if(a){var n=a[e.name]();return n*this.cy().zoom()}},ma["rendered"+e.uppercaseOuterName]=function(){var a=this[0];if(a){var n=a[e.outerName]();return n*this.cy().zoom()}}};Wv({name:"width"});Wv({name:"height"});qa.padding=function(){var r=this[0],e=r._private;return r.isParent()?(r.updateCompoundBounds(),e.autoPadding!==void 0?e.autoPadding:r.pstyle("padding").pfValue):r.pstyle("padding").pfValue};qa.paddedHeight=function(){var r=this[0];return r.height()+2*r.padding()};qa.paddedWidth=function(){var r=this[0];return r.width()+2*r.padding()};var Wg=qa,$g=function(e,t){if(e.isEdge()&&e.takesUpSpace())return t(e)},Ug=function(e,t){if(e.isEdge()&&e.takesUpSpace()){var a=e.cy();return On(t(e),a.zoom(),a.pan())}},Kg=function(e,t){if(e.isEdge()&&e.takesUpSpace()){var a=e.cy(),n=a.pan(),i=a.zoom();return t(e).map(function(s){return On(s,i,n)})}},Xg=function(e){return e.renderer().getControlPoints(e)},Yg=function(e){return e.renderer().getSegmentPoints(e)},Zg=function(e){return e.renderer().getSourceEndpoint(e)},Qg=function(e){return e.renderer().getTargetEndpoint(e)},Jg=function(e){return e.renderer().getEdgeMidpoint(e)},vl={controlPoints:{get:Xg,mult:!0},segmentPoints:{get:Yg,mult:!0},sourceEndpoint:{get:Zg},targetEndpoint:{get:Qg},midpoint:{get:Jg}},jg=function(e){return"rendered"+e[0].toUpperCase()+e.substr(1)},ep=Object.keys(vl).reduce(function(r,e){var t=vl[e],a=jg(e);return r[e]=function(){return $g(this,t.get)},t.mult?r[a]=function(){return Kg(this,t.get)}:r[a]=function(){return Ug(this,t.get)},r},{}),rp=pe({},qg,Hg,Wg,ep);/*!
Event object based on jQuery events, MIT license

https://jquery.org/license/
https://tldrlegal.com/license/mit-license
https://github.com/jquery/jquery/blob/master/src/event.js
*/var $v=function(e,t){this.recycle(e,t)};function ca(){return!1}function en(){return!0}$v.prototype={instanceString:function(){return"event"},recycle:function(e,t){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=ca,e!=null&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?en:ca):e!=null&&e.type?t=e:this.type=e,t!=null&&(this.originalEvent=t.originalEvent,this.type=t.type!=null?t.type:this.type,this.cy=t.cy,this.target=t.target,this.position=t.position,this.renderedPosition=t.renderedPosition,this.namespace=t.namespace,this.layout=t.layout),this.cy!=null&&this.position!=null&&this.renderedPosition==null){var a=this.position,n=this.cy.zoom(),i=this.cy.pan();this.renderedPosition={x:a.x*n+i.x,y:a.y*n+i.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=en;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=en;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=en,this.stopPropagation()},isDefaultPrevented:ca,isPropagationStopped:ca,isImmediatePropagationStopped:ca};var Uv=/^([^.]+)(\.(?:[^.]+))?$/,tp=".*",Kv={qualifierCompare:function(e,t){return e===t},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},fl=Object.keys(Kv),ap={};function Gn(){for(var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:ap,e=arguments.length>1?arguments[1]:void 0,t=0;t<fl.length;t++){var a=fl[t];this[a]=r[a]||Kv[a]}this.context=e||this.context,this.listeners=[],this.emitting=0}var ct=Gn.prototype,Xv=function(e,t,a,n,i,s,o){$e(n)&&(i=n,n=null),o&&(s==null?s=o:s=pe({},s,o));for(var l=ze(a)?a:a.split(/\s+/),u=0;u<l.length;u++){var v=l[u];if(!ot(v)){var f=v.match(Uv);if(f){var c=f[1],h=f[2]?f[2]:null,d=t(e,v,c,h,n,i,s);if(d===!1)break}}}},cl=function(e,t){return e.addEventFields(e.context,t),new $v(t.type,t)},np=function(e,t,a){if(vc(a)){t(e,a);return}else if(Re(a)){t(e,cl(e,a));return}for(var n=ze(a)?a:a.split(/\s+/),i=0;i<n.length;i++){var s=n[i];if(!ot(s)){var o=s.match(Uv);if(o){var l=o[1],u=o[2]?o[2]:null,v=cl(e,{type:l,namespace:u,target:e.context});t(e,v)}}}};ct.on=ct.addListener=function(r,e,t,a,n){return Xv(this,function(i,s,o,l,u,v,f){$e(v)&&i.listeners.push({event:s,callback:v,type:o,namespace:l,qualifier:u,conf:f})},r,e,t,a,n),this};ct.one=function(r,e,t,a){return this.on(r,e,t,a,{one:!0})};ct.removeListener=ct.off=function(r,e,t,a){var n=this;this.emitting!==0&&(this.listeners=Hc(this.listeners));for(var i=this.listeners,s=function(u){var v=i[u];Xv(n,function(f,c,h,d,m,g){if((v.type===h||r==="*")&&(!d&&v.namespace!==".*"||v.namespace===d)&&(!m||f.qualifierCompare(v.qualifier,m))&&(!g||v.callback===g))return i.splice(u,1),!1},r,e,t,a)},o=i.length-1;o>=0;o--)s(o);return this};ct.removeAllListeners=function(){return this.removeListener("*")};ct.emit=ct.trigger=function(r,e,t){var a=this.listeners,n=a.length;return this.emitting++,ze(e)||(e=[e]),np(this,function(i,s){t!=null&&(a=[{event:s.event,type:s.type,namespace:s.namespace,callback:t}],n=a.length);for(var o=function(){var v=a[l];if(v.type===s.type&&(!v.namespace||v.namespace===s.namespace||v.namespace===tp)&&i.eventMatches(i.context,v,s)){var f=[s];e!=null&&$c(f,e),i.beforeEmit(i.context,v,s),v.conf&&v.conf.one&&(i.listeners=i.listeners.filter(function(d){return d!==v}));var c=i.callbackContext(i.context,v,s),h=v.callback.apply(c,f);i.afterEmit(i.context,v,s),h===!1&&(s.stopPropagation(),s.preventDefault())}},l=0;l<n;l++)o();i.bubble(i.context)&&!s.isPropagationStopped()&&i.parent(i.context).emit(s,e)},r),this.emitting--,this};var ip={qualifierCompare:function(e,t){return e==null||t==null?e==null&&t==null:e.sameText(t)},eventMatches:function(e,t,a){var n=t.qualifier;return n!=null?e!==a.target&&Ia(a.target)&&n.matches(a.target):!0},addEventFields:function(e,t){t.cy=e.cy(),t.target=e},callbackContext:function(e,t,a){return t.qualifier!=null?a.target:e},beforeEmit:function(e,t){t.conf&&t.conf.once&&t.conf.onceCollection.removeListener(t.event,t.qualifier,t.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},rn=function(e){return de(e)?new vt(e):e},Yv={createEmitter:function(){for(var e=0;e<this.length;e++){var t=this[e],a=t._private;a.emitter||(a.emitter=new Gn(ip,t))}return this},emitter:function(){return this._private.emitter},on:function(e,t,a){for(var n=rn(t),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a)}return this},removeListener:function(e,t,a){for(var n=rn(t),i=0;i<this.length;i++){var s=this[i];s.emitter().removeListener(e,n,a)}return this},removeAllListeners:function(){for(var e=0;e<this.length;e++){var t=this[e];t.emitter().removeAllListeners()}return this},one:function(e,t,a){for(var n=rn(t),i=0;i<this.length;i++){var s=this[i];s.emitter().one(e,n,a)}return this},once:function(e,t,a){for(var n=rn(t),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a,{once:!0,onceCollection:this})}},emit:function(e,t){for(var a=0;a<this.length;a++){var n=this[a];n.emitter().emit(e,t)}return this},emitAndNotify:function(e,t){if(this.length!==0)return this.cy().notify(e,this),this.emit(e,t),this}};Le.eventAliasesOn(Yv);var Zv={nodes:function(e){return this.filter(function(t){return t.isNode()}).filter(e)},edges:function(e){return this.filter(function(t){return t.isEdge()}).filter(e)},byGroup:function(){for(var e=this.spawn(),t=this.spawn(),a=0;a<this.length;a++){var n=this[a];n.isNode()?e.push(n):t.push(n)}return{nodes:e,edges:t}},filter:function(e,t){if(e===void 0)return this;if(de(e)||Dr(e))return new vt(e).filter(this);if($e(e)){for(var a=this.spawn(),n=this,i=0;i<n.length;i++){var s=n[i],o=t?e.apply(t,[s,i,n]):e(s,i,n);o&&a.push(s)}return a}return this.spawn()},not:function(e){if(e){de(e)&&(e=this.filter(e));for(var t=this.spawn(),a=0;a<this.length;a++){var n=this[a],i=e.has(n);i||t.push(n)}return t}else return this},absoluteComplement:function(){var e=this.cy();return e.mutableElements().not(this)},intersect:function(e){if(de(e)){var t=e;return this.filter(t)}for(var a=this.spawn(),n=this,i=e,s=this.length<e.length,o=s?n:i,l=s?i:n,u=0;u<o.length;u++){var v=o[u];l.has(v)&&a.push(v)}return a},xor:function(e){var t=this._private.cy;de(e)&&(e=t.$(e));var a=this.spawn(),n=this,i=e,s=function(l,u){for(var v=0;v<l.length;v++){var f=l[v],c=f._private.data.id,h=u.hasElementWithId(c);h||a.push(f)}};return s(n,i),s(i,n),a},diff:function(e){var t=this._private.cy;de(e)&&(e=t.$(e));var a=this.spawn(),n=this.spawn(),i=this.spawn(),s=this,o=e,l=function(v,f,c){for(var h=0;h<v.length;h++){var d=v[h],m=d._private.data.id,g=f.hasElementWithId(m);g?i.merge(d):c.push(d)}};return l(s,o,a),l(o,s,n),{left:a,right:n,both:i}},add:function(e){var t=this._private.cy;if(!e)return this;if(de(e)){var a=e;e=t.mutableElements().filter(a)}for(var n=this.spawnSelf(),i=0;i<e.length;i++){var s=e[i],o=!this.has(s);o&&n.push(s)}return n},merge:function(e){var t=this._private,a=t.cy;if(!e)return this;if(e&&de(e)){var n=e;e=a.mutableElements().filter(n)}for(var i=t.map,s=0;s<e.length;s++){var o=e[s],l=o._private.data.id,u=!i.has(l);if(u){var v=this.length++;this[v]=o,i.set(l,{ele:o,index:v})}}return this},unmergeAt:function(e){var t=this[e],a=t.id(),n=this._private,i=n.map;this[e]=void 0,i.delete(a);var s=e===this.length-1;if(this.length>1&&!s){var o=this.length-1,l=this[o],u=l._private.data.id;this[o]=void 0,this[e]=l,i.set(u,{ele:l,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var t=this._private,a=e._private.data.id,n=t.map,i=n.get(a);if(!i)return this;var s=i.index;return this.unmergeAt(s),this},unmerge:function(e){var t=this._private.cy;if(!e)return this;if(e&&de(e)){var a=e;e=t.mutableElements().filter(a)}for(var n=0;n<e.length;n++)this.unmergeOne(e[n]);return this},unmergeBy:function(e){for(var t=this.length-1;t>=0;t--){var a=this[t];e(a)&&this.unmergeAt(t)}return this},map:function(e,t){for(var a=[],n=this,i=0;i<n.length;i++){var s=n[i],o=t?e.apply(t,[s,i,n]):e(s,i,n);a.push(o)}return a},reduce:function(e,t){for(var a=t,n=this,i=0;i<n.length;i++)a=e(a,n[i],i,n);return a},max:function(e,t){for(var a=-1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=t?e.apply(t,[o,s,i]):e(o,s,i);l>a&&(a=l,n=o)}return{value:a,ele:n}},min:function(e,t){for(var a=1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=t?e.apply(t,[o,s,i]):e(o,s,i);l<a&&(a=l,n=o)}return{value:a,ele:n}}},Me=Zv;Me.u=Me["|"]=Me["+"]=Me.union=Me.or=Me.add;Me["\\"]=Me["!"]=Me["-"]=Me.difference=Me.relativeComplement=Me.subtract=Me.not;Me.n=Me["&"]=Me["."]=Me.and=Me.intersection=Me.intersect;Me["^"]=Me["(+)"]=Me["(-)"]=Me.symmetricDifference=Me.symdiff=Me.xor;Me.fnFilter=Me.filterFn=Me.stdFilter=Me.filter;Me.complement=Me.abscomp=Me.absoluteComplement;var sp={isNode:function(){return this.group()==="nodes"},isEdge:function(){return this.group()==="edges"},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},Qv=function(e,t){var a=e.cy(),n=a.hasCompoundNodes();function i(v){var f=v.pstyle("z-compound-depth");return f.value==="auto"?n?v.zDepth():0:f.value==="bottom"?-1:f.value==="top"?Js:0}var s=i(e)-i(t);if(s!==0)return s;function o(v){var f=v.pstyle("z-index-compare");return f.value==="auto"&&v.isNode()?1:0}var l=o(e)-o(t);if(l!==0)return l;var u=e.pstyle("z-index").value-t.pstyle("z-index").value;return u!==0?u:e.poolIndex()-t.poolIndex()},Sn={forEach:function(e,t){if($e(e))for(var a=this.length,n=0;n<a;n++){var i=this[n],s=t?e.apply(t,[i,n,this]):e(i,n,this);if(s===!1)break}return this},toArray:function(){for(var e=[],t=0;t<this.length;t++)e.push(this[t]);return e},slice:function(e,t){var a=[],n=this.length;t==null&&(t=n),e==null&&(e=0),e<0&&(e=n+e),t<0&&(t=n+t);for(var i=e;i>=0&&i<t&&i<n;i++)a.push(this[i]);return this.spawn(a)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return this.length===0},nonempty:function(){return!this.empty()},sort:function(e){if(!$e(e))return this;var t=this.toArray().sort(e);return this.spawn(t)},sortByZIndex:function(){return this.sort(Qv)},zDepth:function(){var e=this[0];if(e){var t=e._private,a=t.group;if(a==="nodes"){var n=t.data.parent?e.parents().size():0;return e.isParent()?n:Js-1}else{var i=t.source,s=t.target,o=i.zDepth(),l=s.zDepth();return Math.max(o,l,0)}}}};Sn.each=Sn.forEach;var op=function(){var e="undefined",t=(typeof Symbol>"u"?"undefined":ar(Symbol))!=e&&ar(Symbol.iterator)!=e;t&&(Sn[Symbol.iterator]=function(){var a=this,n={value:void 0,done:!1},i=0,s=this.length;return Zl({next:function(){return i<s?n.value=a[i++]:(n.value=void 0,n.done=!0),n}},Symbol.iterator,function(){return this})})};op();var up=cr({nodeDimensionsIncludeLabels:!1}),fn={layoutDimensions:function(e){e=up(e);var t;if(!this.takesUpSpace())t={w:0,h:0};else if(e.nodeDimensionsIncludeLabels){var a=this.boundingBox();t={w:a.w,h:a.h}}else t={w:this.outerWidth(),h:this.outerHeight()};return(t.w===0||t.h===0)&&(t.w=t.h=1),t},layoutPositions:function(e,t,a){var n=this.nodes().filter(function(E){return!E.isParent()}),i=this.cy(),s=t.eles,o=function(C){return C.id()},l=Zt(a,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var u=function(C,x,T){var S={x:x.x1+x.w/2,y:x.y1+x.h/2},P={x:(T.x-S.x)*C,y:(T.y-S.y)*C};return{x:S.x+P.x,y:S.y+P.y}},v=t.spacingFactor&&t.spacingFactor!==1,f=function(){if(!v)return null;for(var C=wr(),x=0;x<n.length;x++){var T=n[x],S=l(T,x);pv(C,S.x,S.y)}return C},c=f(),h=Zt(function(E,C){var x=l(E,C);if(v){var T=Math.abs(t.spacingFactor);x=u(T,c,x)}return t.transform!=null&&(x=t.transform(E,x)),x},o);if(t.animate){for(var d=0;d<n.length;d++){var m=n[d],g=h(m,d),p=t.animateFilter==null||t.animateFilter(m,d);if(p){var y=m.animation({position:g,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(y)}else m.position(g)}if(t.fit){var b=i.animation({fit:{boundingBox:s.boundingBoxAt(h),padding:t.padding},duration:t.animationDuration,easing:t.animationEasing});e.animations.push(b)}else if(t.zoom!==void 0&&t.pan!==void 0){var w=i.animation({zoom:t.zoom,pan:t.pan,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(w)}e.animations.forEach(function(E){return E.play()}),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),ta.all(e.animations.map(function(E){return E.promise()})).then(function(){e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e})})}else n.positions(h),t.fit&&i.fit(t.eles,t.padding),t.zoom!=null&&i.zoom(t.zoom),t.pan&&i.pan(t.pan),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){var t=this.cy();return t.makeLayout(pe({},e,{eles:this}))}};fn.createLayout=fn.makeLayout=fn.layout;function Jv(r,e,t){var a=t._private,n=a.styleCache=a.styleCache||[],i;return(i=n[r])!=null||(i=n[r]=e(t)),i}function Hn(r,e){return r=kt(r),function(a){return Jv(r,e,a)}}function Wn(r,e){r=kt(r);var t=function(n){return e.call(n)};return function(){var n=this[0];if(n)return Jv(r,t,n)}}var vr={recalculateRenderedStyle:function(e){var t=this.cy(),a=t.renderer(),n=t.styleEnabled();return a&&n&&a.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e=this.cy(),t=function(i){return i._private.styleCache=null};if(e.hasCompoundNodes()){var a;a=this.spawnSelf().merge(this.descendants()).merge(this.parents()),a.merge(a.connectedEdges()),a.forEach(t)}else this.forEach(function(n){t(n),n.connectedEdges().forEach(t)});return this},updateStyle:function(e){var t=this._private.cy;if(!t.styleEnabled())return this;if(t.batching()){var a=t._private.batchStyleEles;return a.merge(this),this}var n=t.hasCompoundNodes(),i=this;e=!!(e||e===void 0),n&&(i=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var s=i;return e?s.emitAndNotify("style"):s.emit("style"),i.forEach(function(o){return o._private.styleDirty=!0}),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var t=0;t<this.length;t++){var a=this[t];a._private.styleDirty&&(a._private.styleDirty=!1,e.style().apply(a))}},parsedStyle:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,a=this[0],n=a.cy();if(n.styleEnabled()&&a){a._private.styleDirty&&(a._private.styleDirty=!1,n.style().apply(a));var i=a._private.style[e];return i??(t?n.style().getDefaultProperty(e):null)}},numericStyle:function(e){var t=this[0];if(t.cy().styleEnabled()&&t){var a=t.pstyle(e);return a.pfValue!==void 0?a.pfValue:a.value}},numericStyleUnits:function(e){var t=this[0];if(t.cy().styleEnabled()&&t)return t.pstyle(e).units},renderedStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var a=this[0];if(a)return t.style().getRenderedStyle(a,e)},style:function(e,t){var a=this.cy();if(!a.styleEnabled())return this;var n=!1,i=a.style();if(Re(e)){var s=e;i.applyBypass(this,s,n),this.emitAndNotify("style")}else if(de(e))if(t===void 0){var o=this[0];return o?i.getStylePropertyValue(o,e):void 0}else i.applyBypass(this,e,t,n),this.emitAndNotify("style");else if(e===void 0){var l=this[0];return l?i.getRawStyle(l):void 0}return this},removeStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var a=!1,n=t.style(),i=this;if(e===void 0)for(var s=0;s<i.length;s++){var o=i[s];n.removeAllBypasses(o,a)}else{e=e.split(/\s+/);for(var l=0;l<i.length;l++){var u=i[l];n.removeBypasses(u,e,a)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var t=e.hasCompoundNodes(),a=this[0];if(a){var n=a._private,i=a.pstyle("opacity").value;if(!t)return i;var s=n.data.parent?a.parents():null;if(s)for(var o=0;o<s.length;o++){var l=s[o],u=l.pstyle("opacity").value;i=u*i}return i}},transparent:function(){var e=this.cy();if(!e.styleEnabled())return!1;var t=this[0],a=t.cy().hasCompoundNodes();if(t)return a?t.effectiveOpacity()===0:t.pstyle("opacity").value===0},backgrounding:function(){var e=this.cy();if(!e.styleEnabled())return!1;var t=this[0];return!!t._private.backgrounding}};function ys(r,e){var t=r._private,a=t.data.parent?r.parents():null;if(a)for(var n=0;n<a.length;n++){var i=a[n];if(!e(i))return!1}return!0}function vo(r){var e=r.ok,t=r.edgeOkViaNode||r.ok,a=r.parentOk||r.ok;return function(){var n=this.cy();if(!n.styleEnabled())return!0;var i=this[0],s=n.hasCompoundNodes();if(i){var o=i._private;if(!e(i))return!1;if(i.isNode())return!s||ys(i,a);var l=o.source,u=o.target;return t(l)&&(!s||ys(l,t))&&(l===u||t(u)&&(!s||ys(u,t)))}}}var aa=Hn("eleTakesUpSpace",function(r){return r.pstyle("display").value==="element"&&r.width()!==0&&(r.isNode()?r.height()!==0:!0)});vr.takesUpSpace=Wn("takesUpSpace",vo({ok:aa}));var lp=Hn("eleInteractive",function(r){return r.pstyle("events").value==="yes"&&r.pstyle("visibility").value==="visible"&&aa(r)}),vp=Hn("parentInteractive",function(r){return r.pstyle("visibility").value==="visible"&&aa(r)});vr.interactive=Wn("interactive",vo({ok:lp,parentOk:vp,edgeOkViaNode:aa}));vr.noninteractive=function(){var r=this[0];if(r)return!r.interactive()};var fp=Hn("eleVisible",function(r){return r.pstyle("visibility").value==="visible"&&r.pstyle("opacity").pfValue!==0&&aa(r)}),cp=aa;vr.visible=Wn("visible",vo({ok:fp,edgeOkViaNode:cp}));vr.hidden=function(){var r=this[0];if(r)return!r.visible()};vr.isBundledBezier=Wn("isBundledBezier",function(){return this.cy().styleEnabled()?!this.removed()&&this.pstyle("curve-style").value==="bezier"&&this.takesUpSpace():!1});vr.bypass=vr.css=vr.style;vr.renderedCss=vr.renderedStyle;vr.removeBypass=vr.removeCss=vr.removeStyle;vr.pstyle=vr.parsedStyle;var st={};function dl(r){return function(){var e=arguments,t=[];if(e.length===2){var a=e[0],n=e[1];this.on(r.event,a,n)}else if(e.length===1&&$e(e[0])){var i=e[0];this.on(r.event,i)}else if(e.length===0||e.length===1&&ze(e[0])){for(var s=e.length===1?e[0]:null,o=0;o<this.length;o++){var l=this[o],u=!r.ableField||l._private[r.ableField],v=l._private[r.field]!=r.value;if(r.overrideAble){var f=r.overrideAble(l);if(f!==void 0&&(u=f,!f))return this}u&&(l._private[r.field]=r.value,v&&t.push(l))}var c=this.spawn(t);c.updateStyle(),c.emit(r.event),s&&c.emit(s)}return this}}function na(r){st[r.field]=function(){var e=this[0];if(e){if(r.overrideField){var t=r.overrideField(e);if(t!==void 0)return t}return e._private[r.field]}},st[r.on]=dl({event:r.on,field:r.field,ableField:r.ableField,overrideAble:r.overrideAble,value:!0}),st[r.off]=dl({event:r.off,field:r.field,ableField:r.ableField,overrideAble:r.overrideAble,value:!1})}na({field:"locked",overrideField:function(e){return e.cy().autolock()?!0:void 0},on:"lock",off:"unlock"});na({field:"grabbable",overrideField:function(e){return e.cy().autoungrabify()||e.pannable()?!1:void 0},on:"grabify",off:"ungrabify"});na({field:"selected",ableField:"selectable",overrideAble:function(e){return e.cy().autounselectify()?!1:void 0},on:"select",off:"unselect"});na({field:"selectable",overrideField:function(e){return e.cy().autounselectify()?!1:void 0},on:"selectify",off:"unselectify"});st.deselect=st.unselect;st.grabbed=function(){var r=this[0];if(r)return r._private.grabbed};na({field:"active",on:"activate",off:"unactivate"});na({field:"pannable",on:"panify",off:"unpanify"});st.inactive=function(){var r=this[0];if(r)return!r._private.active};var gr={},hl=function(e){return function(a){for(var n=this,i=[],s=0;s<n.length;s++){var o=n[s];if(o.isNode()){for(var l=!1,u=o.connectedEdges(),v=0;v<u.length;v++){var f=u[v],c=f.source(),h=f.target();if(e.noIncomingEdges&&h===o&&c!==o||e.noOutgoingEdges&&c===o&&h!==o){l=!0;break}}l||i.push(o)}}return this.spawn(i,!0).filter(a)}},gl=function(e){return function(t){for(var a=this,n=[],i=0;i<a.length;i++){var s=a[i];if(s.isNode())for(var o=s.connectedEdges(),l=0;l<o.length;l++){var u=o[l],v=u.source(),f=u.target();e.outgoing&&v===s?(n.push(u),n.push(f)):e.incoming&&f===s&&(n.push(u),n.push(v))}}return this.spawn(n,!0).filter(t)}},pl=function(e){return function(t){for(var a=this,n=[],i={};;){var s=e.outgoing?a.outgoers():a.incomers();if(s.length===0)break;for(var o=!1,l=0;l<s.length;l++){var u=s[l],v=u.id();i[v]||(i[v]=!0,n.push(u),o=!0)}if(!o)break;a=s}return this.spawn(n,!0).filter(t)}};gr.clearTraversalCache=function(){for(var r=0;r<this.length;r++)this[r]._private.traversalCache=null};pe(gr,{roots:hl({noIncomingEdges:!0}),leaves:hl({noOutgoingEdges:!0}),outgoers:Rr(gl({outgoing:!0}),"outgoers"),successors:pl({outgoing:!0}),incomers:Rr(gl({incoming:!0}),"incomers"),predecessors:pl({})});pe(gr,{neighborhood:Rr(function(r){for(var e=[],t=this.nodes(),a=0;a<t.length;a++)for(var n=t[a],i=n.connectedEdges(),s=0;s<i.length;s++){var o=i[s],l=o.source(),u=o.target(),v=n===l?u:l;v.length>0&&e.push(v[0]),e.push(o[0])}return this.spawn(e,!0).filter(r)},"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}});gr.neighbourhood=gr.neighborhood;gr.closedNeighbourhood=gr.closedNeighborhood;gr.openNeighbourhood=gr.openNeighborhood;pe(gr,{source:Rr(function(e){var t=this[0],a;return t&&(a=t._private.source||t.cy().collection()),a&&e?a.filter(e):a},"source"),target:Rr(function(e){var t=this[0],a;return t&&(a=t._private.target||t.cy().collection()),a&&e?a.filter(e):a},"target"),sources:yl({attr:"source"}),targets:yl({attr:"target"})});function yl(r){return function(t){for(var a=[],n=0;n<this.length;n++){var i=this[n],s=i._private[r.attr];s&&a.push(s)}return this.spawn(a,!0).filter(t)}}pe(gr,{edgesWith:Rr(ml(),"edgesWith"),edgesTo:Rr(ml({thisIsSrc:!0}),"edgesTo")});function ml(r){return function(t){var a=[],n=this._private.cy,i=r||{};de(t)&&(t=n.$(t));for(var s=0;s<t.length;s++)for(var o=t[s]._private.edges,l=0;l<o.length;l++){var u=o[l],v=u._private.data,f=this.hasElementWithId(v.source)&&t.hasElementWithId(v.target),c=t.hasElementWithId(v.source)&&this.hasElementWithId(v.target),h=f||c;h&&((i.thisIsSrc||i.thisIsTgt)&&(i.thisIsSrc&&!f||i.thisIsTgt&&!c)||a.push(u))}return this.spawn(a,!0)}}pe(gr,{connectedEdges:Rr(function(r){for(var e=[],t=this,a=0;a<t.length;a++){var n=t[a];if(n.isNode())for(var i=n._private.edges,s=0;s<i.length;s++){var o=i[s];e.push(o)}}return this.spawn(e,!0).filter(r)},"connectedEdges"),connectedNodes:Rr(function(r){for(var e=[],t=this,a=0;a<t.length;a++){var n=t[a];n.isEdge()&&(e.push(n.source()[0]),e.push(n.target()[0]))}return this.spawn(e,!0).filter(r)},"connectedNodes"),parallelEdges:Rr(bl(),"parallelEdges"),codirectedEdges:Rr(bl({codirected:!0}),"codirectedEdges")});function bl(r){var e={codirected:!1};return r=pe({},e,r),function(a){for(var n=[],i=this.edges(),s=r,o=0;o<i.length;o++)for(var l=i[o],u=l._private,v=u.source,f=v._private.data.id,c=u.data.target,h=v._private.edges,d=0;d<h.length;d++){var m=h[d],g=m._private.data,p=g.target,y=g.source,b=p===c&&y===f,w=f===p&&c===y;(s.codirected&&b||!s.codirected&&(b||w))&&n.push(m)}return this.spawn(n,!0).filter(a)}}pe(gr,{components:function(e){var t=this,a=t.cy(),n=a.collection(),i=e==null?t.nodes():e.nodes(),s=[];e!=null&&i.empty()&&(i=e.sources());var o=function(v,f){n.merge(v),i.unmerge(v),f.merge(v)};if(i.empty())return t.spawn();var l=function(){var v=a.collection();s.push(v);var f=i[0];o(f,v),t.bfs({directed:!1,roots:f,visit:function(h){return o(h,v)}}),v.forEach(function(c){c.connectedEdges().forEach(function(h){t.has(h)&&v.has(h.source())&&v.has(h.target())&&v.merge(h)})})};do l();while(i.length>0);return s},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}});gr.componentsOf=gr.components;var fr=function(e,t){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e===void 0){We("A collection must have a reference to the core");return}var i=new Xr,s=!1;if(!t)t=[];else if(t.length>0&&Re(t[0])&&!Ia(t[0])){s=!0;for(var o=[],l=new ra,u=0,v=t.length;u<v;u++){var f=t[u];f.data==null&&(f.data={});var c=f.data;if(c.id==null)c.id=dv();else if(e.hasElementWithId(c.id)||l.has(c.id))continue;var h=new In(e,f,!1);o.push(h),l.add(c.id)}t=o}this.length=0;for(var d=0,m=t.length;d<m;d++){var g=t[d][0];if(g!=null){var p=g._private.data.id;(!a||!i.has(p))&&(a&&i.set(p,{index:this.length,ele:g}),this[this.length]=g,this.length++)}}this._private={eles:this,cy:e,get map(){return this.lazyMap==null&&this.rebuildMap(),this.lazyMap},set map(y){this.lazyMap=y},rebuildMap:function(){for(var b=this.lazyMap=new Xr,w=this.eles,E=0;E<w.length;E++){var C=w[E];b.set(C.id(),{index:E,ele:C})}}},a&&(this._private.map=i),s&&!n&&this.restore()},_e=In.prototype=fr.prototype=Object.create(Array.prototype);_e.instanceString=function(){return"collection"};_e.spawn=function(r,e){return new fr(this.cy(),r,e)};_e.spawnSelf=function(){return this.spawn(this)};_e.cy=function(){return this._private.cy};_e.renderer=function(){return this._private.cy.renderer()};_e.element=function(){return this[0]};_e.collection=function(){return jl(this)?this:new fr(this._private.cy,[this])};_e.unique=function(){return new fr(this._private.cy,this,!0)};_e.hasElementWithId=function(r){return r=""+r,this._private.map.has(r)};_e.getElementById=function(r){r=""+r;var e=this._private.cy,t=this._private.map.get(r);return t?t.ele:new fr(e)};_e.$id=_e.getElementById;_e.poolIndex=function(){var r=this._private.cy,e=r._private.elements,t=this[0]._private.data.id;return e._private.map.get(t).index};_e.indexOf=function(r){var e=r[0]._private.data.id;return this._private.map.get(e).index};_e.indexOfId=function(r){return r=""+r,this._private.map.get(r).index};_e.json=function(r){var e=this.element(),t=this.cy();if(e==null&&r)return this;if(e!=null){var a=e._private;if(Re(r)){if(t.startBatch(),r.data){e.data(r.data);var n=a.data;if(e.isEdge()){var i=!1,s={},o=r.data.source,l=r.data.target;o!=null&&o!=n.source&&(s.source=""+o,i=!0),l!=null&&l!=n.target&&(s.target=""+l,i=!0),i&&(e=e.move(s))}else{var u="parent"in r.data,v=r.data.parent;u&&(v!=null||n.parent!=null)&&v!=n.parent&&(v===void 0&&(v=null),v!=null&&(v=""+v),e=e.move({parent:v}))}}r.position&&e.position(r.position);var f=function(m,g,p){var y=r[m];y!=null&&y!==a[m]&&(y?e[g]():e[p]())};return f("removed","remove","restore"),f("selected","select","unselect"),f("selectable","selectify","unselectify"),f("locked","lock","unlock"),f("grabbable","grabify","ungrabify"),f("pannable","panify","unpanify"),r.classes!=null&&e.classes(r.classes),t.endBatch(),this}else if(r===void 0){var c={data:qr(a.data),position:qr(a.position),group:a.group,removed:a.removed,selected:a.selected,selectable:a.selectable,locked:a.locked,grabbable:a.grabbable,pannable:a.pannable,classes:null};c.classes="";var h=0;return a.classes.forEach(function(d){return c.classes+=h++===0?d:" "+d}),c}}};_e.jsons=function(){for(var r=[],e=0;e<this.length;e++){var t=this[e],a=t.json();r.push(a)}return r};_e.clone=function(){for(var r=this.cy(),e=[],t=0;t<this.length;t++){var a=this[t],n=a.json(),i=new In(r,n,!1);e.push(i)}return new fr(r,e)};_e.copy=_e.clone;_e.restore=function(){for(var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,t=this,a=t.cy(),n=a._private,i=[],s=[],o,l=0,u=t.length;l<u;l++){var v=t[l];e&&!v.removed()||(v.isNode()?i.push(v):s.push(v))}o=i.concat(s);var f,c=function(){o.splice(f,1),f--};for(f=0;f<o.length;f++){var h=o[f],d=h._private,m=d.data;if(h.clearTraversalCache(),!(!e&&!d.removed)){if(m.id===void 0)m.id=dv();else if(te(m.id))m.id=""+m.id;else if(ot(m.id)||!de(m.id)){We("Can not create element with invalid string ID `"+m.id+"`"),c();continue}else if(a.hasElementWithId(m.id)){We("Can not create second element with ID `"+m.id+"`"),c();continue}}var g=m.id;if(h.isNode()){var p=d.position;p.x==null&&(p.x=0),p.y==null&&(p.y=0)}if(h.isEdge()){for(var y=h,b=["source","target"],w=b.length,E=!1,C=0;C<w;C++){var x=b[C],T=m[x];te(T)&&(T=m[x]=""+m[x]),T==null||T===""?(We("Can not create edge `"+g+"` with unspecified "+x),E=!0):a.hasElementWithId(T)||(We("Can not create edge `"+g+"` with nonexistant "+x+" `"+T+"`"),E=!0)}if(E){c();continue}var S=a.getElementById(m.source),P=a.getElementById(m.target);S.same(P)?S._private.edges.push(y):(S._private.edges.push(y),P._private.edges.push(y)),y._private.source=S,y._private.target=P}d.map=new Xr,d.map.set(g,{ele:h,index:0}),d.removed=!1,e&&a.addToPool(h)}for(var D=0;D<i.length;D++){var A=i[D],B=A._private.data;te(B.parent)&&(B.parent=""+B.parent);var R=B.parent,M=R!=null;if(M||A._private.parent){var L=A._private.parent?a.collection().merge(A._private.parent):a.getElementById(R);if(L.empty())B.parent=void 0;else if(L[0].removed())Ie("Node added with missing parent, reference to parent removed"),B.parent=void 0,A._private.parent=null;else{for(var I=!1,O=L;!O.empty();){if(A.same(O)){I=!0,B.parent=void 0;break}O=O.parent()}I||(L[0]._private.children.push(A),A._private.parent=L[0],n.hasCompoundNodes=!0)}}}if(o.length>0){for(var F=o.length===t.length?t:new fr(a,o),_=0;_<F.length;_++){var N=F[_];N.isNode()||(N.parallelEdges().clearTraversalCache(),N.source().clearTraversalCache(),N.target().clearTraversalCache())}var q;n.hasCompoundNodes?q=a.collection().merge(F).merge(F.connectedNodes()).merge(F.parent()):q=F,q.dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(r),r?F.emitAndNotify("add"):e&&F.emit("add")}return t};_e.removed=function(){var r=this[0];return r&&r._private.removed};_e.inside=function(){var r=this[0];return r&&!r._private.removed};_e.remove=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,t=this,a=[],n={},i=t._private.cy;function s(R){for(var M=R._private.edges,L=0;L<M.length;L++)l(M[L])}function o(R){for(var M=R._private.children,L=0;L<M.length;L++)l(M[L])}function l(R){var M=n[R.id()];e&&R.removed()||M||(n[R.id()]=!0,R.isNode()?(a.push(R),s(R),o(R)):a.unshift(R))}for(var u=0,v=t.length;u<v;u++){var f=t[u];l(f)}function c(R,M){var L=R._private.edges;ut(L,M),R.clearTraversalCache()}function h(R){R.clearTraversalCache()}var d=[];d.ids={};function m(R,M){M=M[0],R=R[0];var L=R._private.children,I=R.id();ut(L,M),M._private.parent=null,d.ids[I]||(d.ids[I]=!0,d.push(R))}t.dirtyCompoundBoundsCache(),e&&i.removeFromPool(a);for(var g=0;g<a.length;g++){var p=a[g];if(p.isEdge()){var y=p.source()[0],b=p.target()[0];c(y,p),c(b,p);for(var w=p.parallelEdges(),E=0;E<w.length;E++){var C=w[E];h(C),C.isBundledBezier()&&C.dirtyBoundingBoxCache()}}else{var x=p.parent();x.length!==0&&m(x,p)}e&&(p._private.removed=!0)}var T=i._private.elements;i._private.hasCompoundNodes=!1;for(var S=0;S<T.length;S++){var P=T[S];if(P.isParent()){i._private.hasCompoundNodes=!0;break}}var D=new fr(this.cy(),a);D.size()>0&&(r?D.emitAndNotify("remove"):e&&D.emit("remove"));for(var A=0;A<d.length;A++){var B=d[A];(!e||!B.removed())&&B.updateStyle()}return D};_e.move=function(r){var e=this._private.cy,t=this,a=!1,n=!1,i=function(d){return d==null?d:""+d};if(r.source!==void 0||r.target!==void 0){var s=i(r.source),o=i(r.target),l=s!=null&&e.hasElementWithId(s),u=o!=null&&e.hasElementWithId(o);(l||u)&&(e.batch(function(){t.remove(a,n),t.emitAndNotify("moveout");for(var h=0;h<t.length;h++){var d=t[h],m=d._private.data;d.isEdge()&&(l&&(m.source=s),u&&(m.target=o))}t.restore(a,n)}),t.emitAndNotify("move"))}else if(r.parent!==void 0){var v=i(r.parent),f=v===null||e.hasElementWithId(v);if(f){var c=v===null?void 0:v;e.batch(function(){var h=t.remove(a,n);h.emitAndNotify("moveout");for(var d=0;d<t.length;d++){var m=t[d],g=m._private.data;m.isNode()&&(g.parent=c)}h.restore(a,n)}),t.emitAndNotify("move")}}return this};[Tv,Cg,vn,it,jt,Vg,_n,rp,Yv,Zv,sp,Sn,fn,vr,st,gr].forEach(function(r){pe(_e,r)});var dp={add:function(e){var t,a=this;if(Dr(e)){var n=e;if(n._private.cy===a)t=n.restore();else{for(var i=[],s=0;s<n.length;s++){var o=n[s];i.push(o.json())}t=new fr(a,i)}}else if(ze(e)){var l=e;t=new fr(a,l)}else if(Re(e)&&(ze(e.nodes)||ze(e.edges))){for(var u=e,v=[],f=["nodes","edges"],c=0,h=f.length;c<h;c++){var d=f[c],m=u[d];if(ze(m))for(var g=0,p=m.length;g<p;g++){var y=pe({group:d},m[g]);v.push(y)}}t=new fr(a,v)}else{var b=e;t=new In(a,b).collection()}return t},remove:function(e){if(!Dr(e)){if(de(e)){var t=e;e=this.$(t)}}return e.remove()}};/*! Bezier curve function generator. Copyright Gaetan Renaudeau. MIT License: http://en.wikipedia.org/wiki/MIT_License */function hp(r,e,t,a){var n=4,i=.001,s=1e-7,o=10,l=11,u=1/(l-1),v=typeof Float32Array<"u";if(arguments.length!==4)return!1;for(var f=0;f<4;++f)if(typeof arguments[f]!="number"||isNaN(arguments[f])||!isFinite(arguments[f]))return!1;r=Math.min(r,1),t=Math.min(t,1),r=Math.max(r,0),t=Math.max(t,0);var c=v?new Float32Array(l):new Array(l);function h(P,D){return 1-3*D+3*P}function d(P,D){return 3*D-6*P}function m(P){return 3*P}function g(P,D,A){return((h(D,A)*P+d(D,A))*P+m(D))*P}function p(P,D,A){return 3*h(D,A)*P*P+2*d(D,A)*P+m(D)}function y(P,D){for(var A=0;A<n;++A){var B=p(D,r,t);if(B===0)return D;var R=g(D,r,t)-P;D-=R/B}return D}function b(){for(var P=0;P<l;++P)c[P]=g(P*u,r,t)}function w(P,D,A){var B,R,M=0;do R=D+(A-D)/2,B=g(R,r,t)-P,B>0?A=R:D=R;while(Math.abs(B)>s&&++M<o);return R}function E(P){for(var D=0,A=1,B=l-1;A!==B&&c[A]<=P;++A)D+=u;--A;var R=(P-c[A])/(c[A+1]-c[A]),M=D+R*u,L=p(M,r,t);return L>=i?y(P,M):L===0?M:w(P,D,D+u)}var C=!1;function x(){C=!0,(r!==e||t!==a)&&b()}var T=function(D){return C||x(),r===e&&t===a?D:D===0?0:D===1?1:g(E(D),e,a)};T.getControlPoints=function(){return[{x:r,y:e},{x:t,y:a}]};var S="generateBezier("+[r,e,t,a]+")";return T.toString=function(){return S},T}/*! Runge-Kutta spring physics function generator. Adapted from Framer.js, copyright Koen Bok. MIT License: http://en.wikipedia.org/wiki/MIT_License */var gp=function(){function r(a){return-a.tension*a.x-a.friction*a.v}function e(a,n,i){var s={x:a.x+i.dx*n,v:a.v+i.dv*n,tension:a.tension,friction:a.friction};return{dx:s.v,dv:r(s)}}function t(a,n){var i={dx:a.v,dv:r(a)},s=e(a,n*.5,i),o=e(a,n*.5,s),l=e(a,n,o),u=1/6*(i.dx+2*(s.dx+o.dx)+l.dx),v=1/6*(i.dv+2*(s.dv+o.dv)+l.dv);return a.x=a.x+u*n,a.v=a.v+v*n,a}return function a(n,i,s){var o={x:-1,v:0,tension:null,friction:null},l=[0],u=0,v=1/1e4,f=16/1e3,c,h,d;for(n=parseFloat(n)||500,i=parseFloat(i)||20,s=s||null,o.tension=n,o.friction=i,c=s!==null,c?(u=a(n,i),h=u/s*f):h=f;d=t(d||o,h),l.push(1+d.x),u+=16,Math.abs(d.x)>v&&Math.abs(d.v)>v;);return c?function(m){return l[m*(l.length-1)|0]}:u}}(),qe=function(e,t,a,n){var i=hp(e,t,a,n);return function(s,o,l){return s+(o-s)*i(l)}},cn={linear:function(e,t,a){return e+(t-e)*a},ease:qe(.25,.1,.25,1),"ease-in":qe(.42,0,1,1),"ease-out":qe(0,0,.58,1),"ease-in-out":qe(.42,0,.58,1),"ease-in-sine":qe(.47,0,.745,.715),"ease-out-sine":qe(.39,.575,.565,1),"ease-in-out-sine":qe(.445,.05,.55,.95),"ease-in-quad":qe(.55,.085,.68,.53),"ease-out-quad":qe(.25,.46,.45,.94),"ease-in-out-quad":qe(.455,.03,.515,.955),"ease-in-cubic":qe(.55,.055,.675,.19),"ease-out-cubic":qe(.215,.61,.355,1),"ease-in-out-cubic":qe(.645,.045,.355,1),"ease-in-quart":qe(.895,.03,.685,.22),"ease-out-quart":qe(.165,.84,.44,1),"ease-in-out-quart":qe(.77,0,.175,1),"ease-in-quint":qe(.755,.05,.855,.06),"ease-out-quint":qe(.23,1,.32,1),"ease-in-out-quint":qe(.86,0,.07,1),"ease-in-expo":qe(.95,.05,.795,.035),"ease-out-expo":qe(.19,1,.22,1),"ease-in-out-expo":qe(1,0,0,1),"ease-in-circ":qe(.6,.04,.98,.335),"ease-out-circ":qe(.075,.82,.165,1),"ease-in-out-circ":qe(.785,.135,.15,.86),spring:function(e,t,a){if(a===0)return cn.linear;var n=gp(e,t,a);return function(i,s,o){return i+(s-i)*n(o)}},"cubic-bezier":qe};function wl(r,e,t,a,n){if(a===1||e===t)return t;var i=n(e,t,a);return r==null||((r.roundValue||r.color)&&(i=Math.round(i)),r.min!==void 0&&(i=Math.max(i,r.min)),r.max!==void 0&&(i=Math.min(i,r.max))),i}function xl(r,e){return r.pfValue!=null||r.value!=null?r.pfValue!=null&&(e==null||e.type.units!=="%")?r.pfValue:r.value:r}function Nt(r,e,t,a,n){var i=n!=null?n.type:null;t<0?t=0:t>1&&(t=1);var s=xl(r,n),o=xl(e,n);if(te(s)&&te(o))return wl(i,s,o,t,a);if(ze(s)&&ze(o)){for(var l=[],u=0;u<o.length;u++){var v=s[u],f=o[u];if(v!=null&&f!=null){var c=wl(i,v,f,t,a);l.push(c)}else l.push(f)}return l}}function pp(r,e,t,a){var n=!a,i=r._private,s=e._private,o=s.easing,l=s.startTime,u=a?r:r.cy(),v=u.style();if(!s.easingImpl)if(o==null)s.easingImpl=cn.linear;else{var f;if(de(o)){var c=v.parse("transition-timing-function",o);f=c.value}else f=o;var h,d;de(f)?(h=f,d=[]):(h=f[1],d=f.slice(2).map(function(F){return+F})),d.length>0?(h==="spring"&&d.push(s.duration),s.easingImpl=cn[h].apply(null,d)):s.easingImpl=cn[h]}var m=s.easingImpl,g;if(s.duration===0?g=1:g=(t-l)/s.duration,s.applying&&(g=s.progress),g<0?g=0:g>1&&(g=1),s.delay==null){var p=s.startPosition,y=s.position;if(y&&n&&!r.locked()){var b={};da(p.x,y.x)&&(b.x=Nt(p.x,y.x,g,m)),da(p.y,y.y)&&(b.y=Nt(p.y,y.y,g,m)),r.position(b)}var w=s.startPan,E=s.pan,C=i.pan,x=E!=null&&a;x&&(da(w.x,E.x)&&(C.x=Nt(w.x,E.x,g,m)),da(w.y,E.y)&&(C.y=Nt(w.y,E.y,g,m)),r.emit("pan"));var T=s.startZoom,S=s.zoom,P=S!=null&&a;P&&(da(T,S)&&(i.zoom=ka(i.minZoom,Nt(T,S,g,m),i.maxZoom)),r.emit("zoom")),(x||P)&&r.emit("viewport");var D=s.style;if(D&&D.length>0&&n){for(var A=0;A<D.length;A++){var B=D[A],R=B.name,M=B,L=s.startStyle[R],I=v.properties[L.name],O=Nt(L,M,g,m,I);v.overrideBypass(r,R,O)}r.emit("style")}}return s.progress=g,g}function da(r,e){return r==null||e==null?!1:te(r)&&te(e)?!0:!!(r&&e)}function yp(r,e,t,a){var n=e._private;n.started=!0,n.startTime=t-n.progress*n.duration}function El(r,e){var t=e._private.aniEles,a=[];function n(v,f){var c=v._private,h=c.animation.current,d=c.animation.queue,m=!1;if(h.length===0){var g=d.shift();g&&h.push(g)}for(var p=function(C){for(var x=C.length-1;x>=0;x--){var T=C[x];T()}C.splice(0,C.length)},y=h.length-1;y>=0;y--){var b=h[y],w=b._private;if(w.stopped){h.splice(y,1),w.hooked=!1,w.playing=!1,w.started=!1,p(w.frames);continue}!w.playing&&!w.applying||(w.playing&&w.applying&&(w.applying=!1),w.started||yp(v,b,r),pp(v,b,r,f),w.applying&&(w.applying=!1),p(w.frames),w.step!=null&&w.step(r),b.completed()&&(h.splice(y,1),w.hooked=!1,w.playing=!1,w.started=!1,p(w.completes)),m=!0)}return!f&&h.length===0&&d.length===0&&a.push(v),m}for(var i=!1,s=0;s<t.length;s++){var o=t[s],l=n(o);i=i||l}var u=n(e,!0);(i||u)&&(t.length>0?e.notify("draw",t):e.notify("draw")),t.unmerge(a),e.emit("step")}var mp={animate:Le.animate(),animation:Le.animation(),animated:Le.animated(),clearQueue:Le.clearQueue(),delay:Le.delay(),delayAnimation:Le.delayAnimation(),stop:Le.stop(),addToAnimationPool:function(e){var t=this;t.styleEnabled()&&t._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,!e.styleEnabled())return;function t(){e._private.animationsRunning&&wn(function(i){El(i,e),t()})}var a=e.renderer();a&&a.beforeRender?a.beforeRender(function(i,s){El(s,e)},a.beforeRenderPriorities.animations):t()}},bp={qualifierCompare:function(e,t){return e==null||t==null?e==null&&t==null:e.sameText(t)},eventMatches:function(e,t,a){var n=t.qualifier;return n!=null?e!==a.target&&Ia(a.target)&&n.matches(a.target):!0},addEventFields:function(e,t){t.cy=e,t.target=e},callbackContext:function(e,t,a){return t.qualifier!=null?a.target:e}},tn=function(e){return de(e)?new vt(e):e},jv={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new Gn(bp,this)),this},emitter:function(){return this._private.emitter},on:function(e,t,a){return this.emitter().on(e,tn(t),a),this},removeListener:function(e,t,a){return this.emitter().removeListener(e,tn(t),a),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,t,a){return this.emitter().one(e,tn(t),a),this},once:function(e,t,a){return this.emitter().one(e,tn(t),a),this},emit:function(e,t){return this.emitter().emit(e,t),this},emitAndNotify:function(e,t){return this.emit(e),this.notify(e,t),this}};Le.eventAliasesOn(jv);var zs={png:function(e){var t=this._private.renderer;return e=e||{},t.png(e)},jpg:function(e){var t=this._private.renderer;return e=e||{},e.bg=e.bg||"#fff",t.jpg(e)}};zs.jpeg=zs.jpg;var dn={layout:function(e){var t=this;if(e==null){We("Layout options must be specified to make a layout");return}if(e.name==null){We("A `name` must be specified to make a layout");return}var a=e.name,n=t.extension("layout",a);if(n==null){We("No such layout `"+a+"` found.  Did you forget to import it and `cytoscape.use()` it?");return}var i;de(e.eles)?i=t.$(e.eles):i=e.eles!=null?e.eles:t.$();var s=new n(pe({},e,{cy:t,eles:i}));return s}};dn.createLayout=dn.makeLayout=dn.layout;var wp={notify:function(e,t){var a=this._private;if(this.batching()){a.batchNotifications=a.batchNotifications||{};var n=a.batchNotifications[e]=a.batchNotifications[e]||this.collection();t!=null&&n.merge(t);return}if(a.notificationsEnabled){var i=this.renderer();this.destroyed()||!i||i.notify(e,t)}},notifications:function(e){var t=this._private;return e===void 0?t.notificationsEnabled:(t.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return e.batchCount==null&&(e.batchCount=0),e.batchCount===0&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(e.batchCount===0)return this;if(e.batchCount--,e.batchCount===0){e.batchStyleEles.updateStyle();var t=this.renderer();Object.keys(e.batchNotifications).forEach(function(a){var n=e.batchNotifications[a];n.empty()?t.notify(a):t.notify(a,n)})}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var t=this;return this.batch(function(){for(var a=Object.keys(e),n=0;n<a.length;n++){var i=a[n],s=e[i],o=t.getElementById(i);o.data(s)}})}},xp=cr({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1,webgl:!1,webglDebug:!1,webglDebugShowAtlases:!1,webglTexSize:2048,webglTexRows:36,webglTexRowsNodes:18,webglBatchSize:2048,webglTexPerBatch:14,webglBgColor:[255,255,255]}),Fs={renderTo:function(e,t,a,n){var i=this._private.renderer;return i.renderTo(e,t,a,n),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var t=this,a=t.extension("renderer",e.name);if(a==null){We("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"));return}e.wheelSensitivity!==void 0&&Ie("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var n=xp(e);n.cy=t,t._private.renderer=new a(n),this.notify("init")},destroyRenderer:function(){var e=this;e.notify("destroy");var t=e.container();if(t)for(t._cyreg=null;t.childNodes.length>0;)t.removeChild(t.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach(function(a){var n=a._private;n.rscratch={},n.rstyle={},n.animation.current=[],n.animation.queue=[]})},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};Fs.invalidateDimensions=Fs.resize;var hn={collection:function(e,t){return de(e)?this.$(e):Dr(e)?e.collection():ze(e)?(t||(t={}),new fr(this,e,t.unique,t.removed)):new fr(this)},nodes:function(e){var t=this.$(function(a){return a.isNode()});return e?t.filter(e):t},edges:function(e){var t=this.$(function(a){return a.isEdge()});return e?t.filter(e):t},$:function(e){var t=this._private.elements;return e?t.filter(e):t.spawnSelf()},mutableElements:function(){return this._private.elements}};hn.elements=hn.filter=hn.$;var ur={},wa="t",Ep="f";ur.apply=function(r){for(var e=this,t=e._private,a=t.cy,n=a.collection(),i=0;i<r.length;i++){var s=r[i],o=e.getContextMeta(s);if(!o.empty){var l=e.getContextStyle(o),u=e.applyContextStyle(o,l,s);s._private.appliedInitStyle?e.updateTransitions(s,u.diffProps):s._private.appliedInitStyle=!0;var v=e.updateStyleHints(s);v&&n.push(s)}}return n};ur.getPropertiesDiff=function(r,e){var t=this,a=t._private.propDiffs=t._private.propDiffs||{},n=r+"-"+e,i=a[n];if(i)return i;for(var s=[],o={},l=0;l<t.length;l++){var u=t[l],v=r[l]===wa,f=e[l]===wa,c=v!==f,h=u.mappedProperties.length>0;if(c||f&&h){var d=void 0;c&&h||c?d=u.properties:h&&(d=u.mappedProperties);for(var m=0;m<d.length;m++){for(var g=d[m],p=g.name,y=!1,b=l+1;b<t.length;b++){var w=t[b],E=e[b]===wa;if(E&&(y=w.properties[g.name]!=null,y))break}!o[p]&&!y&&(o[p]=!0,s.push(p))}}}return a[n]=s,s};ur.getContextMeta=function(r){for(var e=this,t="",a,n=r._private.styleCxtKey||"",i=0;i<e.length;i++){var s=e[i],o=s.selector&&s.selector.matches(r);o?t+=wa:t+=Ep}return a=e.getPropertiesDiff(n,t),r._private.styleCxtKey=t,{key:t,diffPropNames:a,empty:a.length===0}};ur.getContextStyle=function(r){var e=r.key,t=this,a=this._private.contextStyles=this._private.contextStyles||{};if(a[e])return a[e];for(var n={_private:{key:e}},i=0;i<t.length;i++){var s=t[i],o=e[i]===wa;if(o)for(var l=0;l<s.properties.length;l++){var u=s.properties[l];n[u.name]=u}}return a[e]=n,n};ur.applyContextStyle=function(r,e,t){for(var a=this,n=r.diffPropNames,i={},s=a.types,o=0;o<n.length;o++){var l=n[o],u=e[l],v=t.pstyle(l);if(!u)if(v)v.bypass?u={name:l,deleteBypassed:!0}:u={name:l,delete:!0};else continue;if(v!==u){if(u.mapped===s.fn&&v!=null&&v.mapping!=null&&v.mapping.value===u.value){var f=v.mapping,c=f.fnValue=u.value(t);if(c===f.prevFnValue)continue}var h=i[l]={prev:v};a.applyParsedProperty(t,u),h.next=t.pstyle(l),h.next&&h.next.bypass&&(h.next=h.next.bypassed)}}return{diffProps:i}};ur.updateStyleHints=function(r){var e=r._private,t=this,a=t.propertyGroupNames,n=t.propertyGroupKeys,i=function(H,Q,ne){return t.getPropertiesHash(H,Q,ne)},s=e.styleKey;if(r.removed())return!1;var o=e.group==="nodes",l=r._private.style;a=Object.keys(l);for(var u=0;u<n.length;u++){var v=n[u];e.styleKeys[v]=[Ct,Gt]}for(var f=function(H,Q){return e.styleKeys[Q][0]=Ca(H,e.styleKeys[Q][0])},c=function(H,Q){return e.styleKeys[Q][1]=Ta(H,e.styleKeys[Q][1])},h=function(H,Q){f(H,Q),c(H,Q)},d=function(H,Q){for(var ne=0;ne<H.length;ne++){var be=H.charCodeAt(ne);f(be,Q),c(be,Q)}},m=2e9,g=function(H){return-128<H&&H<128&&Math.floor(H)!==H?m-(H*1024|0):H},p=0;p<a.length;p++){var y=a[p],b=l[y];if(b!=null){var w=this.properties[y],E=w.type,C=w.groupKey,x=void 0;w.hashOverride!=null?x=w.hashOverride(r,b):b.pfValue!=null&&(x=b.pfValue);var T=w.enums==null?b.value:null,S=x!=null,P=T!=null,D=S||P,A=b.units;if(E.number&&D&&!E.multiple){var B=S?x:T;h(g(B),C),!S&&A!=null&&d(A,C)}else d(b.strValue,C)}}for(var R=[Ct,Gt],M=0;M<n.length;M++){var L=n[M],I=e.styleKeys[L];R[0]=Ca(I[0],R[0]),R[1]=Ta(I[1],R[1])}e.styleKey=Fc(R[0],R[1]);var O=e.styleKeys;e.labelDimsKey=et(O.labelDimensions);var F=i(r,["label"],O.labelDimensions);if(e.labelKey=et(F),e.labelStyleKey=et(Xa(O.commonLabel,F)),!o){var _=i(r,["source-label"],O.labelDimensions);e.sourceLabelKey=et(_),e.sourceLabelStyleKey=et(Xa(O.commonLabel,_));var N=i(r,["target-label"],O.labelDimensions);e.targetLabelKey=et(N),e.targetLabelStyleKey=et(Xa(O.commonLabel,N))}if(o){var q=e.styleKeys,U=q.nodeBody,X=q.nodeBorder,j=q.nodeOutline,J=q.backgroundImage,re=q.compound,ae=q.pie,Z=q.stripe,z=[U,X,j,J,re,ae,Z].filter(function(G){return G!=null}).reduce(Xa,[Ct,Gt]);e.nodeKey=et(z),e.hasPie=ae!=null&&ae[0]!==Ct&&ae[1]!==Gt,e.hasStripe=Z!=null&&Z[0]!==Ct&&Z[1]!==Gt}return s!==e.styleKey};ur.clearStyleHints=function(r){var e=r._private;e.styleCxtKey="",e.styleKeys={},e.styleKey=null,e.labelKey=null,e.labelStyleKey=null,e.sourceLabelKey=null,e.sourceLabelStyleKey=null,e.targetLabelKey=null,e.targetLabelStyleKey=null,e.nodeKey=null,e.hasPie=null,e.hasStripe=null};ur.applyParsedProperty=function(r,e){var t=this,a=e,n=r._private.style,i,s=t.types,o=t.properties[a.name].type,l=a.bypass,u=n[a.name],v=u&&u.bypass,f=r._private,c="mapping",h=function(U){return U==null?null:U.pfValue!=null?U.pfValue:U.value},d=function(){var U=h(u),X=h(a);t.checkTriggers(r,a.name,U,X)};if(e.name==="curve-style"&&r.isEdge()&&(e.value!=="bezier"&&r.isLoop()||e.value==="haystack"&&(r.source().isParent()||r.target().isParent()))&&(a=e=this.parse(e.name,"bezier",l)),a.delete)return n[a.name]=void 0,d(),!0;if(a.deleteBypassed)return u?u.bypass?(u.bypassed=void 0,d(),!0):!1:(d(),!0);if(a.deleteBypass)return u?u.bypass?(n[a.name]=u.bypassed,d(),!0):!1:(d(),!0);var m=function(){Ie("Do not assign mappings to elements without corresponding data (i.e. ele `"+r.id()+"` has no mapping for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")};switch(a.mapped){case s.mapData:{for(var g=a.field.split("."),p=f.data,y=0;y<g.length&&p;y++){var b=g[y];p=p[b]}if(p==null)return m(),!1;var w;if(te(p)){var E=a.fieldMax-a.fieldMin;E===0?w=0:w=(p-a.fieldMin)/E}else return Ie("Do not use continuous mappers without specifying numeric data (i.e. `"+a.field+": "+p+"` for `"+r.id()+"` is non-numeric)"),!1;if(w<0?w=0:w>1&&(w=1),o.color){var C=a.valueMin[0],x=a.valueMax[0],T=a.valueMin[1],S=a.valueMax[1],P=a.valueMin[2],D=a.valueMax[2],A=a.valueMin[3]==null?1:a.valueMin[3],B=a.valueMax[3]==null?1:a.valueMax[3],R=[Math.round(C+(x-C)*w),Math.round(T+(S-T)*w),Math.round(P+(D-P)*w),Math.round(A+(B-A)*w)];i={bypass:a.bypass,name:a.name,value:R,strValue:"rgb("+R[0]+", "+R[1]+", "+R[2]+")"}}else if(o.number){var M=a.valueMin+(a.valueMax-a.valueMin)*w;i=this.parse(a.name,M,a.bypass,c)}else return!1;if(!i)return m(),!1;i.mapping=a,a=i;break}case s.data:{for(var L=a.field.split("."),I=f.data,O=0;O<L.length&&I;O++){var F=L[O];I=I[F]}if(I!=null&&(i=this.parse(a.name,I,a.bypass,c)),!i)return m(),!1;i.mapping=a,a=i;break}case s.fn:{var _=a.value,N=a.fnValue!=null?a.fnValue:_(r);if(a.prevFnValue=N,N==null)return Ie("Custom function mappers may not return null (i.e. `"+a.name+"` for ele `"+r.id()+"` is null)"),!1;if(i=this.parse(a.name,N,a.bypass,c),!i)return Ie("Custom function mappers may not return invalid values for the property type (i.e. `"+a.name+"` for ele `"+r.id()+"` is invalid)"),!1;i.mapping=qr(a),a=i;break}case void 0:break;default:return!1}return l?(v?a.bypassed=u.bypassed:a.bypassed=u,n[a.name]=a):v?u.bypassed=a:n[a.name]=a,d(),!0};ur.cleanElements=function(r,e){for(var t=0;t<r.length;t++){var a=r[t];if(this.clearStyleHints(a),a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),!e)a._private.style={};else for(var n=a._private.style,i=Object.keys(n),s=0;s<i.length;s++){var o=i[s],l=n[o];l!=null&&(l.bypass?l.bypassed=null:n[o]=null)}}};ur.update=function(){var r=this._private.cy,e=r.mutableElements();e.updateStyle()};ur.updateTransitions=function(r,e){var t=this,a=r._private,n=r.pstyle("transition-property").value,i=r.pstyle("transition-duration").pfValue,s=r.pstyle("transition-delay").pfValue;if(n.length>0&&i>0){for(var o={},l=!1,u=0;u<n.length;u++){var v=n[u],f=r.pstyle(v),c=e[v];if(c){var h=c.prev,d=h,m=c.next!=null?c.next:f,g=!1,p=void 0,y=1e-6;d&&(te(d.pfValue)&&te(m.pfValue)?(g=m.pfValue-d.pfValue,p=d.pfValue+y*g):te(d.value)&&te(m.value)?(g=m.value-d.value,p=d.value+y*g):ze(d.value)&&ze(m.value)&&(g=d.value[0]!==m.value[0]||d.value[1]!==m.value[1]||d.value[2]!==m.value[2],p=d.strValue),g&&(o[v]=m.strValue,this.applyBypass(r,v,p),l=!0))}}if(!l)return;a.transitioning=!0,new ta(function(b){s>0?r.delayAnimation(s).play().promise().then(b):b()}).then(function(){return r.animation({style:o,duration:i,easing:r.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){t.removeBypasses(r,n),r.emitAndNotify("style"),a.transitioning=!1})}else a.transitioning&&(this.removeBypasses(r,n),r.emitAndNotify("style"),a.transitioning=!1)};ur.checkTrigger=function(r,e,t,a,n,i){var s=this.properties[e],o=n(s);r.removed()||o!=null&&o(t,a,r)&&i(s)};ur.checkZOrderTrigger=function(r,e,t,a){var n=this;this.checkTrigger(r,e,t,a,function(i){return i.triggersZOrder},function(){n._private.cy.notify("zorder",r)})};ur.checkBoundsTrigger=function(r,e,t,a){this.checkTrigger(r,e,t,a,function(n){return n.triggersBounds},function(n){r.dirtyCompoundBoundsCache(),r.dirtyBoundingBoxCache()})};ur.checkConnectedEdgesBoundsTrigger=function(r,e,t,a){this.checkTrigger(r,e,t,a,function(n){return n.triggersBoundsOfConnectedEdges},function(n){r.connectedEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})};ur.checkParallelEdgesBoundsTrigger=function(r,e,t,a){this.checkTrigger(r,e,t,a,function(n){return n.triggersBoundsOfParallelEdges},function(n){r.parallelEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})};ur.checkTriggers=function(r,e,t,a){r.dirtyStyleCache(),this.checkZOrderTrigger(r,e,t,a),this.checkBoundsTrigger(r,e,t,a),this.checkConnectedEdgesBoundsTrigger(r,e,t,a),this.checkParallelEdgesBoundsTrigger(r,e,t,a)};var _a={};_a.applyBypass=function(r,e,t,a){var n=this,i=[],s=!0;if(e==="*"||e==="**"){if(t!==void 0)for(var o=0;o<n.properties.length;o++){var l=n.properties[o],u=l.name,v=this.parse(u,t,!0);v&&i.push(v)}}else if(de(e)){var f=this.parse(e,t,!0);f&&i.push(f)}else if(Re(e)){var c=e;a=t;for(var h=Object.keys(c),d=0;d<h.length;d++){var m=h[d],g=c[m];if(g===void 0&&(g=c[Mn(m)]),g!==void 0){var p=this.parse(m,g,!0);p&&i.push(p)}}}else return!1;if(i.length===0)return!1;for(var y=!1,b=0;b<r.length;b++){for(var w=r[b],E={},C=void 0,x=0;x<i.length;x++){var T=i[x];if(a){var S=w.pstyle(T.name);C=E[T.name]={prev:S}}y=this.applyParsedProperty(w,qr(T))||y,a&&(C.next=w.pstyle(T.name))}y&&this.updateStyleHints(w),a&&this.updateTransitions(w,E,s)}return y};_a.overrideBypass=function(r,e,t){e=Zs(e);for(var a=0;a<r.length;a++){var n=r[a],i=n._private.style[e],s=this.properties[e].type,o=s.color,l=s.mutiple,u=i?i.pfValue!=null?i.pfValue:i.value:null;!i||!i.bypass?this.applyBypass(n,e,t):(i.value=t,i.pfValue!=null&&(i.pfValue=t),o?i.strValue="rgb("+t.join(",")+")":l?i.strValue=t.join(" "):i.strValue=""+t,this.updateStyleHints(n)),this.checkTriggers(n,e,u,t)}};_a.removeAllBypasses=function(r,e){return this.removeBypasses(r,this.propertyNames,e)};_a.removeBypasses=function(r,e,t){for(var a=!0,n=0;n<r.length;n++){for(var i=r[n],s={},o=0;o<e.length;o++){var l=e[o],u=this.properties[l],v=i.pstyle(u.name);if(!(!v||!v.bypass)){var f="",c=this.parse(l,f,!0),h=s[u.name]={prev:v};this.applyParsedProperty(i,c),h.next=i.pstyle(u.name)}}this.updateStyleHints(i),t&&this.updateTransitions(i,s,a)}};var fo={};fo.getEmSizeInPixels=function(){var r=this.containerCss("font-size");return r!=null?parseFloat(r):1};fo.containerCss=function(r){var e=this._private.cy,t=e.container(),a=e.window();if(a&&t&&a.getComputedStyle)return a.getComputedStyle(t).getPropertyValue(r)};var _r={};_r.getRenderedStyle=function(r,e){return e?this.getStylePropertyValue(r,e,!0):this.getRawStyle(r,!0)};_r.getRawStyle=function(r,e){var t=this;if(r=r[0],r){for(var a={},n=0;n<t.properties.length;n++){var i=t.properties[n],s=t.getStylePropertyValue(r,i.name,e);s!=null&&(a[i.name]=s,a[Mn(i.name)]=s)}return a}};_r.getIndexedStyle=function(r,e,t,a){var n=r.pstyle(e)[t][a];return n??r.cy().style().getDefaultProperty(e)[t][0]};_r.getStylePropertyValue=function(r,e,t){var a=this;if(r=r[0],r){var n=a.properties[e];n.alias&&(n=n.pointsTo);var i=n.type,s=r.pstyle(n.name);if(s){var o=s.value,l=s.units,u=s.strValue;if(t&&i.number&&o!=null&&te(o)){var v=r.cy().zoom(),f=function(g){return g*v},c=function(g,p){return f(g)+p},h=ze(o),d=h?l.every(function(m){return m!=null}):l!=null;return d?h?o.map(function(m,g){return c(m,l[g])}).join(" "):c(o,l):h?o.map(function(m){return de(m)?m:""+f(m)}).join(" "):""+f(o)}else if(u!=null)return u}return null}};_r.getAnimationStartStyle=function(r,e){for(var t={},a=0;a<e.length;a++){var n=e[a],i=n.name,s=r.pstyle(i);s!==void 0&&(Re(s)?s=this.parse(i,s.strValue):s=this.parse(i,s)),s&&(t[i]=s)}return t};_r.getPropsList=function(r){var e=this,t=[],a=r,n=e.properties;if(a)for(var i=Object.keys(a),s=0;s<i.length;s++){var o=i[s],l=a[o],u=n[o]||n[Zs(o)],v=this.parse(u.name,l);v&&t.push(v)}return t};_r.getNonDefaultPropertiesHash=function(r,e,t){var a=t.slice(),n,i,s,o,l,u;for(l=0;l<e.length;l++)if(n=e[l],i=r.pstyle(n,!1),i!=null)if(i.pfValue!=null)a[0]=Ca(o,a[0]),a[1]=Ta(o,a[1]);else for(s=i.strValue,u=0;u<s.length;u++)o=s.charCodeAt(u),a[0]=Ca(o,a[0]),a[1]=Ta(o,a[1]);return a};_r.getPropertiesHash=_r.getNonDefaultPropertiesHash;var $n={};$n.appendFromJson=function(r){for(var e=this,t=0;t<r.length;t++){var a=r[t],n=a.selector,i=a.style||a.css,s=Object.keys(i);e.selector(n);for(var o=0;o<s.length;o++){var l=s[o],u=i[l];e.css(l,u)}}return e};$n.fromJson=function(r){var e=this;return e.resetToDefault(),e.appendFromJson(r),e};$n.json=function(){for(var r=[],e=this.defaultLength;e<this.length;e++){for(var t=this[e],a=t.selector,n=t.properties,i={},s=0;s<n.length;s++){var o=n[s];i[o.name]=o.strValue}r.push({selector:a?a.toString():"core",style:i})}return r};var co={};co.appendFromString=function(r){var e=this,t=this,a=""+r,n,i,s;a=a.replace(/[/][*](\s|.)+?[*][/]/g,"");function o(){a.length>n.length?a=a.substr(n.length):a=""}function l(){i.length>s.length?i=i.substr(s.length):i=""}for(;;){var u=a.match(/^\s*$/);if(u)break;var v=a.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!v){Ie("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+a);break}n=v[0];var f=v[1];if(f!=="core"){var c=new vt(f);if(c.invalid){Ie("Skipping parsing of block: Invalid selector found in string stylesheet: "+f),o();continue}}var h=v[2],d=!1;i=h;for(var m=[];;){var g=i.match(/^\s*$/);if(g)break;var p=i.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!p){Ie("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+h),d=!0;break}s=p[0];var y=p[1],b=p[2],w=e.properties[y];if(!w){Ie("Skipping property: Invalid property name in: "+s),l();continue}var E=t.parse(y,b);if(!E){Ie("Skipping property: Invalid property definition in: "+s),l();continue}m.push({name:y,val:b}),l()}if(d){o();break}t.selector(f);for(var C=0;C<m.length;C++){var x=m[C];t.css(x.name,x.val)}o()}return t};co.fromString=function(r){var e=this;return e.resetToDefault(),e.appendFromString(r),e};var Qe={};(function(){var r=tr,e=pc,t=mc,a=bc,n=wc,i=function(G){return"^"+G+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},s=function(G){var H=r+"|\\w+|"+e+"|"+t+"|"+a+"|"+n;return"^"+G+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+r+")\\s*\\,\\s*("+r+")\\s*,\\s*("+H+")\\s*\\,\\s*("+H+")\\)$"},o=[`^url\\s*\\(\\s*['"]?(.+?)['"]?\\s*\\)$`,"^(none)$","^(.+)$"];Qe.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},nonNegativeNumber:{number:!0,min:0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},axisDirectionExplicit:{enums:["leftward","rightward","upward","downward"]},axisDirectionPrimary:{enums:["horizontal","vertical"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:!0},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:!0,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:!0,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:i("data")},layoutData:{mapping:!0,regex:i("layoutData")},scratch:{mapping:!0,regex:i("scratch")},mapData:{mapping:!0,regex:s("mapData")},mapLayoutData:{mapping:!0,regex:s("mapLayoutData")},mapScratch:{mapping:!0,regex:s("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:o,singleRegexMatchValue:!0},urls:{regexes:o,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(G,H){switch(G.length){case 2:return H[0]!=="deg"&&H[0]!=="rad"&&H[1]!=="deg"&&H[1]!=="rad";case 1:return de(G[0])||H[0]==="deg"||H[0]==="rad";default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+r+")\\s*,\\s*("+r+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+r+")\\s*,\\s*("+r+")\\s*,\\s*("+r+")\\s*,\\s*("+r+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(G){var H=G.length;return H===1||H===2||H===4}}};var l={zeroNonZero:function(G,H){return(G==null||H==null)&&G!==H||G==0&&H!=0?!0:G!=0&&H==0},any:function(G,H){return G!=H},emptyNonEmpty:function(G,H){var Q=ot(G),ne=ot(H);return Q&&!ne||!Q&&ne}},u=Qe.types,v=[{name:"label",type:u.text,triggersBounds:l.any,triggersZOrder:l.emptyNonEmpty},{name:"text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any}],f=[{name:"source-label",type:u.text,triggersBounds:l.any},{name:"source-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"source-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-offset",type:u.size,triggersBounds:l.any}],c=[{name:"target-label",type:u.text,triggersBounds:l.any},{name:"target-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"target-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-offset",type:u.size,triggersBounds:l.any}],h=[{name:"font-family",type:u.fontFamily,triggersBounds:l.any},{name:"font-style",type:u.fontStyle,triggersBounds:l.any},{name:"font-weight",type:u.fontWeight,triggersBounds:l.any},{name:"font-size",type:u.size,triggersBounds:l.any},{name:"text-transform",type:u.textTransform,triggersBounds:l.any},{name:"text-wrap",type:u.textWrap,triggersBounds:l.any},{name:"text-overflow-wrap",type:u.textOverflowWrap,triggersBounds:l.any},{name:"text-max-width",type:u.size,triggersBounds:l.any},{name:"text-outline-width",type:u.size,triggersBounds:l.any},{name:"line-height",type:u.positiveNumber,triggersBounds:l.any}],d=[{name:"text-valign",type:u.valign,triggersBounds:l.any},{name:"text-halign",type:u.halign,triggersBounds:l.any},{name:"color",type:u.color},{name:"text-outline-color",type:u.color},{name:"text-outline-opacity",type:u.zeroOneNumber},{name:"text-background-color",type:u.color},{name:"text-background-opacity",type:u.zeroOneNumber},{name:"text-background-padding",type:u.size,triggersBounds:l.any},{name:"text-border-opacity",type:u.zeroOneNumber},{name:"text-border-color",type:u.color},{name:"text-border-width",type:u.size,triggersBounds:l.any},{name:"text-border-style",type:u.borderStyle,triggersBounds:l.any},{name:"text-background-shape",type:u.textBackgroundShape,triggersBounds:l.any},{name:"text-justification",type:u.justification},{name:"box-select-labels",type:u.bool,triggersBounds:l.any}],m=[{name:"events",type:u.bool,triggersZOrder:l.any},{name:"text-events",type:u.bool,triggersZOrder:l.any}],g=[{name:"display",type:u.display,triggersZOrder:l.any,triggersBounds:l.any,triggersBoundsOfConnectedEdges:l.any,triggersBoundsOfParallelEdges:function(G,H,Q){return G===H?!1:Q.pstyle("curve-style").value==="bezier"}},{name:"visibility",type:u.visibility,triggersZOrder:l.any},{name:"opacity",type:u.zeroOneNumber,triggersZOrder:l.zeroNonZero},{name:"text-opacity",type:u.zeroOneNumber},{name:"min-zoomed-font-size",type:u.size},{name:"z-compound-depth",type:u.zCompoundDepth,triggersZOrder:l.any},{name:"z-index-compare",type:u.zIndexCompare,triggersZOrder:l.any},{name:"z-index",type:u.number,triggersZOrder:l.any}],p=[{name:"overlay-padding",type:u.size,triggersBounds:l.any},{name:"overlay-color",type:u.color},{name:"overlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"overlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"overlay-corner-radius",type:u.cornerRadius}],y=[{name:"underlay-padding",type:u.size,triggersBounds:l.any},{name:"underlay-color",type:u.color},{name:"underlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"underlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"underlay-corner-radius",type:u.cornerRadius}],b=[{name:"transition-property",type:u.propList},{name:"transition-duration",type:u.time},{name:"transition-delay",type:u.time},{name:"transition-timing-function",type:u.easing}],w=function(G,H){return H.value==="label"?-G.poolIndex():H.pfValue},E=[{name:"height",type:u.nodeSize,triggersBounds:l.any,hashOverride:w},{name:"width",type:u.nodeSize,triggersBounds:l.any,hashOverride:w},{name:"shape",type:u.nodeShape,triggersBounds:l.any},{name:"shape-polygon-points",type:u.polygonPointList,triggersBounds:l.any},{name:"corner-radius",type:u.cornerRadius},{name:"background-color",type:u.color},{name:"background-fill",type:u.fill},{name:"background-opacity",type:u.zeroOneNumber},{name:"background-blacken",type:u.nOneOneNumber},{name:"background-gradient-stop-colors",type:u.colors},{name:"background-gradient-stop-positions",type:u.percentages},{name:"background-gradient-direction",type:u.gradientDirection},{name:"padding",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"padding-relative-to",type:u.paddingRelativeTo,triggersBounds:l.any},{name:"bounds-expansion",type:u.boundsExpansion,triggersBounds:l.any}],C=[{name:"border-color",type:u.color},{name:"border-opacity",type:u.zeroOneNumber},{name:"border-width",type:u.size,triggersBounds:l.any},{name:"border-style",type:u.borderStyle},{name:"border-cap",type:u.lineCap},{name:"border-join",type:u.lineJoin},{name:"border-dash-pattern",type:u.numbers},{name:"border-dash-offset",type:u.number},{name:"border-position",type:u.linePosition}],x=[{name:"outline-color",type:u.color},{name:"outline-opacity",type:u.zeroOneNumber},{name:"outline-width",type:u.size,triggersBounds:l.any},{name:"outline-style",type:u.borderStyle},{name:"outline-offset",type:u.size,triggersBounds:l.any}],T=[{name:"background-image",type:u.urls},{name:"background-image-crossorigin",type:u.bgCrossOrigin},{name:"background-image-opacity",type:u.zeroOneNumbers},{name:"background-image-containment",type:u.bgContainment},{name:"background-image-smoothing",type:u.bools},{name:"background-position-x",type:u.bgPos},{name:"background-position-y",type:u.bgPos},{name:"background-width-relative-to",type:u.bgRelativeTo},{name:"background-height-relative-to",type:u.bgRelativeTo},{name:"background-repeat",type:u.bgRepeat},{name:"background-fit",type:u.bgFit},{name:"background-clip",type:u.bgClip},{name:"background-width",type:u.bgWH},{name:"background-height",type:u.bgWH},{name:"background-offset-x",type:u.bgPos},{name:"background-offset-y",type:u.bgPos}],S=[{name:"position",type:u.position,triggersBounds:l.any},{name:"compound-sizing-wrt-labels",type:u.compoundIncludeLabels,triggersBounds:l.any},{name:"min-width",type:u.size,triggersBounds:l.any},{name:"min-width-bias-left",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-width-bias-right",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height",type:u.size,triggersBounds:l.any},{name:"min-height-bias-top",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height-bias-bottom",type:u.sizeMaybePercent,triggersBounds:l.any}],P=[{name:"line-style",type:u.lineStyle},{name:"line-color",type:u.color},{name:"line-fill",type:u.fill},{name:"line-cap",type:u.lineCap},{name:"line-opacity",type:u.zeroOneNumber},{name:"line-dash-pattern",type:u.numbers},{name:"line-dash-offset",type:u.number},{name:"line-outline-width",type:u.size},{name:"line-outline-color",type:u.color},{name:"line-gradient-stop-colors",type:u.colors},{name:"line-gradient-stop-positions",type:u.percentages},{name:"curve-style",type:u.curveStyle,triggersBounds:l.any,triggersBoundsOfParallelEdges:function(G,H){return G===H?!1:G==="bezier"||H==="bezier"}},{name:"haystack-radius",type:u.zeroOneNumber,triggersBounds:l.any},{name:"source-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"target-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"control-point-step-size",type:u.size,triggersBounds:l.any},{name:"control-point-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"control-point-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"segment-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-radii",type:u.numbers,triggersBounds:l.any},{name:"radius-type",type:u.radiusType,triggersBounds:l.any},{name:"taxi-turn",type:u.bidirectionalSizeMaybePercent,triggersBounds:l.any},{name:"taxi-turn-min-distance",type:u.size,triggersBounds:l.any},{name:"taxi-direction",type:u.axisDirection,triggersBounds:l.any},{name:"taxi-radius",type:u.number,triggersBounds:l.any},{name:"edge-distances",type:u.edgeDistances,triggersBounds:l.any},{name:"arrow-scale",type:u.positiveNumber,triggersBounds:l.any},{name:"loop-direction",type:u.angle,triggersBounds:l.any},{name:"loop-sweep",type:u.angle,triggersBounds:l.any},{name:"source-distance-from-node",type:u.size,triggersBounds:l.any},{name:"target-distance-from-node",type:u.size,triggersBounds:l.any}],D=[{name:"ghost",type:u.bool,triggersBounds:l.any},{name:"ghost-offset-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-offset-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-opacity",type:u.zeroOneNumber}],A=[{name:"selection-box-color",type:u.color},{name:"selection-box-opacity",type:u.zeroOneNumber},{name:"selection-box-border-color",type:u.color},{name:"selection-box-border-width",type:u.size},{name:"active-bg-color",type:u.color},{name:"active-bg-opacity",type:u.zeroOneNumber},{name:"active-bg-size",type:u.size},{name:"outside-texture-bg-color",type:u.color},{name:"outside-texture-bg-opacity",type:u.zeroOneNumber}],B=[];Qe.pieBackgroundN=16,B.push({name:"pie-size",type:u.sizeMaybePercent}),B.push({name:"pie-hole",type:u.sizeMaybePercent}),B.push({name:"pie-start-angle",type:u.angle});for(var R=1;R<=Qe.pieBackgroundN;R++)B.push({name:"pie-"+R+"-background-color",type:u.color}),B.push({name:"pie-"+R+"-background-size",type:u.percent}),B.push({name:"pie-"+R+"-background-opacity",type:u.zeroOneNumber});var M=[];Qe.stripeBackgroundN=16,M.push({name:"stripe-size",type:u.sizeMaybePercent}),M.push({name:"stripe-direction",type:u.axisDirectionPrimary});for(var L=1;L<=Qe.stripeBackgroundN;L++)M.push({name:"stripe-"+L+"-background-color",type:u.color}),M.push({name:"stripe-"+L+"-background-size",type:u.percent}),M.push({name:"stripe-"+L+"-background-opacity",type:u.zeroOneNumber});var I=[],O=Qe.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:u.arrowShape,triggersBounds:l.any},{name:"arrow-color",type:u.color},{name:"arrow-fill",type:u.arrowFill},{name:"arrow-width",type:u.arrowWidth}].forEach(function(z){O.forEach(function(G){var H=G+"-"+z.name,Q=z.type,ne=z.triggersBounds;I.push({name:H,type:Q,triggersBounds:ne})})},{});var F=Qe.properties=[].concat(m,b,g,p,y,D,d,h,v,f,c,E,C,x,T,B,M,S,P,I,A),_=Qe.propertyGroups={behavior:m,transition:b,visibility:g,overlay:p,underlay:y,ghost:D,commonLabel:d,labelDimensions:h,mainLabel:v,sourceLabel:f,targetLabel:c,nodeBody:E,nodeBorder:C,nodeOutline:x,backgroundImage:T,pie:B,stripe:M,compound:S,edgeLine:P,edgeArrow:I,core:A},N=Qe.propertyGroupNames={},q=Qe.propertyGroupKeys=Object.keys(_);q.forEach(function(z){N[z]=_[z].map(function(G){return G.name}),_[z].forEach(function(G){return G.groupKey=z})});var U=Qe.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];Qe.propertyNames=F.map(function(z){return z.name});for(var X=0;X<F.length;X++){var j=F[X];F[j.name]=j}for(var J=0;J<U.length;J++){var re=U[J],ae=F[re.pointsTo],Z={name:re.name,alias:!0,pointsTo:ae};F.push(Z),F[re.name]=Z}})();Qe.getDefaultProperty=function(r){return this.getDefaultProperties()[r]};Qe.getDefaultProperties=function(){var r=this._private;if(r.defaultProperties!=null)return r.defaultProperties;for(var e=pe({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","box-select-labels":"no","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%","pie-hole":0,"pie-start-angle":"0deg"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(l,u){for(var v=1;v<=Qe.pieBackgroundN;v++){var f=u.name.replace("{{i}}",v),c=u.value;l[f]=c}return l},{}),{"stripe-size":"100%","stripe-direction":"horizontal"},[{name:"stripe-{{i}}-background-color",value:"black"},{name:"stripe-{{i}}-background-size",value:"0%"},{name:"stripe-{{i}}-background-opacity",value:1}].reduce(function(l,u){for(var v=1;v<=Qe.stripeBackgroundN;v++){var f=u.name.replace("{{i}}",v),c=u.value;l[f]=c}return l},{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce(function(l,u){return Qe.arrowPrefixes.forEach(function(v){var f=v+"-"+u.name,c=u.value;l[f]=c}),l},{})),t={},a=0;a<this.properties.length;a++){var n=this.properties[a];if(!n.pointsTo){var i=n.name,s=e[i],o=this.parse(i,s);t[i]=o}}return r.defaultProperties=t,r.defaultProperties};Qe.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var Un={};Un.parse=function(r,e,t,a){var n=this;if($e(e))return n.parseImplWarn(r,e,t,a);var i=a==="mapping"||a===!0||a===!1||a==null?"dontcare":a,s=t?"t":"f",o=""+e,l=vv(r,o,s,i),u=n.propCache=n.propCache||[],v;return(v=u[l])||(v=u[l]=n.parseImplWarn(r,e,t,a)),(t||a==="mapping")&&(v=qr(v),v&&(v.value=qr(v.value))),v};Un.parseImplWarn=function(r,e,t,a){var n=this.parseImpl(r,e,t,a);return!n&&e!=null&&Ie("The style property `".concat(r,": ").concat(e,"` is invalid")),n&&(n.name==="width"||n.name==="height")&&e==="label"&&Ie("The style value of `label` is deprecated for `"+n.name+"`"),n};Un.parseImpl=function(r,e,t,a){var n=this;r=Zs(r);var i=n.properties[r],s=e,o=n.types;if(!i||e===void 0)return null;i.alias&&(i=i.pointsTo,r=i.name);var l=de(e);l&&(e=e.trim());var u=i.type;if(!u)return null;if(t&&(e===""||e===null))return{name:r,value:e,bypass:!0,deleteBypass:!0};if($e(e))return{name:r,value:e,strValue:"fn",mapped:o.fn,bypass:t};var v,f;if(!(!l||a||e.length<7||e[1]!=="a")){if(e.length>=7&&e[0]==="d"&&(v=new RegExp(o.data.regex).exec(e))){if(t)return!1;var c=o.data;return{name:r,value:v,strValue:""+e,mapped:c,field:v[1],bypass:t}}else if(e.length>=10&&e[0]==="m"&&(f=new RegExp(o.mapData.regex).exec(e))){if(t||u.multiple)return!1;var h=o.mapData;if(!(u.color||u.number))return!1;var d=this.parse(r,f[4]);if(!d||d.mapped)return!1;var m=this.parse(r,f[5]);if(!m||m.mapped)return!1;if(d.pfValue===m.pfValue||d.strValue===m.strValue)return Ie("`"+r+": "+e+"` is not a valid mapper because the output range is zero; converting to `"+r+": "+d.strValue+"`"),this.parse(r,d.strValue);if(u.color){var g=d.value,p=m.value,y=g[0]===p[0]&&g[1]===p[1]&&g[2]===p[2]&&(g[3]===p[3]||(g[3]==null||g[3]===1)&&(p[3]==null||p[3]===1));if(y)return!1}return{name:r,value:f,strValue:""+e,mapped:h,field:f[1],fieldMin:parseFloat(f[2]),fieldMax:parseFloat(f[3]),valueMin:d.value,valueMax:m.value,bypass:t}}}if(u.multiple&&a!=="multiple"){var b;if(l?b=e.split(/\s+/):ze(e)?b=e:b=[e],u.evenMultiple&&b.length%2!==0)return null;for(var w=[],E=[],C=[],x="",T=!1,S=0;S<b.length;S++){var P=n.parse(r,b[S],t,"multiple");T=T||de(P.value),w.push(P.value),C.push(P.pfValue!=null?P.pfValue:P.value),E.push(P.units),x+=(S>0?" ":"")+P.strValue}return u.validate&&!u.validate(w,E)?null:u.singleEnum&&T?w.length===1&&de(w[0])?{name:r,value:w[0],strValue:w[0],bypass:t}:null:{name:r,value:w,pfValue:C,strValue:x,bypass:t,units:E}}var D=function(){for(var Z=0;Z<u.enums.length;Z++){var z=u.enums[Z];if(z===e)return{name:r,value:e,strValue:""+e,bypass:t}}return null};if(u.number){var A,B="px";if(u.units&&(A=u.units),u.implicitUnits&&(B=u.implicitUnits),!u.unitless)if(l){var R="px|em"+(u.allowPercent?"|\\%":"");A&&(R=A);var M=e.match("^("+tr+")("+R+")?$");M&&(e=M[1],A=M[2]||B)}else(!A||u.implicitUnits)&&(A=B);if(e=parseFloat(e),isNaN(e)&&u.enums===void 0)return null;if(isNaN(e)&&u.enums!==void 0)return e=s,D();if(u.integer&&!lc(e)||u.min!==void 0&&(e<u.min||u.strictMin&&e===u.min)||u.max!==void 0&&(e>u.max||u.strictMax&&e===u.max))return null;var L={name:r,value:e,strValue:""+e+(A||""),units:A,bypass:t};return u.unitless||A!=="px"&&A!=="em"?L.pfValue=e:L.pfValue=A==="px"||!A?e:this.getEmSizeInPixels()*e,(A==="ms"||A==="s")&&(L.pfValue=A==="ms"?e:1e3*e),(A==="deg"||A==="rad")&&(L.pfValue=A==="rad"?e:pd(e)),A==="%"&&(L.pfValue=e/100),L}else if(u.propList){var I=[],O=""+e;if(O!=="none"){for(var F=O.split(/\s*,\s*|\s+/),_=0;_<F.length;_++){var N=F[_].trim();n.properties[N]?I.push(N):Ie("`"+N+"` is not a valid property name")}if(I.length===0)return null}return{name:r,value:I,strValue:I.length===0?"none":I.join(" "),bypass:t}}else if(u.color){var q=av(e);return q?{name:r,value:q,pfValue:q,strValue:"rgb("+q[0]+","+q[1]+","+q[2]+")",bypass:t}:null}else if(u.regex||u.regexes){if(u.enums){var U=D();if(U)return U}for(var X=u.regexes?u.regexes:[u.regex],j=0;j<X.length;j++){var J=new RegExp(X[j]),re=J.exec(e);if(re)return{name:r,value:u.singleRegexMatchValue?re[1]:re,strValue:""+e,bypass:t}}return null}else return u.string?{name:r,value:""+e,strValue:""+e,bypass:t}:u.enums?D():null};var or=function(e){if(!(this instanceof or))return new or(e);if(!Ys(e)){We("A style must have a core reference");return}this._private={cy:e,coreStyle:{}},this.length=0,this.resetToDefault()},pr=or.prototype;pr.instanceString=function(){return"style"};pr.clear=function(){for(var r=this._private,e=r.cy,t=e.elements(),a=0;a<this.length;a++)this[a]=void 0;return this.length=0,r.contextStyles={},r.propDiffs={},this.cleanElements(t,!0),t.forEach(function(n){var i=n[0]._private;i.styleDirty=!0,i.appliedInitStyle=!1}),this};pr.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this};pr.core=function(r){return this._private.coreStyle[r]||this.getDefaultProperty(r)};pr.selector=function(r){var e=r==="core"?null:new vt(r),t=this.length++;return this[t]={selector:e,properties:[],mappedProperties:[],index:t},this};pr.css=function(){var r=this,e=arguments;if(e.length===1)for(var t=e[0],a=0;a<r.properties.length;a++){var n=r.properties[a],i=t[n.name];i===void 0&&(i=t[Mn(n.name)]),i!==void 0&&this.cssRule(n.name,i)}else e.length===2&&this.cssRule(e[0],e[1]);return this};pr.style=pr.css;pr.cssRule=function(r,e){var t=this.parse(r,e);if(t){var a=this.length-1;this[a].properties.push(t),this[a].properties[t.name]=t,t.name.match(/pie-(\d+)-background-size/)&&t.value&&(this._private.hasPie=!0),t.name.match(/stripe-(\d+)-background-size/)&&t.value&&(this._private.hasStripe=!0),t.mapped&&this[a].mappedProperties.push(t);var n=!this[a].selector;n&&(this._private.coreStyle[t.name]=t)}return this};pr.append=function(r){return ev(r)?r.appendToStyle(this):ze(r)?this.appendFromJson(r):de(r)&&this.appendFromString(r),this};or.fromJson=function(r,e){var t=new or(r);return t.fromJson(e),t};or.fromString=function(r,e){return new or(r).fromString(e)};[ur,_a,fo,_r,$n,co,Qe,Un].forEach(function(r){pe(pr,r)});or.types=pr.types;or.properties=pr.properties;or.propertyGroups=pr.propertyGroups;or.propertyGroupNames=pr.propertyGroupNames;or.propertyGroupKeys=pr.propertyGroupKeys;var Cp={style:function(e){if(e){var t=this.setStyle(e);t.update()}return this._private.style},setStyle:function(e){var t=this._private;return ev(e)?t.style=e.generateStyle(this):ze(e)?t.style=or.fromJson(this,e):de(e)?t.style=or.fromString(this,e):t.style=or(this),t.style},updateStyle:function(){this.mutableElements().updateStyle()}},Tp="single",Pt={autolock:function(e){if(e!==void 0)this._private.autolock=!!e;else return this._private.autolock;return this},autoungrabify:function(e){if(e!==void 0)this._private.autoungrabify=!!e;else return this._private.autoungrabify;return this},autounselectify:function(e){if(e!==void 0)this._private.autounselectify=!!e;else return this._private.autounselectify;return this},selectionType:function(e){var t=this._private;if(t.selectionType==null&&(t.selectionType=Tp),e!==void 0)(e==="additive"||e==="single")&&(t.selectionType=e);else return t.selectionType;return this},panningEnabled:function(e){if(e!==void 0)this._private.panningEnabled=!!e;else return this._private.panningEnabled;return this},userPanningEnabled:function(e){if(e!==void 0)this._private.userPanningEnabled=!!e;else return this._private.userPanningEnabled;return this},zoomingEnabled:function(e){if(e!==void 0)this._private.zoomingEnabled=!!e;else return this._private.zoomingEnabled;return this},userZoomingEnabled:function(e){if(e!==void 0)this._private.userZoomingEnabled=!!e;else return this._private.userZoomingEnabled;return this},boxSelectionEnabled:function(e){if(e!==void 0)this._private.boxSelectionEnabled=!!e;else return this._private.boxSelectionEnabled;return this},pan:function(){var e=arguments,t=this._private.pan,a,n,i,s,o;switch(e.length){case 0:return t;case 1:if(de(e[0]))return a=e[0],t[a];if(Re(e[0])){if(!this._private.panningEnabled)return this;i=e[0],s=i.x,o=i.y,te(s)&&(t.x=s),te(o)&&(t.y=o),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;a=e[0],n=e[1],(a==="x"||a==="y")&&te(n)&&(t[a]=n),this.emit("pan viewport");break}return this.notify("viewport"),this},panBy:function(e,t){var a=arguments,n=this._private.pan,i,s,o,l,u;if(!this._private.panningEnabled)return this;switch(a.length){case 1:Re(e)&&(o=a[0],l=o.x,u=o.y,te(l)&&(n.x+=l),te(u)&&(n.y+=u),this.emit("pan viewport"));break;case 2:i=e,s=t,(i==="x"||i==="y")&&te(s)&&(n[i]+=s),this.emit("pan viewport");break}return this.notify("viewport"),this},gc:function(){this.notify("gc")},fit:function(e,t){var a=this.getFitViewport(e,t);if(a){var n=this._private;n.zoom=a.zoom,n.pan=a.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,t){if(te(e)&&t===void 0&&(t=e,e=void 0),!(!this._private.panningEnabled||!this._private.zoomingEnabled)){var a;if(de(e)){var n=e;e=this.$(n)}else if(cc(e)){var i=e;a={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2},a.w=a.x2-a.x1,a.h=a.y2-a.y1}else Dr(e)||(e=this.mutableElements());if(!(Dr(e)&&e.empty())){a=a||e.boundingBox();var s=this.width(),o=this.height(),l;if(t=te(t)?t:0,!isNaN(s)&&!isNaN(o)&&s>0&&o>0&&!isNaN(a.w)&&!isNaN(a.h)&&a.w>0&&a.h>0){l=Math.min((s-2*t)/a.w,(o-2*t)/a.h),l=l>this._private.maxZoom?this._private.maxZoom:l,l=l<this._private.minZoom?this._private.minZoom:l;var u={x:(s-l*(a.x1+a.x2))/2,y:(o-l*(a.y1+a.y2))/2};return{zoom:l,pan:u}}}}},zoomRange:function(e,t){var a=this._private;if(t==null){var n=e;e=n.min,t=n.max}return te(e)&&te(t)&&e<=t?(a.minZoom=e,a.maxZoom=t):te(e)&&t===void 0&&e<=a.maxZoom?a.minZoom=e:te(t)&&e===void 0&&t>=a.minZoom&&(a.maxZoom=t),this},minZoom:function(e){return e===void 0?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return e===void 0?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var t=this._private,a=t.pan,n=t.zoom,i,s,o=!1;if(t.zoomingEnabled||(o=!0),te(e)?s=e:Re(e)&&(s=e.level,e.position!=null?i=On(e.position,n,a):e.renderedPosition!=null&&(i=e.renderedPosition),i!=null&&!t.panningEnabled&&(o=!0)),s=s>t.maxZoom?t.maxZoom:s,s=s<t.minZoom?t.minZoom:s,o||!te(s)||s===n||i!=null&&(!te(i.x)||!te(i.y)))return null;if(i!=null){var l=a,u=n,v=s,f={x:-v/u*(i.x-l.x)+i.x,y:-v/u*(i.y-l.y)+i.y};return{zoomed:!0,panned:!0,zoom:v,pan:f}}else return{zoomed:!0,panned:!1,zoom:s,pan:a}},zoom:function(e){if(e===void 0)return this._private.zoom;var t=this.getZoomedViewport(e),a=this._private;return t==null||!t.zoomed?this:(a.zoom=t.zoom,t.panned&&(a.pan.x=t.pan.x,a.pan.y=t.pan.y),this.emit("zoom"+(t.panned?" pan":"")+" viewport"),this.notify("viewport"),this)},viewport:function(e){var t=this._private,a=!0,n=!0,i=[],s=!1,o=!1;if(!e)return this;if(te(e.zoom)||(a=!1),Re(e.pan)||(n=!1),!a&&!n)return this;if(a){var l=e.zoom;l<t.minZoom||l>t.maxZoom||!t.zoomingEnabled?s=!0:(t.zoom=l,i.push("zoom"))}if(n&&(!s||!e.cancelOnFailedZoom)&&t.panningEnabled){var u=e.pan;te(u.x)&&(t.pan.x=u.x,o=!1),te(u.y)&&(t.pan.y=u.y,o=!1),o||i.push("pan")}return i.length>0&&(i.push("viewport"),this.emit(i.join(" ")),this.notify("viewport")),this},center:function(e){var t=this.getCenterPan(e);return t&&(this._private.pan=t,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,t){if(this._private.panningEnabled){if(de(e)){var a=e;e=this.mutableElements().filter(a)}else Dr(e)||(e=this.mutableElements());if(e.length!==0){var n=e.boundingBox(),i=this.width(),s=this.height();t=t===void 0?this._private.zoom:t;var o={x:(i-t*(n.x1+n.x2))/2,y:(s-t*(n.y1+n.y2))/2};return o}}},reset:function(){return!this._private.panningEnabled||!this._private.zoomingEnabled?this:(this.viewport({pan:{x:0,y:0},zoom:1}),this)},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e=this._private,t=e.container,a=this;return e.sizeCache=e.sizeCache||(t?function(){var n=a.window().getComputedStyle(t),i=function(o){return parseFloat(n.getPropertyValue(o))};return{width:t.clientWidth-i("padding-left")-i("padding-right"),height:t.clientHeight-i("padding-top")-i("padding-bottom")}}():{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,t=this._private.zoom,a=this.renderedExtent(),n={x1:(a.x1-e.x)/t,x2:(a.x2-e.x)/t,y1:(a.y1-e.y)/t,y2:(a.y2-e.y)/t};return n.w=n.x2-n.x1,n.h=n.y2-n.y1,n},renderedExtent:function(){var e=this.width(),t=this.height();return{x1:0,y1:0,x2:e,y2:t,w:e,h:t}},multiClickDebounceTime:function(e){if(e)this._private.multiClickDebounceTime=e;else return this._private.multiClickDebounceTime;return this}};Pt.centre=Pt.center;Pt.autolockNodes=Pt.autolock;Pt.autoungrabifyNodes=Pt.autoungrabify;var Aa={data:Le.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:Le.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:Le.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Le.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};Aa.attr=Aa.data;Aa.removeAttr=Aa.removeData;var Ra=function(e){var t=this;e=pe({},e);var a=e.container;a&&!bn(a)&&bn(a[0])&&(a=a[0]);var n=a?a._cyreg:null;n=n||{},n&&n.cy&&(n.cy.destroy(),n={});var i=n.readies=n.readies||[];a&&(a._cyreg=n),n.cy=t;var s=er!==void 0&&a!==void 0&&!e.headless,o=e;o.layout=pe({name:s?"grid":"null"},o.layout),o.renderer=pe({name:s?"canvas":"null"},o.renderer);var l=function(d,m,g){return m!==void 0?m:g!==void 0?g:d},u=this._private={container:a,ready:!1,options:o,elements:new fr(this),listeners:[],aniEles:new fr(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:l(!0,o.zoomingEnabled),userZoomingEnabled:l(!0,o.userZoomingEnabled),panningEnabled:l(!0,o.panningEnabled),userPanningEnabled:l(!0,o.userPanningEnabled),boxSelectionEnabled:l(!0,o.boxSelectionEnabled),autolock:l(!1,o.autolock,o.autolockNodes),autoungrabify:l(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:l(!1,o.autounselectify),styleEnabled:o.styleEnabled===void 0?s:o.styleEnabled,zoom:te(o.zoom)?o.zoom:1,pan:{x:Re(o.pan)&&te(o.pan.x)?o.pan.x:0,y:Re(o.pan)&&te(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:l(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom});var v=function(d,m){var g=d.some(dc);if(g)return ta.all(d).then(m);m(d)};u.styleEnabled&&t.setStyle([]);var f=pe({},o,o.renderer);t.initRenderer(f);var c=function(d,m,g){t.notifications(!1);var p=t.mutableElements();p.length>0&&p.remove(),d!=null&&(Re(d)||ze(d))&&t.add(d),t.one("layoutready",function(b){t.notifications(!0),t.emit(b),t.one("load",m),t.emitAndNotify("load")}).one("layoutstop",function(){t.one("done",g),t.emit("done")});var y=pe({},t._private.options.layout);y.eles=t.elements(),t.layout(y).run()};v([o.style,o.elements],function(h){var d=h[0],m=h[1];u.styleEnabled&&t.style().append(d),c(m,function(){t.startAnimationLoop(),u.ready=!0,$e(o.ready)&&t.on("ready",o.ready);for(var g=0;g<i.length;g++){var p=i[g];t.on("ready",p)}n&&(n.readies=[]),t.emit("ready")},o.done)})},kn=Ra.prototype;pe(kn,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},window:function(){var e=this._private.container;if(e==null)return er;var t=this._private.container.ownerDocument;return t===void 0||t==null?er:t.defaultView||er},mount:function(e){if(e!=null){var t=this,a=t._private,n=a.options;return!bn(e)&&bn(e[0])&&(e=e[0]),t.stopAnimationLoop(),t.destroyRenderer(),a.container=e,a.styleEnabled=!0,t.invalidateSize(),t.initRenderer(pe({},n,n.renderer,{name:n.renderer.name==="null"?"canvas":n.renderer.name})),t.startAnimationLoop(),t.style(n.style),t.emit("mount"),t}},unmount:function(){var e=this;return e.stopAnimationLoop(),e.destroyRenderer(),e.initRenderer({name:"null"}),e.emit("unmount"),e},options:function(){return qr(this._private.options)},json:function(e){var t=this,a=t._private,n=t.mutableElements(),i=function(w){return t.getElementById(w.id())};if(Re(e)){if(t.startBatch(),e.elements){var s={},o=function(w,E){for(var C=[],x=[],T=0;T<w.length;T++){var S=w[T];if(!S.data.id){Ie("cy.json() cannot handle elements without an ID attribute");continue}var P=""+S.data.id,D=t.getElementById(P);s[P]=!0,D.length!==0?x.push({ele:D,json:S}):(E&&(S.group=E),C.push(S))}t.add(C);for(var A=0;A<x.length;A++){var B=x[A],R=B.ele,M=B.json;R.json(M)}};if(ze(e.elements))o(e.elements);else for(var l=["nodes","edges"],u=0;u<l.length;u++){var v=l[u],f=e.elements[v];ze(f)&&o(f,v)}var c=t.collection();n.filter(function(b){return!s[b.id()]}).forEach(function(b){b.isParent()?c.merge(b):b.remove()}),c.forEach(function(b){return b.children().move({parent:null})}),c.forEach(function(b){return i(b).remove()})}e.style&&t.style(e.style),e.zoom!=null&&e.zoom!==a.zoom&&t.zoom(e.zoom),e.pan&&(e.pan.x!==a.pan.x||e.pan.y!==a.pan.y)&&t.pan(e.pan),e.data&&t.data(e.data);for(var h=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],d=0;d<h.length;d++){var m=h[d];e[m]!=null&&t[m](e[m])}return t.endBatch(),this}else{var g=!!e,p={};g?p.elements=this.elements().map(function(b){return b.json()}):(p.elements={},n.forEach(function(b){var w=b.group();p.elements[w]||(p.elements[w]=[]),p.elements[w].push(b.json())})),this._private.styleEnabled&&(p.style=t.style().json()),p.data=qr(t.data());var y=a.options;return p.zoomingEnabled=a.zoomingEnabled,p.userZoomingEnabled=a.userZoomingEnabled,p.zoom=a.zoom,p.minZoom=a.minZoom,p.maxZoom=a.maxZoom,p.panningEnabled=a.panningEnabled,p.userPanningEnabled=a.userPanningEnabled,p.pan=qr(a.pan),p.boxSelectionEnabled=a.boxSelectionEnabled,p.renderer=qr(y.renderer),p.hideEdgesOnViewport=y.hideEdgesOnViewport,p.textureOnViewport=y.textureOnViewport,p.wheelSensitivity=y.wheelSensitivity,p.motionBlur=y.motionBlur,p.multiClickDebounceTime=y.multiClickDebounceTime,p}}});kn.$id=kn.getElementById;[dp,mp,jv,zs,dn,wp,Fs,hn,Cp,Pt,Aa].forEach(function(r){pe(kn,r)});var Sp={fit:!0,directed:!1,padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}},kp={maximal:!1,acyclic:!1},zt=function(e){return e.scratch("breadthfirst")},Cl=function(e,t){return e.scratch("breadthfirst",t)};function ef(r){this.options=pe({},Sp,kp,r)}ef.prototype.run=function(){var r=this.options,e=r.cy,t=r.eles,a=t.nodes().filter(function(oe){return oe.isChildless()}),n=t,i=r.directed,s=r.acyclic||r.maximal||r.maximalAdjustments>0,o=!!r.boundingBox,l=e.extent(),u=wr(o?r.boundingBox:{x1:l.x1,y1:l.y1,w:l.w,h:l.h}),v;if(Dr(r.roots))v=r.roots;else if(ze(r.roots)){for(var f=[],c=0;c<r.roots.length;c++){var h=r.roots[c],d=e.getElementById(h);f.push(d)}v=e.collection(f)}else if(de(r.roots))v=e.$(r.roots);else if(i)v=a.roots();else{var m=t.components();v=e.collection();for(var g=function(){var ve=m[p],he=ve.maxDegree(!1),ye=ve.filter(function(me){return me.degree(!1)===he});v=v.add(ye)},p=0;p<m.length;p++)g()}var y=[],b={},w=function(ve,he){y[he]==null&&(y[he]=[]);var ye=y[he].length;y[he].push(ve),Cl(ve,{index:ye,depth:he})},E=function(ve,he){var ye=zt(ve),me=ye.depth,we=ye.index;y[me][we]=null,ve.isChildless()&&w(ve,he)};n.bfs({roots:v,directed:r.directed,visit:function(ve,he,ye,me,we){var xe=ve[0],Pe=xe.id();xe.isChildless()&&w(xe,we),b[Pe]=!0}});for(var C=[],x=0;x<a.length;x++){var T=a[x];b[T.id()]||C.push(T)}var S=function(ve){for(var he=y[ve],ye=0;ye<he.length;ye++){var me=he[ye];if(me==null){he.splice(ye,1),ye--;continue}Cl(me,{depth:ve,index:ye})}},P=function(ve,he){for(var ye=zt(ve),me=ve.incomers().filter(function(He){return He.isNode()&&t.has(He)}),we=-1,xe=ve.id(),Pe=0;Pe<me.length;Pe++){var Ve=me[Pe],Xe=zt(Ve);we=Math.max(we,Xe.depth)}if(ye.depth<=we){if(!r.acyclic&&he[xe])return null;var Oe=we+1;return E(ve,Oe),he[xe]=Oe,!0}return!1};if(i&&s){var D=[],A={},B=function(ve){return D.push(ve)},R=function(){return D.shift()};for(a.forEach(function(oe){return D.push(oe)});D.length>0;){var M=R(),L=P(M,A);if(L)M.outgoers().filter(function(oe){return oe.isNode()&&t.has(oe)}).forEach(B);else if(L===null){Ie("Detected double maximal shift for node `"+M.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}var I=0;if(r.avoidOverlap)for(var O=0;O<a.length;O++){var F=a[O],_=F.layoutDimensions(r),N=_.w,q=_.h;I=Math.max(I,N,q)}var U={},X=function(ve){if(U[ve.id()])return U[ve.id()];for(var he=zt(ve).depth,ye=ve.neighborhood(),me=0,we=0,xe=0;xe<ye.length;xe++){var Pe=ye[xe];if(!(Pe.isEdge()||Pe.isParent()||!a.has(Pe))){var Ve=zt(Pe);if(Ve!=null){var Xe=Ve.index,Oe=Ve.depth;if(!(Xe==null||Oe==null)){var He=y[Oe].length;Oe<he&&(me+=Xe/He,we++)}}}}return we=Math.max(1,we),me=me/we,we===0&&(me=0),U[ve.id()]=me,me},j=function(ve,he){var ye=X(ve),me=X(he),we=ye-me;return we===0?tv(ve.id(),he.id()):we};r.depthSort!==void 0&&(j=r.depthSort);for(var J=y.length,re=0;re<J;re++)y[re].sort(j),S(re);for(var ae=[],Z=0;Z<C.length;Z++)ae.push(C[Z]);var z=function(){for(var ve=0;ve<J;ve++)S(ve)};ae.length&&(y.unshift(ae),J=y.length,z());for(var G=0,H=0;H<J;H++)G=Math.max(y[H].length,G);var Q={x:u.x1+u.w/2,y:u.y1+u.h/2},ne=a.reduce(function(oe,ve){return function(he){return{w:oe.w===-1?he.w:(oe.w+he.w)/2,h:oe.h===-1?he.h:(oe.h+he.h)/2}}(ve.boundingBox({includeLabels:r.nodeDimensionsIncludeLabels}))},{w:-1,h:-1}),be=Math.max(J===1?0:o?(u.h-r.padding*2-ne.h)/(J-1):(u.h-r.padding*2-ne.h)/(J+1),I),Fe=y.reduce(function(oe,ve){return Math.max(oe,ve.length)},0),Be=function(ve){var he=zt(ve),ye=he.depth,me=he.index;if(r.circle){var we=Math.min(u.w/2/J,u.h/2/J);we=Math.max(we,I);var xe=we*ye+we-(J>0&&y[0].length<=3?we/2:0),Pe=2*Math.PI/y[ye].length*me;return ye===0&&y[0].length===1&&(xe=1),{x:Q.x+xe*Math.cos(Pe),y:Q.y+xe*Math.sin(Pe)}}else{var Ve=y[ye].length,Xe=Math.max(Ve===1?0:o?(u.w-r.padding*2-ne.w)/((r.grid?Fe:Ve)-1):(u.w-r.padding*2-ne.w)/((r.grid?Fe:Ve)+1),I),Oe={x:Q.x+(me+1-(Ve+1)/2)*Xe,y:Q.y+(ye+1-(J+1)/2)*be};return Oe}};return t.nodes().layoutPositions(this,r,Be),this};var Dp={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function rf(r){this.options=pe({},Dp,r)}rf.prototype.run=function(){var r=this.options,e=r,t=r.cy,a=e.eles,n=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,i=a.nodes().not(":parent");e.sort&&(i=i.sort(e.sort));for(var s=wr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=e.sweep===void 0?2*Math.PI-2*Math.PI/i.length:e.sweep,u=l/Math.max(1,i.length-1),v,f=0,c=0;c<i.length;c++){var h=i[c],d=h.layoutDimensions(e),m=d.w,g=d.h;f=Math.max(f,m,g)}if(te(e.radius)?v=e.radius:i.length<=1?v=0:v=Math.min(s.h,s.w)/2-f,i.length>1&&e.avoidOverlap){f*=1.75;var p=Math.cos(u)-Math.cos(0),y=Math.sin(u)-Math.sin(0),b=Math.sqrt(f*f/(p*p+y*y));v=Math.max(b,v)}var w=function(C,x){var T=e.startAngle+x*u*(n?1:-1),S=v*Math.cos(T),P=v*Math.sin(T),D={x:o.x+S,y:o.y+P};return D};return a.nodes().layoutPositions(this,e,w),this};var Bp={fit:!0,padding:30,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function tf(r){this.options=pe({},Bp,r)}tf.prototype.run=function(){for(var r=this.options,e=r,t=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,a=r.cy,n=e.eles,i=n.nodes().not(":parent"),s=wr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:a.width(),h:a.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=[],u=0,v=0;v<i.length;v++){var f=i[v],c=void 0;c=e.concentric(f),l.push({value:c,node:f}),f._private.scratch.concentric=c}i.updateStyle();for(var h=0;h<i.length;h++){var d=i[h],m=d.layoutDimensions(e);u=Math.max(u,m.w,m.h)}l.sort(function(ne,be){return be.value-ne.value});for(var g=e.levelWidth(i),p=[[]],y=p[0],b=0;b<l.length;b++){var w=l[b];if(y.length>0){var E=Math.abs(y[0].value-w.value);E>=g&&(y=[],p.push(y))}y.push(w)}var C=u+e.minNodeSpacing;if(!e.avoidOverlap){var x=p.length>0&&p[0].length>1,T=Math.min(s.w,s.h)/2-C,S=T/(p.length+x?1:0);C=Math.min(C,S)}for(var P=0,D=0;D<p.length;D++){var A=p[D],B=e.sweep===void 0?2*Math.PI-2*Math.PI/A.length:e.sweep,R=A.dTheta=B/Math.max(1,A.length-1);if(A.length>1&&e.avoidOverlap){var M=Math.cos(R)-Math.cos(0),L=Math.sin(R)-Math.sin(0),I=Math.sqrt(C*C/(M*M+L*L));P=Math.max(I,P)}A.r=P,P+=C}if(e.equidistant){for(var O=0,F=0,_=0;_<p.length;_++){var N=p[_],q=N.r-F;O=Math.max(O,q)}F=0;for(var U=0;U<p.length;U++){var X=p[U];U===0&&(F=X.r),X.r=F,F+=O}}for(var j={},J=0;J<p.length;J++)for(var re=p[J],ae=re.dTheta,Z=re.r,z=0;z<re.length;z++){var G=re[z],H=e.startAngle+(t?1:-1)*ae*z,Q={x:o.x+Z*Math.cos(H),y:o.y+Z*Math.sin(H)};j[G.node.id()]=Q}return n.nodes().layoutPositions(this,e,function(ne){var be=ne.id();return j[be]}),this};var ms,Pp={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,t){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function Kn(r){this.options=pe({},Pp,r),this.options.layout=this;var e=this.options.eles.nodes(),t=this.options.eles.edges(),a=t.filter(function(n){var i=n.source().data("id"),s=n.target().data("id"),o=e.some(function(u){return u.data("id")===i}),l=e.some(function(u){return u.data("id")===s});return!o||!l});this.options.eles=this.options.eles.not(a)}Kn.prototype.run=function(){var r=this.options,e=r.cy,t=this;t.stopped=!1,(r.animate===!0||r.animate===!1)&&t.emit({type:"layoutstart",layout:t}),r.debug===!0?ms=!0:ms=!1;var a=Ap(e,t,r);ms&&Mp(a),r.randomize&&Lp(a);var n=Yr(),i=function(){Ip(a,e,r),r.fit===!0&&e.fit(r.padding)},s=function(c){return!(t.stopped||c>=r.numIter||(Op(a,r),a.temperature=a.temperature*r.coolingFactor,a.temperature<r.minTemp))},o=function(){if(r.animate===!0||r.animate===!1)i(),t.one("layoutstop",r.stop),t.emit({type:"layoutstop",layout:t});else{var c=r.eles.nodes(),h=nf(a,r,c);c.layoutPositions(t,r,h)}},l=0,u=!0;if(r.animate===!0){var v=function(){for(var c=0;u&&c<r.refresh;)u=s(l),l++,c++;if(!u)Sl(a,r),o();else{var h=Yr();h-n>=r.animationThreshold&&i(),wn(v)}};v()}else{for(;u;)u=s(l),l++;Sl(a,r),o()}return this};Kn.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this};Kn.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var Ap=function(e,t,a){for(var n=a.eles.edges(),i=a.eles.nodes(),s=wr(a.boundingBox?a.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:n.size(),temperature:a.initialTemp,clientWidth:s.w,clientHeight:s.h,boundingBox:s},l=a.eles.components(),u={},v=0;v<l.length;v++)for(var f=l[v],c=0;c<f.length;c++){var h=f[c];u[h.id()]=v}for(var v=0;v<o.nodeSize;v++){var d=i[v],m=d.layoutDimensions(a),g={};g.isLocked=d.locked(),g.id=d.data("id"),g.parentId=d.data("parent"),g.cmptId=u[d.id()],g.children=[],g.positionX=d.position("x"),g.positionY=d.position("y"),g.offsetX=0,g.offsetY=0,g.height=m.w,g.width=m.h,g.maxX=g.positionX+g.width/2,g.minX=g.positionX-g.width/2,g.maxY=g.positionY+g.height/2,g.minY=g.positionY-g.height/2,g.padLeft=parseFloat(d.style("padding")),g.padRight=parseFloat(d.style("padding")),g.padTop=parseFloat(d.style("padding")),g.padBottom=parseFloat(d.style("padding")),g.nodeRepulsion=$e(a.nodeRepulsion)?a.nodeRepulsion(d):a.nodeRepulsion,o.layoutNodes.push(g),o.idToIndex[g.id]=v}for(var p=[],y=0,b=-1,w=[],v=0;v<o.nodeSize;v++){var d=o.layoutNodes[v],E=d.parentId;E!=null?o.layoutNodes[o.idToIndex[E]].children.push(d.id):(p[++b]=d.id,w.push(d.id))}for(o.graphSet.push(w);y<=b;){var C=p[y++],x=o.idToIndex[C],h=o.layoutNodes[x],T=h.children;if(T.length>0){o.graphSet.push(T);for(var v=0;v<T.length;v++)p[++b]=T[v]}}for(var v=0;v<o.graphSet.length;v++)for(var S=o.graphSet[v],c=0;c<S.length;c++){var P=o.idToIndex[S[c]];o.indexToGraph[P]=v}for(var v=0;v<o.edgeSize;v++){var D=n[v],A={};A.id=D.data("id"),A.sourceId=D.data("source"),A.targetId=D.data("target");var B=$e(a.idealEdgeLength)?a.idealEdgeLength(D):a.idealEdgeLength,R=$e(a.edgeElasticity)?a.edgeElasticity(D):a.edgeElasticity,M=o.idToIndex[A.sourceId],L=o.idToIndex[A.targetId],I=o.indexToGraph[M],O=o.indexToGraph[L];if(I!=O){for(var F=Rp(A.sourceId,A.targetId,o),_=o.graphSet[F],N=0,g=o.layoutNodes[M];_.indexOf(g.id)===-1;)g=o.layoutNodes[o.idToIndex[g.parentId]],N++;for(g=o.layoutNodes[L];_.indexOf(g.id)===-1;)g=o.layoutNodes[o.idToIndex[g.parentId]],N++;B*=N*a.nestingFactor}A.idealLength=B,A.elasticity=R,o.layoutEdges.push(A)}return o},Rp=function(e,t,a){var n=af(e,t,0,a);return 2>n.count?0:n.graph},af=function(e,t,a,n){var i=n.graphSet[a];if(-1<i.indexOf(e)&&-1<i.indexOf(t))return{count:2,graph:a};for(var s=0,o=0;o<i.length;o++){var l=i[o],u=n.idToIndex[l],v=n.layoutNodes[u].children;if(v.length!==0){var f=n.indexToGraph[n.idToIndex[v[0]]],c=af(e,t,f,n);if(c.count!==0)if(c.count===1){if(s++,s===2)break}else return c}}return{count:s,graph:a}},Mp,Lp=function(e,t){for(var a=e.clientWidth,n=e.clientHeight,i=0;i<e.nodeSize;i++){var s=e.layoutNodes[i];s.children.length===0&&!s.isLocked&&(s.positionX=Math.random()*a,s.positionY=Math.random()*n)}},nf=function(e,t,a){var n=e.boundingBox,i={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return t.boundingBox&&(a.forEach(function(s){var o=e.layoutNodes[e.idToIndex[s.data("id")]];i.x1=Math.min(i.x1,o.positionX),i.x2=Math.max(i.x2,o.positionX),i.y1=Math.min(i.y1,o.positionY),i.y2=Math.max(i.y2,o.positionY)}),i.w=i.x2-i.x1,i.h=i.y2-i.y1),function(s,o){var l=e.layoutNodes[e.idToIndex[s.data("id")]];if(t.boundingBox){var u=(l.positionX-i.x1)/i.w,v=(l.positionY-i.y1)/i.h;return{x:n.x1+u*n.w,y:n.y1+v*n.h}}else return{x:l.positionX,y:l.positionY}}},Ip=function(e,t,a){var n=a.layout,i=a.eles.nodes(),s=nf(e,a,i);i.positions(s),e.ready!==!0&&(e.ready=!0,n.one("layoutready",a.ready),n.emit({type:"layoutready",layout:this}))},Op=function(e,t,a){Np(e,t),Vp(e),qp(e,t),_p(e),Gp(e)},Np=function(e,t){for(var a=0;a<e.graphSet.length;a++)for(var n=e.graphSet[a],i=n.length,s=0;s<i;s++)for(var o=e.layoutNodes[e.idToIndex[n[s]]],l=s+1;l<i;l++){var u=e.layoutNodes[e.idToIndex[n[l]]];zp(o,u,e,t)}},Tl=function(e){return-1+2*e*Math.random()},zp=function(e,t,a,n){var i=e.cmptId,s=t.cmptId;if(!(i!==s&&!a.isCompound)){var o=t.positionX-e.positionX,l=t.positionY-e.positionY,u=1;o===0&&l===0&&(o=Tl(u),l=Tl(u));var v=Fp(e,t,o,l);if(v>0)var f=n.nodeOverlap*v,c=Math.sqrt(o*o+l*l),h=f*o/c,d=f*l/c;else var m=Dn(e,o,l),g=Dn(t,-1*o,-1*l),p=g.x-m.x,y=g.y-m.y,b=p*p+y*y,c=Math.sqrt(b),f=(e.nodeRepulsion+t.nodeRepulsion)/b,h=f*p/c,d=f*y/c;e.isLocked||(e.offsetX-=h,e.offsetY-=d),t.isLocked||(t.offsetX+=h,t.offsetY+=d)}},Fp=function(e,t,a,n){if(a>0)var i=e.maxX-t.minX;else var i=t.maxX-e.minX;if(n>0)var s=e.maxY-t.minY;else var s=t.maxY-e.minY;return i>=0&&s>=0?Math.sqrt(i*i+s*s):0},Dn=function(e,t,a){var n=e.positionX,i=e.positionY,s=e.height||1,o=e.width||1,l=a/t,u=s/o,v={};return t===0&&0<a||t===0&&0>a?(v.x=n,v.y=i+s/2,v):0<t&&-1*u<=l&&l<=u?(v.x=n+o/2,v.y=i+o*a/2/t,v):0>t&&-1*u<=l&&l<=u?(v.x=n-o/2,v.y=i-o*a/2/t,v):0<a&&(l<=-1*u||l>=u)?(v.x=n+s*t/2/a,v.y=i+s/2,v):(0>a&&(l<=-1*u||l>=u)&&(v.x=n-s*t/2/a,v.y=i-s/2),v)},Vp=function(e,t){for(var a=0;a<e.edgeSize;a++){var n=e.layoutEdges[a],i=e.idToIndex[n.sourceId],s=e.layoutNodes[i],o=e.idToIndex[n.targetId],l=e.layoutNodes[o],u=l.positionX-s.positionX,v=l.positionY-s.positionY;if(!(u===0&&v===0)){var f=Dn(s,u,v),c=Dn(l,-1*u,-1*v),h=c.x-f.x,d=c.y-f.y,m=Math.sqrt(h*h+d*d),g=Math.pow(n.idealLength-m,2)/n.elasticity;if(m!==0)var p=g*h/m,y=g*d/m;else var p=0,y=0;s.isLocked||(s.offsetX+=p,s.offsetY+=y),l.isLocked||(l.offsetX-=p,l.offsetY-=y)}}},qp=function(e,t){if(t.gravity!==0)for(var a=1,n=0;n<e.graphSet.length;n++){var i=e.graphSet[n],s=i.length;if(n===0)var o=e.clientHeight/2,l=e.clientWidth/2;else var u=e.layoutNodes[e.idToIndex[i[0]]],v=e.layoutNodes[e.idToIndex[u.parentId]],o=v.positionX,l=v.positionY;for(var f=0;f<s;f++){var c=e.layoutNodes[e.idToIndex[i[f]]];if(!c.isLocked){var h=o-c.positionX,d=l-c.positionY,m=Math.sqrt(h*h+d*d);if(m>a){var g=t.gravity*h/m,p=t.gravity*d/m;c.offsetX+=g,c.offsetY+=p}}}}},_p=function(e,t){var a=[],n=0,i=-1;for(a.push.apply(a,e.graphSet[0]),i+=e.graphSet[0].length;n<=i;){var s=a[n++],o=e.idToIndex[s],l=e.layoutNodes[o],u=l.children;if(0<u.length&&!l.isLocked){for(var v=l.offsetX,f=l.offsetY,c=0;c<u.length;c++){var h=e.layoutNodes[e.idToIndex[u[c]]];h.offsetX+=v,h.offsetY+=f,a[++i]=u[c]}l.offsetX=0,l.offsetY=0}}},Gp=function(e,t){for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&(n.maxX=void 0,n.minX=void 0,n.maxY=void 0,n.minY=void 0)}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];if(!(0<n.children.length||n.isLocked)){var i=Hp(n.offsetX,n.offsetY,e.temperature);n.positionX+=i.x,n.positionY+=i.y,n.offsetX=0,n.offsetY=0,n.minX=n.positionX-n.width,n.maxX=n.positionX+n.width,n.minY=n.positionY-n.height,n.maxY=n.positionY+n.height,sf(n,e)}}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&!n.isLocked&&(n.positionX=(n.maxX+n.minX)/2,n.positionY=(n.maxY+n.minY)/2,n.width=n.maxX-n.minX,n.height=n.maxY-n.minY)}},Hp=function(e,t,a){var n=Math.sqrt(e*e+t*t);if(n>a)var i={x:a*e/n,y:a*t/n};else var i={x:e,y:t};return i},sf=function(e,t){var a=e.parentId;if(a!=null){var n=t.layoutNodes[t.idToIndex[a]],i=!1;if((n.maxX==null||e.maxX+n.padRight>n.maxX)&&(n.maxX=e.maxX+n.padRight,i=!0),(n.minX==null||e.minX-n.padLeft<n.minX)&&(n.minX=e.minX-n.padLeft,i=!0),(n.maxY==null||e.maxY+n.padBottom>n.maxY)&&(n.maxY=e.maxY+n.padBottom,i=!0),(n.minY==null||e.minY-n.padTop<n.minY)&&(n.minY=e.minY-n.padTop,i=!0),i)return sf(n,t)}},Sl=function(e,t){for(var a=e.layoutNodes,n=[],i=0;i<a.length;i++){var s=a[i],o=s.cmptId,l=n[o]=n[o]||[];l.push(s)}for(var u=0,i=0;i<n.length;i++){var v=n[i];if(v){v.x1=1/0,v.x2=-1/0,v.y1=1/0,v.y2=-1/0;for(var f=0;f<v.length;f++){var c=v[f];v.x1=Math.min(v.x1,c.positionX-c.width/2),v.x2=Math.max(v.x2,c.positionX+c.width/2),v.y1=Math.min(v.y1,c.positionY-c.height/2),v.y2=Math.max(v.y2,c.positionY+c.height/2)}v.w=v.x2-v.x1,v.h=v.y2-v.y1,u+=v.w*v.h}}n.sort(function(y,b){return b.w*b.h-y.w*y.h});for(var h=0,d=0,m=0,g=0,p=Math.sqrt(u)*e.clientWidth/e.clientHeight,i=0;i<n.length;i++){var v=n[i];if(v){for(var f=0;f<v.length;f++){var c=v[f];c.isLocked||(c.positionX+=h-v.x1,c.positionY+=d-v.y1)}h+=v.w+t.componentSpacing,m+=v.w+t.componentSpacing,g=Math.max(g,v.h),m>p&&(d+=g+t.componentSpacing,h=0,m=0,g=0)}}},Wp={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function of(r){this.options=pe({},Wp,r)}of.prototype.run=function(){var r=this.options,e=r,t=r.cy,a=e.eles,n=a.nodes().not(":parent");e.sort&&(n=n.sort(e.sort));var i=wr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});if(i.h===0||i.w===0)a.nodes().layoutPositions(this,e,function(U){return{x:i.x1,y:i.y1}});else{var s=n.size(),o=Math.sqrt(s*i.h/i.w),l=Math.round(o),u=Math.round(i.w/i.h*o),v=function(X){if(X==null)return Math.min(l,u);var j=Math.min(l,u);j==l?l=X:u=X},f=function(X){if(X==null)return Math.max(l,u);var j=Math.max(l,u);j==l?l=X:u=X},c=e.rows,h=e.cols!=null?e.cols:e.columns;if(c!=null&&h!=null)l=c,u=h;else if(c!=null&&h==null)l=c,u=Math.ceil(s/l);else if(c==null&&h!=null)u=h,l=Math.ceil(s/u);else if(u*l>s){var d=v(),m=f();(d-1)*m>=s?v(d-1):(m-1)*d>=s&&f(m-1)}else for(;u*l<s;){var g=v(),p=f();(p+1)*g>=s?f(p+1):v(g+1)}var y=i.w/u,b=i.h/l;if(e.condense&&(y=0,b=0),e.avoidOverlap)for(var w=0;w<n.length;w++){var E=n[w],C=E._private.position;(C.x==null||C.y==null)&&(C.x=0,C.y=0);var x=E.layoutDimensions(e),T=e.avoidOverlapPadding,S=x.w+T,P=x.h+T;y=Math.max(y,S),b=Math.max(b,P)}for(var D={},A=function(X,j){return!!D["c-"+X+"-"+j]},B=function(X,j){D["c-"+X+"-"+j]=!0},R=0,M=0,L=function(){M++,M>=u&&(M=0,R++)},I={},O=0;O<n.length;O++){var F=n[O],_=e.position(F);if(_&&(_.row!==void 0||_.col!==void 0)){var N={row:_.row,col:_.col};if(N.col===void 0)for(N.col=0;A(N.row,N.col);)N.col++;else if(N.row===void 0)for(N.row=0;A(N.row,N.col);)N.row++;I[F.id()]=N,B(N.row,N.col)}}var q=function(X,j){var J,re;if(X.locked()||X.isParent())return!1;var ae=I[X.id()];if(ae)J=ae.col*y+y/2+i.x1,re=ae.row*b+b/2+i.y1;else{for(;A(R,M);)L();J=M*y+y/2+i.x1,re=R*b+b/2+i.y1,B(R,M),L()}return{x:J,y:re}};n.layoutPositions(this,e,q)}return this};var $p={ready:function(){},stop:function(){}};function ho(r){this.options=pe({},$p,r)}ho.prototype.run=function(){var r=this.options,e=r.eles,t=this;return r.cy,t.emit("layoutstart"),e.nodes().positions(function(){return{x:0,y:0}}),t.one("layoutready",r.ready),t.emit("layoutready"),t.one("layoutstop",r.stop),t.emit("layoutstop"),this};ho.prototype.stop=function(){return this};var Up={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,spacingFactor:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function uf(r){this.options=pe({},Up,r)}uf.prototype.run=function(){var r=this.options,e=r.eles,t=e.nodes(),a=$e(r.positions);function n(i){if(r.positions==null)return fd(i.position());if(a)return r.positions(i);var s=r.positions[i._private.data.id];return s??null}return t.layoutPositions(this,r,function(i,s){var o=n(i);return i.locked()||o==null?!1:o}),this};var Kp={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function lf(r){this.options=pe({},Kp,r)}lf.prototype.run=function(){var r=this.options,e=r.cy,t=r.eles,a=wr(r.boundingBox?r.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),n=function(s,o){return{x:a.x1+Math.round(Math.random()*a.w),y:a.y1+Math.round(Math.random()*a.h)}};return t.nodes().layoutPositions(this,r,n),this};var Xp=[{name:"breadthfirst",impl:ef},{name:"circle",impl:rf},{name:"concentric",impl:tf},{name:"cose",impl:Kn},{name:"grid",impl:of},{name:"null",impl:ho},{name:"preset",impl:uf},{name:"random",impl:lf}];function vf(r){this.options=r,this.notifications=0}var kl=function(){},Dl=function(){throw new Error("A headless instance can not render images")};vf.prototype={recalculateRenderedStyle:kl,notify:function(){this.notifications++},init:kl,isHeadless:function(){return!0},png:Dl,jpg:Dl};var go={};go.arrowShapeWidth=.3;go.registerArrowShapes=function(){var r=this.arrowShapes={},e=this,t=function(u,v,f,c,h,d,m){var g=h.x-f/2-m,p=h.x+f/2+m,y=h.y-f/2-m,b=h.y+f/2+m,w=g<=u&&u<=p&&y<=v&&v<=b;return w},a=function(u,v,f,c,h){var d=u*Math.cos(c)-v*Math.sin(c),m=u*Math.sin(c)+v*Math.cos(c),g=d*f,p=m*f,y=g+h.x,b=p+h.y;return{x:y,y:b}},n=function(u,v,f,c){for(var h=[],d=0;d<u.length;d+=2){var m=u[d],g=u[d+1];h.push(a(m,g,v,f,c))}return h},i=function(u){for(var v=[],f=0;f<u.length;f++){var c=u[f];v.push(c.x,c.y)}return v},s=function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").pfValue*2},o=function(u,v){de(v)&&(v=r[v]),r[u]=pe({name:u,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(c,h,d,m,g,p){var y=i(n(this.points,d+2*p,m,g)),b=Sr(c,h,y);return b},roughCollide:t,draw:function(c,h,d,m){var g=n(this.points,h,d,m);e.arrowShapeImpl("polygon")(c,g)},spacing:function(c){return 0},gap:s},v)};o("none",{collide:xn,roughCollide:xn,draw:js,spacing:Go,gap:Go}),o("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),o("arrow","triangle"),o("triangle-backcurve",{points:r.triangle.points,controlPoint:[0,-.15],roughCollide:t,draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),m=this.controlPoint,g=a(m[0],m[1],v,f,c);e.arrowShapeImpl(this.name)(u,d,g)},gap:function(u){return s(u)*.8}}),o("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(u,v,f,c,h,d,m){var g=i(n(this.points,f+2*m,c,h)),p=i(n(this.pointsTee,f+2*m,c,h)),y=Sr(u,v,g)||Sr(u,v,p);return y},draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),m=n(this.pointsTee,v,f,c);e.arrowShapeImpl(this.name)(u,d,m)}}),o("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(u,v,f,c,h,d,m){var g=h,p=Math.pow(g.x-u,2)+Math.pow(g.y-v,2)<=Math.pow((f+2*m)*this.radius,2),y=i(n(this.points,f+2*m,c,h));return Sr(u,v,y)||p},draw:function(u,v,f,c,h){var d=n(this.pointsTr,v,f,c);e.arrowShapeImpl(this.name)(u,d,c.x,c.y,this.radius*v)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(u,v){var f=this.baseCrossLinePts.slice(),c=v/u,h=3,d=5;return f[h]=f[h]-c,f[d]=f[d]-c,f},collide:function(u,v,f,c,h,d,m){var g=i(n(this.points,f+2*m,c,h)),p=i(n(this.crossLinePts(f,d),f+2*m,c,h)),y=Sr(u,v,g)||Sr(u,v,p);return y},draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),m=n(this.crossLinePts(v,h),v,f,c);e.arrowShapeImpl(this.name)(u,d,m)}}),o("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(u){return s(u)*.525}}),o("circle",{radius:.15,collide:function(u,v,f,c,h,d,m){var g=h,p=Math.pow(g.x-u,2)+Math.pow(g.y-v,2)<=Math.pow((f+2*m)*this.radius,2);return p},draw:function(u,v,f,c,h){e.arrowShapeImpl(this.name)(u,c.x,c.y,this.radius*v)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(u){return 1},gap:function(u){return 1}}),o("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),o("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}}),o("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(u){return .95*u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}})};var Rt={};Rt.projectIntoViewport=function(r,e){var t=this.cy,a=this.findContainerClientCoords(),n=a[0],i=a[1],s=a[4],o=t.pan(),l=t.zoom(),u=((r-n)/s-o.x)/l,v=((e-i)/s-o.y)/l;return[u,v]};Rt.findContainerClientCoords=function(){if(this.containerBB)return this.containerBB;var r=this.container,e=r.getBoundingClientRect(),t=this.cy.window().getComputedStyle(r),a=function(p){return parseFloat(t.getPropertyValue(p))},n={left:a("padding-left"),right:a("padding-right"),top:a("padding-top"),bottom:a("padding-bottom")},i={left:a("border-left-width"),right:a("border-right-width"),top:a("border-top-width"),bottom:a("border-bottom-width")},s=r.clientWidth,o=r.clientHeight,l=n.left+n.right,u=n.top+n.bottom,v=i.left+i.right,f=e.width/(s+v),c=s-l,h=o-u,d=e.left+n.left+i.left,m=e.top+n.top+i.top;return this.containerBB=[d,m,c,h,f]};Rt.invalidateContainerClientCoordsCache=function(){this.containerBB=null};Rt.findNearestElement=function(r,e,t,a){return this.findNearestElements(r,e,t,a)[0]};Rt.findNearestElements=function(r,e,t,a){var n=this,i=this,s=i.getCachedZSortedEles(),o=[],l=i.cy.zoom(),u=i.cy.hasCompoundNodes(),v=(a?24:8)/l,f=(a?8:2)/l,c=(a?8:2)/l,h=1/0,d,m;t&&(s=s.interactive);function g(x,T){if(x.isNode()){if(m)return;m=x,o.push(x)}if(x.isEdge()&&(T==null||T<h))if(d){if(d.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value&&d.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value){for(var S=0;S<o.length;S++)if(o[S].isEdge()){o[S]=x,d=x,h=T??h;break}}}else o.push(x),d=x,h=T??h}function p(x){var T=x.outerWidth()+2*f,S=x.outerHeight()+2*f,P=T/2,D=S/2,A=x.position(),B=x.pstyle("corner-radius").value==="auto"?"auto":x.pstyle("corner-radius").pfValue,R=x._private.rscratch;if(A.x-P<=r&&r<=A.x+P&&A.y-D<=e&&e<=A.y+D){var M=i.nodeShapes[n.getNodeShape(x)];if(M.checkPoint(r,e,0,T,S,A.x,A.y,B,R))return g(x,0),!0}}function y(x){var T=x._private,S=T.rscratch,P=x.pstyle("width").pfValue,D=x.pstyle("arrow-scale").value,A=P/2+v,B=A*A,R=A*2,O=T.source,F=T.target,M;if(S.edgeType==="segments"||S.edgeType==="straight"||S.edgeType==="haystack"){for(var L=S.allpts,I=0;I+3<L.length;I+=2)if(Dd(r,e,L[I],L[I+1],L[I+2],L[I+3],R)&&B>(M=Md(r,e,L[I],L[I+1],L[I+2],L[I+3])))return g(x,M),!0}else if(S.edgeType==="bezier"||S.edgeType==="multibezier"||S.edgeType==="self"||S.edgeType==="compound"){for(var L=S.allpts,I=0;I+5<S.allpts.length;I+=4)if(Bd(r,e,L[I],L[I+1],L[I+2],L[I+3],L[I+4],L[I+5],R)&&B>(M=Rd(r,e,L[I],L[I+1],L[I+2],L[I+3],L[I+4],L[I+5])))return g(x,M),!0}for(var O=O||T.source,F=F||T.target,_=n.getArrowWidth(P,D),N=[{name:"source",x:S.arrowStartX,y:S.arrowStartY,angle:S.srcArrowAngle},{name:"target",x:S.arrowEndX,y:S.arrowEndY,angle:S.tgtArrowAngle},{name:"mid-source",x:S.midX,y:S.midY,angle:S.midsrcArrowAngle},{name:"mid-target",x:S.midX,y:S.midY,angle:S.midtgtArrowAngle}],I=0;I<N.length;I++){var q=N[I],U=i.arrowShapes[x.pstyle(q.name+"-arrow-shape").value],X=x.pstyle("width").pfValue;if(U.roughCollide(r,e,_,q.angle,{x:q.x,y:q.y},X,v)&&U.collide(r,e,_,q.angle,{x:q.x,y:q.y},X,v))return g(x),!0}u&&o.length>0&&(p(O),p(F))}function b(x,T,S){return Tr(x,T,S)}function w(x,T){var S=x._private,P=c,D;T?D=T+"-":D="",x.boundingBox();var A=S.labelBounds[T||"main"],B=x.pstyle(D+"label").value,R=x.pstyle("text-events").strValue==="yes";if(!(!R||!B)){var M=b(S.rscratch,"labelX",T),L=b(S.rscratch,"labelY",T),I=b(S.rscratch,"labelAngle",T),O=x.pstyle(D+"text-margin-x").pfValue,F=x.pstyle(D+"text-margin-y").pfValue,_=A.x1-P-O,N=A.x2+P-O,q=A.y1-P-F,U=A.y2+P-F;if(I){var X=Math.cos(I),j=Math.sin(I),J=function(Q,ne){return Q=Q-M,ne=ne-L,{x:Q*X-ne*j+M,y:Q*j+ne*X+L}},re=J(_,q),ae=J(_,U),Z=J(N,q),z=J(N,U),G=[re.x+O,re.y+F,Z.x+O,Z.y+F,z.x+O,z.y+F,ae.x+O,ae.y+F];if(Sr(r,e,G))return g(x),!0}else if(Qt(A,r,e))return g(x),!0}}for(var E=s.length-1;E>=0;E--){var C=s[E];C.isNode()?p(C)||w(C):y(C)||w(C)||w(C,"source")||w(C,"target")}return o};Rt.getAllInBox=function(r,e,t,a){var n=this.getCachedZSortedEles().interactive,i=this.cy.zoom(),s=2/i,o=[],l=Math.min(r,t),u=Math.max(r,t),v=Math.min(e,a),f=Math.max(e,a);r=l,t=u,e=v,a=f;var c=wr({x1:r,y1:e,x2:t,y2:a});function h(B,R,M){return Tr(B,R,M)}function d(B,R){var M=B._private,L=s,I="";B.boundingBox();var O=M.labelBounds.main;if(!O)return null;var F=h(M.rscratch,"labelX",R),_=h(M.rscratch,"labelY",R),N=h(M.rscratch,"labelAngle",R),q=B.pstyle(I+"text-margin-x").pfValue,U=B.pstyle(I+"text-margin-y").pfValue,X=O.x1-L-q,j=O.x2+L-q,J=O.y1-L-U,re=O.y2+L-U;if(N){var ae=Math.cos(N),Z=Math.sin(N),z=function(H,Q){return H=H-F,Q=Q-_,{x:H*ae-Q*Z+F,y:H*Z+Q*ae+_}};return[z(X,J),z(j,J),z(j,re),z(X,re)]}else return[{x:X,y:J},{x:j,y:J},{x:j,y:re},{x:X,y:re}]}for(var m=0;m<n.length;m++){var g=n[m];if(g.isNode()){var p=g,y=p.pstyle("text-events").strValue==="yes",b=p.pstyle("box-select-labels").strValue==="yes",w=p.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:b&&y});if(ao(c,w)){var E=d(p),C=[{x:c.x1,y:c.y1},{x:c.x2,y:c.y1},{x:c.x2,y:c.y2},{x:c.x1,y:c.y2}];(!E||Fd(E,C))&&o.push(p)}}else{var x=g,T=x._private,S=T.rscratch;if(S.startX!=null&&S.startY!=null&&!Qt(c,S.startX,S.startY)||S.endX!=null&&S.endY!=null&&!Qt(c,S.endX,S.endY))continue;if(S.edgeType==="bezier"||S.edgeType==="multibezier"||S.edgeType==="self"||S.edgeType==="compound"||S.edgeType==="segments"||S.edgeType==="haystack"){for(var P=T.rstyle.bezierPts||T.rstyle.linePts||T.rstyle.haystackPts,D=!0,A=0;A<P.length;A++)if(!Ed(c,P[A])){D=!1;break}D&&o.push(x)}else(S.edgeType==="haystack"||S.edgeType==="straight")&&o.push(x)}}return o};var Bn={};Bn.calculateArrowAngles=function(r){var e=r._private.rscratch,t=e.edgeType==="haystack",a=e.edgeType==="bezier",n=e.edgeType==="multibezier",i=e.edgeType==="segments",s=e.edgeType==="compound",o=e.edgeType==="self",l,u,v,f,c,h,p,y;if(t?(v=e.haystackPts[0],f=e.haystackPts[1],c=e.haystackPts[2],h=e.haystackPts[3]):(v=e.arrowStartX,f=e.arrowStartY,c=e.arrowEndX,h=e.arrowEndY),p=e.midX,y=e.midY,i)l=v-e.segpts[0],u=f-e.segpts[1];else if(n||s||o||a){var d=e.allpts,m=sr(d[0],d[2],d[4],.1),g=sr(d[1],d[3],d[5],.1);l=v-m,u=f-g}else l=v-p,u=f-y;e.srcArrowAngle=Ya(l,u);var p=e.midX,y=e.midY;if(t&&(p=(v+c)/2,y=(f+h)/2),l=c-v,u=h-f,i){var d=e.allpts;if(d.length/2%2===0){var b=d.length/2,w=b-2;l=d[b]-d[w],u=d[b+1]-d[w+1]}else if(e.isRound)l=e.midVector[1],u=-e.midVector[0];else{var b=d.length/2-1,w=b-2;l=d[b]-d[w],u=d[b+1]-d[w+1]}}else if(n||s||o){var d=e.allpts,E=e.ctrlpts,C,x,T,S;if(E.length/2%2===0){var P=d.length/2-1,D=P+2,A=D+2;C=sr(d[P],d[D],d[A],0),x=sr(d[P+1],d[D+1],d[A+1],0),T=sr(d[P],d[D],d[A],1e-4),S=sr(d[P+1],d[D+1],d[A+1],1e-4)}else{var D=d.length/2-1,P=D-2,A=D+2;C=sr(d[P],d[D],d[A],.4999),x=sr(d[P+1],d[D+1],d[A+1],.4999),T=sr(d[P],d[D],d[A],.5),S=sr(d[P+1],d[D+1],d[A+1],.5)}l=T-C,u=S-x}if(e.midtgtArrowAngle=Ya(l,u),e.midDispX=l,e.midDispY=u,l*=-1,u*=-1,i){var d=e.allpts;if(d.length/2%2!==0){if(!e.isRound){var b=d.length/2-1,B=b+2;l=-(d[B]-d[b]),u=-(d[B+1]-d[b+1])}}}if(e.midsrcArrowAngle=Ya(l,u),i)l=c-e.segpts[e.segpts.length-2],u=h-e.segpts[e.segpts.length-1];else if(n||s||o||a){var d=e.allpts,R=d.length,m=sr(d[R-6],d[R-4],d[R-2],.9),g=sr(d[R-5],d[R-3],d[R-1],.9);l=c-m,u=h-g}else l=c-p,u=h-y;e.tgtArrowAngle=Ya(l,u)};Bn.getArrowWidth=Bn.getArrowHeight=function(r,e){var t=this.arrowWidthCache=this.arrowWidthCache||{},a=t[r+", "+e];return a||(a=Math.max(Math.pow(r*13.37,.9),29)*e,t[r+", "+e]=a,a)};var Vs,qs,Vr={},Pr={},Bl,Pl,Tt,gn,Ur,bt,Et,zr,Ft,an,ff,cf,_s,Gs,Al,Rl=function(e,t,a){a.x=t.x-e.x,a.y=t.y-e.y,a.len=Math.sqrt(a.x*a.x+a.y*a.y),a.nx=a.x/a.len,a.ny=a.y/a.len,a.ang=Math.atan2(a.ny,a.nx)},Yp=function(e,t){t.x=e.x*-1,t.y=e.y*-1,t.nx=e.nx*-1,t.ny=e.ny*-1,t.ang=e.ang>0?-(Math.PI-e.ang):Math.PI+e.ang},Zp=function(e,t,a,n,i){if(e!==Al?Rl(t,e,Vr):Yp(Pr,Vr),Rl(t,a,Pr),Bl=Vr.nx*Pr.ny-Vr.ny*Pr.nx,Pl=Vr.nx*Pr.nx-Vr.ny*-Pr.ny,Ur=Math.asin(Math.max(-1,Math.min(1,Bl))),Math.abs(Ur)<1e-6){Vs=t.x,qs=t.y,Et=Ft=0;return}Tt=1,gn=!1,Pl<0?Ur<0?Ur=Math.PI+Ur:(Ur=Math.PI-Ur,Tt=-1,gn=!0):Ur>0&&(Tt=-1,gn=!0),t.radius!==void 0?Ft=t.radius:Ft=n,bt=Ur/2,an=Math.min(Vr.len/2,Pr.len/2),i?(zr=Math.abs(Math.cos(bt)*Ft/Math.sin(bt)),zr>an?(zr=an,Et=Math.abs(zr*Math.sin(bt)/Math.cos(bt))):Et=Ft):(zr=Math.min(an,Ft),Et=Math.abs(zr*Math.sin(bt)/Math.cos(bt))),_s=t.x+Pr.nx*zr,Gs=t.y+Pr.ny*zr,Vs=_s-Pr.ny*Et*Tt,qs=Gs+Pr.nx*Et*Tt,ff=t.x+Vr.nx*zr,cf=t.y+Vr.ny*zr,Al=t};function df(r,e){e.radius===0?r.lineTo(e.cx,e.cy):r.arc(e.cx,e.cy,e.radius,e.startAngle,e.endAngle,e.counterClockwise)}function po(r,e,t,a){var n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0;return a===0||e.radius===0?{cx:e.x,cy:e.y,radius:0,startX:e.x,startY:e.y,stopX:e.x,stopY:e.y,startAngle:void 0,endAngle:void 0,counterClockwise:void 0}:(Zp(r,e,t,a,n),{cx:Vs,cy:qs,radius:Et,startX:ff,startY:cf,stopX:_s,stopY:Gs,startAngle:Vr.ang+Math.PI/2*Tt,endAngle:Pr.ang-Math.PI/2*Tt,counterClockwise:gn})}var Ma=.01,Qp=Math.sqrt(2*Ma),yr={};yr.findMidptPtsEtc=function(r,e){var t=e.posPts,a=e.intersectionPts,n=e.vectorNormInverse,i,s=r.pstyle("source-endpoint"),o=r.pstyle("target-endpoint"),l=s.units!=null&&o.units!=null,u=function(E,C,x,T){var S=T-C,P=x-E,D=Math.sqrt(P*P+S*S);return{x:-S/D,y:P/D}},v=r.pstyle("edge-distances").value;switch(v){case"node-position":i=t;break;case"intersection":i=a;break;case"endpoints":{if(l){var f=this.manualEndptToPx(r.source()[0],s),c=rr(f,2),h=c[0],d=c[1],m=this.manualEndptToPx(r.target()[0],o),g=rr(m,2),p=g[0],y=g[1],b={x1:h,y1:d,x2:p,y2:y};n=u(h,d,p,y),i=b}else Ie("Edge ".concat(r.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default).")),i=a;break}}return{midptPts:i,vectorNormInverse:n}};yr.findHaystackPoints=function(r){for(var e=0;e<r.length;e++){var t=r[e],a=t._private,n=a.rscratch;if(!n.haystack){var i=Math.random()*2*Math.PI;n.source={x:Math.cos(i),y:Math.sin(i)},i=Math.random()*2*Math.PI,n.target={x:Math.cos(i),y:Math.sin(i)}}var s=a.source,o=a.target,l=s.position(),u=o.position(),v=s.width(),f=o.width(),c=s.height(),h=o.height(),d=t.pstyle("haystack-radius").value,m=d/2;n.haystackPts=n.allpts=[n.source.x*v*m+l.x,n.source.y*c*m+l.y,n.target.x*f*m+u.x,n.target.y*h*m+u.y],n.midX=(n.allpts[0]+n.allpts[2])/2,n.midY=(n.allpts[1]+n.allpts[3])/2,n.edgeType="haystack",n.haystack=!0,this.storeEdgeProjections(t),this.calculateArrowAngles(t),this.recalculateEdgeLabelProjections(t),this.calculateLabelAngles(t)}};yr.findSegmentsPoints=function(r,e){var t=r._private.rscratch,a=r.pstyle("segment-weights"),n=r.pstyle("segment-distances"),i=r.pstyle("segment-radii"),s=r.pstyle("radius-type"),o=Math.min(a.pfValue.length,n.pfValue.length),l=i.pfValue[i.pfValue.length-1],u=s.pfValue[s.pfValue.length-1];t.edgeType="segments",t.segpts=[],t.radii=[],t.isArcRadius=[];for(var v=0;v<o;v++){var f=a.pfValue[v],c=n.pfValue[v],h=1-f,d=f,m=this.findMidptPtsEtc(r,e),g=m.midptPts,p=m.vectorNormInverse,y={x:g.x1*h+g.x2*d,y:g.y1*h+g.y2*d};t.segpts.push(y.x+p.x*c,y.y+p.y*c),t.radii.push(i.pfValue[v]!==void 0?i.pfValue[v]:l),t.isArcRadius.push((s.pfValue[v]!==void 0?s.pfValue[v]:u)==="arc-radius")}};yr.findLoopPoints=function(r,e,t,a){var n=r._private.rscratch,i=e.dirCounts,s=e.srcPos,o=r.pstyle("control-point-distances"),l=o?o.pfValue[0]:void 0,u=r.pstyle("loop-direction").pfValue,v=r.pstyle("loop-sweep").pfValue,f=r.pstyle("control-point-step-size").pfValue;n.edgeType="self";var c=t,h=f;a&&(c=0,h=l);var d=u-Math.PI/2,m=d-v/2,g=d+v/2,p=u+"_"+v;c=i[p]===void 0?i[p]=0:++i[p],n.ctrlpts=[s.x+Math.cos(m)*1.4*h*(c/3+1),s.y+Math.sin(m)*1.4*h*(c/3+1),s.x+Math.cos(g)*1.4*h*(c/3+1),s.y+Math.sin(g)*1.4*h*(c/3+1)]};yr.findCompoundLoopPoints=function(r,e,t,a){var n=r._private.rscratch;n.edgeType="compound";var i=e.srcPos,s=e.tgtPos,o=e.srcW,l=e.srcH,u=e.tgtW,v=e.tgtH,f=r.pstyle("control-point-step-size").pfValue,c=r.pstyle("control-point-distances"),h=c?c.pfValue[0]:void 0,d=t,m=f;a&&(d=0,m=h);var g=50,p={x:i.x-o/2,y:i.y-l/2},y={x:s.x-u/2,y:s.y-v/2},b={x:Math.min(p.x,y.x),y:Math.min(p.y,y.y)},w=.5,E=Math.max(w,Math.log(o*Ma)),C=Math.max(w,Math.log(u*Ma));n.ctrlpts=[b.x,b.y-(1+Math.pow(g,1.12)/100)*m*(d/3+1)*E,b.x-(1+Math.pow(g,1.12)/100)*m*(d/3+1)*C,b.y]};yr.findStraightEdgePoints=function(r){r._private.rscratch.edgeType="straight"};yr.findBezierPoints=function(r,e,t,a,n){var i=r._private.rscratch,s=r.pstyle("control-point-step-size").pfValue,o=r.pstyle("control-point-distances"),l=r.pstyle("control-point-weights"),u=o&&l?Math.min(o.value.length,l.value.length):1,v=o?o.pfValue[0]:void 0,f=l.value[0],c=a;i.edgeType=c?"multibezier":"bezier",i.ctrlpts=[];for(var h=0;h<u;h++){var d=(.5-e.eles.length/2+t)*s*(n?-1:1),m=void 0,g=to(d);c&&(v=o?o.pfValue[h]:s,f=l.value[h]),a?m=v:m=v!==void 0?g*v:void 0;var p=m!==void 0?m:d,y=1-f,b=f,w=this.findMidptPtsEtc(r,e),E=w.midptPts,C=w.vectorNormInverse,x={x:E.x1*y+E.x2*b,y:E.y1*y+E.y2*b};i.ctrlpts.push(x.x+C.x*p,x.y+C.y*p)}};yr.findTaxiPoints=function(r,e){var t=r._private.rscratch;t.edgeType="segments";var a="vertical",n="horizontal",i="leftward",s="rightward",o="downward",l="upward",u="auto",v=e.posPts,f=e.srcW,c=e.srcH,h=e.tgtW,d=e.tgtH,m=r.pstyle("edge-distances").value,g=m!=="node-position",p=r.pstyle("taxi-direction").value,y=p,b=r.pstyle("taxi-turn"),w=b.units==="%",E=b.pfValue,C=E<0,x=r.pstyle("taxi-turn-min-distance").pfValue,T=g?(f+h)/2:0,S=g?(c+d)/2:0,P=v.x2-v.x1,D=v.y2-v.y1,A=function(k,V){return k>0?Math.max(k-V,0):Math.min(k+V,0)},B=A(P,T),R=A(D,S),M=!1;y===u?p=Math.abs(B)>Math.abs(R)?n:a:y===l||y===o?(p=a,M=!0):(y===i||y===s)&&(p=n,M=!0);var L=p===a,I=L?R:B,O=L?D:P,F=to(O),_=!1;!(M&&(w||C))&&(y===o&&O<0||y===l&&O>0||y===i&&O>0||y===s&&O<0)&&(F*=-1,I=F*Math.abs(I),_=!0);var N;if(w){var q=E<0?1+E:E;N=q*I}else{var U=E<0?I:0;N=U+E*F}var X=function(k){return Math.abs(k)<x||Math.abs(k)>=Math.abs(I)},j=X(N),J=X(Math.abs(I)-Math.abs(N)),re=j||J;if(re&&!_)if(L){var ae=Math.abs(O)<=c/2,Z=Math.abs(P)<=h/2;if(ae){var z=(v.x1+v.x2)/2,G=v.y1,H=v.y2;t.segpts=[z,G,z,H]}else if(Z){var Q=(v.y1+v.y2)/2,ne=v.x1,be=v.x2;t.segpts=[ne,Q,be,Q]}else t.segpts=[v.x1,v.y2]}else{var Fe=Math.abs(O)<=f/2,Be=Math.abs(D)<=d/2;if(Fe){var oe=(v.y1+v.y2)/2,ve=v.x1,he=v.x2;t.segpts=[ve,oe,he,oe]}else if(Be){var ye=(v.x1+v.x2)/2,me=v.y1,we=v.y2;t.segpts=[ye,me,ye,we]}else t.segpts=[v.x2,v.y1]}else if(L){var xe=v.y1+N+(g?c/2*F:0),Pe=v.x1,Ve=v.x2;t.segpts=[Pe,xe,Ve,xe]}else{var Xe=v.x1+N+(g?f/2*F:0),Oe=v.y1,He=v.y2;t.segpts=[Xe,Oe,Xe,He]}if(t.isRound){var ke=r.pstyle("taxi-radius").value,ue=r.pstyle("radius-type").value[0]==="arc-radius";t.radii=new Array(t.segpts.length/2).fill(ke),t.isArcRadius=new Array(t.segpts.length/2).fill(ue)}};yr.tryToCorrectInvalidPoints=function(r,e){var t=r._private.rscratch;if(t.edgeType==="bezier"){var a=e.srcPos,n=e.tgtPos,i=e.srcW,s=e.srcH,o=e.tgtW,l=e.tgtH,u=e.srcShape,v=e.tgtShape,f=e.srcCornerRadius,c=e.tgtCornerRadius,h=e.srcRs,d=e.tgtRs,m=!te(t.startX)||!te(t.startY),g=!te(t.arrowStartX)||!te(t.arrowStartY),p=!te(t.endX)||!te(t.endY),y=!te(t.arrowEndX)||!te(t.arrowEndY),b=3,w=this.getArrowWidth(r.pstyle("width").pfValue,r.pstyle("arrow-scale").value)*this.arrowShapeWidth,E=b*w,C=Dt({x:t.ctrlpts[0],y:t.ctrlpts[1]},{x:t.startX,y:t.startY}),x=C<E,T=Dt({x:t.ctrlpts[0],y:t.ctrlpts[1]},{x:t.endX,y:t.endY}),S=T<E,P=!1;if(m||g||x){P=!0;var D={x:t.ctrlpts[0]-a.x,y:t.ctrlpts[1]-a.y},A=Math.sqrt(D.x*D.x+D.y*D.y),B={x:D.x/A,y:D.y/A},R=Math.max(i,s),M={x:t.ctrlpts[0]+B.x*2*R,y:t.ctrlpts[1]+B.y*2*R},L=u.intersectLine(a.x,a.y,i,s,M.x,M.y,0,f,h);x?(t.ctrlpts[0]=t.ctrlpts[0]+B.x*(E-C),t.ctrlpts[1]=t.ctrlpts[1]+B.y*(E-C)):(t.ctrlpts[0]=L[0]+B.x*E,t.ctrlpts[1]=L[1]+B.y*E)}if(p||y||S){P=!0;var I={x:t.ctrlpts[0]-n.x,y:t.ctrlpts[1]-n.y},O=Math.sqrt(I.x*I.x+I.y*I.y),F={x:I.x/O,y:I.y/O},_=Math.max(i,s),N={x:t.ctrlpts[0]+F.x*2*_,y:t.ctrlpts[1]+F.y*2*_},q=v.intersectLine(n.x,n.y,o,l,N.x,N.y,0,c,d);S?(t.ctrlpts[0]=t.ctrlpts[0]+F.x*(E-T),t.ctrlpts[1]=t.ctrlpts[1]+F.y*(E-T)):(t.ctrlpts[0]=q[0]+F.x*E,t.ctrlpts[1]=q[1]+F.y*E)}P&&this.findEndpoints(r)}};yr.storeAllpts=function(r){var e=r._private.rscratch;if(e.edgeType==="multibezier"||e.edgeType==="bezier"||e.edgeType==="self"||e.edgeType==="compound"){e.allpts=[],e.allpts.push(e.startX,e.startY);for(var t=0;t+1<e.ctrlpts.length;t+=2)e.allpts.push(e.ctrlpts[t],e.ctrlpts[t+1]),t+3<e.ctrlpts.length&&e.allpts.push((e.ctrlpts[t]+e.ctrlpts[t+2])/2,(e.ctrlpts[t+1]+e.ctrlpts[t+3])/2);e.allpts.push(e.endX,e.endY);var a,n;e.ctrlpts.length/2%2===0?(a=e.allpts.length/2-1,e.midX=e.allpts[a],e.midY=e.allpts[a+1]):(a=e.allpts.length/2-3,n=.5,e.midX=sr(e.allpts[a],e.allpts[a+2],e.allpts[a+4],n),e.midY=sr(e.allpts[a+1],e.allpts[a+3],e.allpts[a+5],n))}else if(e.edgeType==="straight")e.allpts=[e.startX,e.startY,e.endX,e.endY],e.midX=(e.startX+e.endX+e.arrowStartX+e.arrowEndX)/4,e.midY=(e.startY+e.endY+e.arrowStartY+e.arrowEndY)/4;else if(e.edgeType==="segments"){if(e.allpts=[],e.allpts.push(e.startX,e.startY),e.allpts.push.apply(e.allpts,e.segpts),e.allpts.push(e.endX,e.endY),e.isRound){e.roundCorners=[];for(var i=2;i+3<e.allpts.length;i+=2){var s=e.radii[i/2-1],o=e.isArcRadius[i/2-1];e.roundCorners.push(po({x:e.allpts[i-2],y:e.allpts[i-1]},{x:e.allpts[i],y:e.allpts[i+1],radius:s},{x:e.allpts[i+2],y:e.allpts[i+3]},s,o))}}if(e.segpts.length%4===0){var l=e.segpts.length/2,u=l-2;e.midX=(e.segpts[u]+e.segpts[l])/2,e.midY=(e.segpts[u+1]+e.segpts[l+1])/2}else{var v=e.segpts.length/2-1;if(!e.isRound)e.midX=e.segpts[v],e.midY=e.segpts[v+1];else{var f={x:e.segpts[v],y:e.segpts[v+1]},c=e.roundCorners[v/2];if(c.radius===0){var h={x:e.segpts[v+2],y:e.segpts[v+3]};e.midX=f.x,e.midY=f.y,e.midVector=[f.y-h.y,h.x-f.x]}else{var d=[f.x-c.cx,f.y-c.cy],m=c.radius/Math.sqrt(Math.pow(d[0],2)+Math.pow(d[1],2));d=d.map(function(g){return g*m}),e.midX=c.cx+d[0],e.midY=c.cy+d[1],e.midVector=d}}}}};yr.checkForInvalidEdgeWarning=function(r){var e=r[0]._private.rscratch;e.nodesOverlap||te(e.startX)&&te(e.startY)&&te(e.endX)&&te(e.endY)?e.loggedErr=!1:e.loggedErr||(e.loggedErr=!0,Ie("Edge `"+r.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))};yr.findEdgeControlPoints=function(r){var e=this;if(!(!r||r.length===0)){for(var t=this,a=t.cy,n=a.hasCompoundNodes(),i=new Xr,s=function(S,P){return[].concat(mn(S),[P?1:0]).join("-")},o=[],l=[],u=0;u<r.length;u++){var v=r[u],f=v._private,c=v.pstyle("curve-style").value;if(!(v.removed()||!v.takesUpSpace())){if(c==="haystack"){l.push(v);continue}var h=c==="unbundled-bezier"||at(c,"segments")||c==="straight"||c==="straight-triangle"||at(c,"taxi"),d=c==="unbundled-bezier"||c==="bezier",m=f.source,g=f.target,p=m.poolIndex(),y=g.poolIndex(),b=[p,y].sort(),w=s(b,h),E=i.get(w);E==null&&(E={eles:[]},o.push({pairId:b,edgeIsUnbundled:h}),i.set(w,E)),E.eles.push(v),h&&(E.hasUnbundled=!0),d&&(E.hasBezier=!0)}}for(var C=function(){var S=o[x],P=S.pairId,D=S.edgeIsUnbundled,A=s(P,D),B=i.get(A),R;if(!B.hasUnbundled){var M=B.eles[0].parallelEdges().filter(function(ue){return ue.isBundledBezier()});eo(B.eles),M.forEach(function(ue){return B.eles.push(ue)}),B.eles.sort(function(ue,Y){return ue.poolIndex()-Y.poolIndex()})}var L=B.eles[0],I=L.source(),O=L.target();if(I.poolIndex()>O.poolIndex()){var F=I;I=O,O=F}var _=B.srcPos=I.position(),N=B.tgtPos=O.position(),q=B.srcW=I.outerWidth(),U=B.srcH=I.outerHeight(),X=B.tgtW=O.outerWidth(),j=B.tgtH=O.outerHeight(),J=B.srcShape=t.nodeShapes[e.getNodeShape(I)],re=B.tgtShape=t.nodeShapes[e.getNodeShape(O)],ae=B.srcCornerRadius=I.pstyle("corner-radius").value==="auto"?"auto":I.pstyle("corner-radius").pfValue,Z=B.tgtCornerRadius=O.pstyle("corner-radius").value==="auto"?"auto":O.pstyle("corner-radius").pfValue,z=B.tgtRs=O._private.rscratch,G=B.srcRs=I._private.rscratch;B.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var H=0;H<B.eles.length;H++){var Q=B.eles[H],ne=Q[0]._private.rscratch,be=Q.pstyle("curve-style").value,Fe=be==="unbundled-bezier"||at(be,"segments")||at(be,"taxi"),Be=!I.same(Q.source());if(!B.calculatedIntersection&&I!==O&&(B.hasBezier||B.hasUnbundled)){B.calculatedIntersection=!0;var oe=J.intersectLine(_.x,_.y,q,U,N.x,N.y,0,ae,G),ve=B.srcIntn=oe,he=re.intersectLine(N.x,N.y,X,j,_.x,_.y,0,Z,z),ye=B.tgtIntn=he,me=B.intersectionPts={x1:oe[0],x2:he[0],y1:oe[1],y2:he[1]},we=B.posPts={x1:_.x,x2:N.x,y1:_.y,y2:N.y},xe=he[1]-oe[1],Pe=he[0]-oe[0],Ve=Math.sqrt(Pe*Pe+xe*xe);te(Ve)&&Ve>=Qp||(Ve=Math.sqrt(Math.max(Pe*Pe,Ma)+Math.max(xe*xe,Ma)));var Xe=B.vector={x:Pe,y:xe},Oe=B.vectorNorm={x:Xe.x/Ve,y:Xe.y/Ve},He={x:-Oe.y,y:Oe.x};B.nodesOverlap=!te(Ve)||re.checkPoint(oe[0],oe[1],0,X,j,N.x,N.y,Z,z)||J.checkPoint(he[0],he[1],0,q,U,_.x,_.y,ae,G),B.vectorNormInverse=He,R={nodesOverlap:B.nodesOverlap,dirCounts:B.dirCounts,calculatedIntersection:!0,hasBezier:B.hasBezier,hasUnbundled:B.hasUnbundled,eles:B.eles,srcPos:N,srcRs:z,tgtPos:_,tgtRs:G,srcW:X,srcH:j,tgtW:q,tgtH:U,srcIntn:ye,tgtIntn:ve,srcShape:re,tgtShape:J,posPts:{x1:we.x2,y1:we.y2,x2:we.x1,y2:we.y1},intersectionPts:{x1:me.x2,y1:me.y2,x2:me.x1,y2:me.y1},vector:{x:-Xe.x,y:-Xe.y},vectorNorm:{x:-Oe.x,y:-Oe.y},vectorNormInverse:{x:-He.x,y:-He.y}}}var ke=Be?R:B;ne.nodesOverlap=ke.nodesOverlap,ne.srcIntn=ke.srcIntn,ne.tgtIntn=ke.tgtIntn,ne.isRound=be.startsWith("round"),n&&(I.isParent()||I.isChild()||O.isParent()||O.isChild())&&(I.parents().anySame(O)||O.parents().anySame(I)||I.same(O)&&I.isParent())?e.findCompoundLoopPoints(Q,ke,H,Fe):I===O?e.findLoopPoints(Q,ke,H,Fe):be.endsWith("segments")?e.findSegmentsPoints(Q,ke):be.endsWith("taxi")?e.findTaxiPoints(Q,ke):be==="straight"||!Fe&&B.eles.length%2===1&&H===Math.floor(B.eles.length/2)?e.findStraightEdgePoints(Q):e.findBezierPoints(Q,ke,H,Fe,Be),e.findEndpoints(Q),e.tryToCorrectInvalidPoints(Q,ke),e.checkForInvalidEdgeWarning(Q),e.storeAllpts(Q),e.storeEdgeProjections(Q),e.calculateArrowAngles(Q),e.recalculateEdgeLabelProjections(Q),e.calculateLabelAngles(Q)}},x=0;x<o.length;x++)C();this.findHaystackPoints(l)}};function hf(r){var e=[];if(r!=null){for(var t=0;t<r.length;t+=2){var a=r[t],n=r[t+1];e.push({x:a,y:n})}return e}}yr.getSegmentPoints=function(r){var e=r[0]._private.rscratch;this.recalculateRenderedStyle(r);var t=e.edgeType;if(t==="segments")return hf(e.segpts)};yr.getControlPoints=function(r){var e=r[0]._private.rscratch;this.recalculateRenderedStyle(r);var t=e.edgeType;if(t==="bezier"||t==="multibezier"||t==="self"||t==="compound")return hf(e.ctrlpts)};yr.getEdgeMidpoint=function(r){var e=r[0]._private.rscratch;return this.recalculateRenderedStyle(r),{x:e.midX,y:e.midY}};var Ga={};Ga.manualEndptToPx=function(r,e){var t=this,a=r.position(),n=r.outerWidth(),i=r.outerHeight(),s=r._private.rscratch;if(e.value.length===2){var o=[e.pfValue[0],e.pfValue[1]];return e.units[0]==="%"&&(o[0]=o[0]*n),e.units[1]==="%"&&(o[1]=o[1]*i),o[0]+=a.x,o[1]+=a.y,o}else{var l=e.pfValue[0];l=-Math.PI/2+l;var u=2*Math.max(n,i),v=[a.x+Math.cos(l)*u,a.y+Math.sin(l)*u];return t.nodeShapes[this.getNodeShape(r)].intersectLine(a.x,a.y,n,i,v[0],v[1],0,r.pstyle("corner-radius").value==="auto"?"auto":r.pstyle("corner-radius").pfValue,s)}};Ga.findEndpoints=function(r){var e,t,a,n,i=this,s,o=r.source()[0],l=r.target()[0],u=o.position(),v=l.position(),f=r.pstyle("target-arrow-shape").value,c=r.pstyle("source-arrow-shape").value,h=r.pstyle("target-distance-from-node").pfValue,d=r.pstyle("source-distance-from-node").pfValue,m=o._private.rscratch,g=l._private.rscratch,p=r.pstyle("curve-style").value,y=r._private.rscratch,b=y.edgeType,w=at(p,"taxi"),E=b==="self"||b==="compound",C=b==="bezier"||b==="multibezier"||E,x=b!=="bezier",T=b==="straight"||b==="segments",S=b==="segments",P=C||x||T,D=E||w,A=r.pstyle("source-endpoint"),B=D?"outside-to-node":A.value,R=o.pstyle("corner-radius").value==="auto"?"auto":o.pstyle("corner-radius").pfValue,M=r.pstyle("target-endpoint"),L=D?"outside-to-node":M.value,I=l.pstyle("corner-radius").value==="auto"?"auto":l.pstyle("corner-radius").pfValue;y.srcManEndpt=A,y.tgtManEndpt=M;var O,F,_,N,q=(e=(M==null||(t=M.pfValue)===null||t===void 0?void 0:t.length)===2?M.pfValue:null)!==null&&e!==void 0?e:[0,0],U=(a=(A==null||(n=A.pfValue)===null||n===void 0?void 0:n.length)===2?A.pfValue:null)!==null&&a!==void 0?a:[0,0];if(C){var X=[y.ctrlpts[0],y.ctrlpts[1]],j=x?[y.ctrlpts[y.ctrlpts.length-2],y.ctrlpts[y.ctrlpts.length-1]]:X;O=j,F=X}else if(T){var J=S?y.segpts.slice(0,2):[v.x+q[0],v.y+q[1]],re=S?y.segpts.slice(y.segpts.length-2):[u.x+U[0],u.y+U[1]];O=re,F=J}if(L==="inside-to-node")s=[v.x,v.y];else if(M.units)s=this.manualEndptToPx(l,M);else if(L==="outside-to-line")s=y.tgtIntn;else if(L==="outside-to-node"||L==="outside-to-node-or-label"?_=O:(L==="outside-to-line"||L==="outside-to-line-or-label")&&(_=[u.x,u.y]),s=i.nodeShapes[this.getNodeShape(l)].intersectLine(v.x,v.y,l.outerWidth(),l.outerHeight(),_[0],_[1],0,I,g),L==="outside-to-node-or-label"||L==="outside-to-line-or-label"){var ae=l._private.rscratch,Z=ae.labelWidth,z=ae.labelHeight,G=ae.labelX,H=ae.labelY,Q=Z/2,ne=z/2,be=l.pstyle("text-valign").value;be==="top"?H-=ne:be==="bottom"&&(H+=ne);var Fe=l.pstyle("text-halign").value;Fe==="left"?G-=Q:Fe==="right"&&(G+=Q);var Be=Da(_[0],_[1],[G-Q,H-ne,G+Q,H-ne,G+Q,H+ne,G-Q,H+ne],v.x,v.y);if(Be.length>0){var oe=u,ve=xt(oe,Ht(s)),he=xt(oe,Ht(Be)),ye=ve;if(he<ve&&(s=Be,ye=he),Be.length>2){var me=xt(oe,{x:Be[2],y:Be[3]});me<ye&&(s=[Be[2],Be[3]])}}}var we=Za(s,O,i.arrowShapes[f].spacing(r)+h),xe=Za(s,O,i.arrowShapes[f].gap(r)+h);if(y.endX=xe[0],y.endY=xe[1],y.arrowEndX=we[0],y.arrowEndY=we[1],B==="inside-to-node")s=[u.x,u.y];else if(A.units)s=this.manualEndptToPx(o,A);else if(B==="outside-to-line")s=y.srcIntn;else if(B==="outside-to-node"||B==="outside-to-node-or-label"?N=F:(B==="outside-to-line"||B==="outside-to-line-or-label")&&(N=[v.x,v.y]),s=i.nodeShapes[this.getNodeShape(o)].intersectLine(u.x,u.y,o.outerWidth(),o.outerHeight(),N[0],N[1],0,R,m),B==="outside-to-node-or-label"||B==="outside-to-line-or-label"){var Pe=o._private.rscratch,Ve=Pe.labelWidth,Xe=Pe.labelHeight,Oe=Pe.labelX,He=Pe.labelY,ke=Ve/2,ue=Xe/2,Y=o.pstyle("text-valign").value;Y==="top"?He-=ue:Y==="bottom"&&(He+=ue);var k=o.pstyle("text-halign").value;k==="left"?Oe-=ke:k==="right"&&(Oe+=ke);var V=Da(N[0],N[1],[Oe-ke,He-ue,Oe+ke,He-ue,Oe+ke,He+ue,Oe-ke,He+ue],u.x,u.y);if(V.length>0){var W=v,$=xt(W,Ht(s)),K=xt(W,Ht(V)),se=$;if(K<$&&(s=[V[0],V[1]],se=K),V.length>2){var ee=xt(W,{x:V[2],y:V[3]});ee<se&&(s=[V[2],V[3]])}}}var fe=Za(s,F,i.arrowShapes[c].spacing(r)+d),le=Za(s,F,i.arrowShapes[c].gap(r)+d);y.startX=le[0],y.startY=le[1],y.arrowStartX=fe[0],y.arrowStartY=fe[1],P&&(!te(y.startX)||!te(y.startY)||!te(y.endX)||!te(y.endY)?y.badLine=!0:y.badLine=!1)};Ga.getSourceEndpoint=function(r){var e=r[0]._private.rscratch;switch(this.recalculateRenderedStyle(r),e.edgeType){case"haystack":return{x:e.haystackPts[0],y:e.haystackPts[1]};default:return{x:e.arrowStartX,y:e.arrowStartY}}};Ga.getTargetEndpoint=function(r){var e=r[0]._private.rscratch;switch(this.recalculateRenderedStyle(r),e.edgeType){case"haystack":return{x:e.haystackPts[2],y:e.haystackPts[3]};default:return{x:e.arrowEndX,y:e.arrowEndY}}};var yo={};function Jp(r,e,t){for(var a=function(u,v,f,c){return sr(u,v,f,c)},n=e._private,i=n.rstyle.bezierPts,s=0;s<r.bezierProjPcts.length;s++){var o=r.bezierProjPcts[s];i.push({x:a(t[0],t[2],t[4],o),y:a(t[1],t[3],t[5],o)})}}yo.storeEdgeProjections=function(r){var e=r._private,t=e.rscratch,a=t.edgeType;if(e.rstyle.bezierPts=null,e.rstyle.linePts=null,e.rstyle.haystackPts=null,a==="multibezier"||a==="bezier"||a==="self"||a==="compound"){e.rstyle.bezierPts=[];for(var n=0;n+5<t.allpts.length;n+=4)Jp(this,r,t.allpts.slice(n,n+6))}else if(a==="segments")for(var i=e.rstyle.linePts=[],n=0;n+1<t.allpts.length;n+=2)i.push({x:t.allpts[n],y:t.allpts[n+1]});else if(a==="haystack"){var s=t.haystackPts;e.rstyle.haystackPts=[{x:s[0],y:s[1]},{x:s[2],y:s[3]}]}e.rstyle.arrowWidth=this.getArrowWidth(r.pstyle("width").pfValue,r.pstyle("arrow-scale").value)*this.arrowShapeWidth};yo.recalculateEdgeProjections=function(r){this.findEdgeControlPoints(r)};var Gr={};Gr.recalculateNodeLabelProjection=function(r){var e=r.pstyle("label").strValue;if(!ot(e)){var t,a,n=r._private,i=r.width(),s=r.height(),o=r.padding(),l=r.position(),u=r.pstyle("text-halign").strValue,v=r.pstyle("text-valign").strValue,f=n.rscratch,c=n.rstyle;switch(u){case"left":t=l.x-i/2-o;break;case"right":t=l.x+i/2+o;break;default:t=l.x}switch(v){case"top":a=l.y-s/2-o;break;case"bottom":a=l.y+s/2+o;break;default:a=l.y}f.labelX=t,f.labelY=a,c.labelX=t,c.labelY=a,this.calculateLabelAngles(r),this.applyLabelDimensions(r)}};var gf=function(e,t){var a=Math.atan(t/e);return e===0&&a<0&&(a=a*-1),a},pf=function(e,t){var a=t.x-e.x,n=t.y-e.y;return gf(a,n)},jp=function(e,t,a,n){var i=ka(0,n-.001,1),s=ka(0,n+.001,1),o=Ut(e,t,a,i),l=Ut(e,t,a,s);return pf(o,l)};Gr.recalculateEdgeLabelProjections=function(r){var e,t=r._private,a=t.rscratch,n=this,i={mid:r.pstyle("label").strValue,source:r.pstyle("source-label").strValue,target:r.pstyle("target-label").strValue};if(i.mid||i.source||i.target){e={x:a.midX,y:a.midY};var s=function(f,c,h){Kr(t.rscratch,f,c,h),Kr(t.rstyle,f,c,h)};s("labelX",null,e.x),s("labelY",null,e.y);var o=gf(a.midDispX,a.midDispY);s("labelAutoAngle",null,o);var l=function(){if(l.cache)return l.cache;for(var f=[],c=0;c+5<a.allpts.length;c+=4){var h={x:a.allpts[c],y:a.allpts[c+1]},d={x:a.allpts[c+2],y:a.allpts[c+3]},m={x:a.allpts[c+4],y:a.allpts[c+5]};f.push({p0:h,p1:d,p2:m,startDist:0,length:0,segments:[]})}var g=t.rstyle.bezierPts,p=n.bezierProjPcts.length;function y(x,T,S,P,D){var A=Dt(T,S),B=x.segments[x.segments.length-1],R={p0:T,p1:S,t0:P,t1:D,startDist:B?B.startDist+B.length:0,length:A};x.segments.push(R),x.length+=A}for(var b=0;b<f.length;b++){var w=f[b],E=f[b-1];E&&(w.startDist=E.startDist+E.length),y(w,w.p0,g[b*p],0,n.bezierProjPcts[0]);for(var C=0;C<p-1;C++)y(w,g[b*p+C],g[b*p+C+1],n.bezierProjPcts[C],n.bezierProjPcts[C+1]);y(w,g[b*p+p-1],w.p2,n.bezierProjPcts[p-1],1)}return l.cache=f},u=function(f){var c,h=f==="source";if(i[f]){var d=r.pstyle(f+"-text-offset").pfValue;switch(a.edgeType){case"self":case"compound":case"bezier":case"multibezier":{for(var m=l(),g,p=0,y=0,b=0;b<m.length;b++){for(var w=m[h?b:m.length-1-b],E=0;E<w.segments.length;E++){var C=w.segments[h?E:w.segments.length-1-E],x=b===m.length-1&&E===w.segments.length-1;if(p=y,y+=C.length,y>=d||x){g={cp:w,segment:C};break}}if(g)break}var T=g.cp,S=g.segment,P=(d-p)/S.length,D=S.t1-S.t0,A=h?S.t0+D*P:S.t1-D*P;A=ka(0,A,1),e=Ut(T.p0,T.p1,T.p2,A),c=jp(T.p0,T.p1,T.p2,A);break}case"straight":case"segments":case"haystack":{for(var B=0,R,M,L,I,O=a.allpts.length,F=0;F+3<O&&(h?(L={x:a.allpts[F],y:a.allpts[F+1]},I={x:a.allpts[F+2],y:a.allpts[F+3]}):(L={x:a.allpts[O-2-F],y:a.allpts[O-1-F]},I={x:a.allpts[O-4-F],y:a.allpts[O-3-F]}),R=Dt(L,I),M=B,B+=R,!(B>=d));F+=2);var _=d-M,N=_/R;N=ka(0,N,1),e=md(L,I,N),c=pf(L,I);break}}s("labelX",f,e.x),s("labelY",f,e.y),s("labelAutoAngle",f,c)}};u("source"),u("target"),this.applyLabelDimensions(r)}};Gr.applyLabelDimensions=function(r){this.applyPrefixedLabelDimensions(r),r.isEdge()&&(this.applyPrefixedLabelDimensions(r,"source"),this.applyPrefixedLabelDimensions(r,"target"))};Gr.applyPrefixedLabelDimensions=function(r,e){var t=r._private,a=this.getLabelText(r,e),n=kt(a,r._private.labelDimsKey);if(Tr(t.rscratch,"prefixedLabelDimsKey",e)!==n){Kr(t.rscratch,"prefixedLabelDimsKey",e,n);var i=this.calculateLabelDimensions(r,a),s=r.pstyle("line-height").pfValue,o=r.pstyle("text-wrap").strValue,l=Tr(t.rscratch,"labelWrapCachedLines",e)||[],u=o!=="wrap"?1:Math.max(l.length,1),v=i.height/u,f=v*s,c=i.width,h=i.height+(u-1)*(s-1)*v;Kr(t.rstyle,"labelWidth",e,c),Kr(t.rscratch,"labelWidth",e,c),Kr(t.rstyle,"labelHeight",e,h),Kr(t.rscratch,"labelHeight",e,h),Kr(t.rscratch,"labelLineHeight",e,f)}};Gr.getLabelText=function(r,e){var t=r._private,a=e?e+"-":"",n=r.pstyle(a+"label").strValue,i=r.pstyle("text-transform").value,s=function(U,X){return X?(Kr(t.rscratch,U,e,X),X):Tr(t.rscratch,U,e)};if(!n)return"";i=="none"||(i=="uppercase"?n=n.toUpperCase():i=="lowercase"&&(n=n.toLowerCase()));var o=r.pstyle("text-wrap").value;if(o==="wrap"){var l=s("labelKey");if(l!=null&&s("labelWrapKey")===l)return s("labelWrapCachedText");for(var u="​",v=n.split(`
`),f=r.pstyle("text-max-width").pfValue,c=r.pstyle("text-overflow-wrap").value,h=c==="anywhere",d=[],m=/[\s\u200b]+|$/g,g=0;g<v.length;g++){var p=v[g],y=this.calculateLabelDimensions(r,p),b=y.width;if(h){var w=p.split("").join(u);p=w}if(b>f){var E=p.matchAll(m),C="",x=0,T=kr(E),S;try{for(T.s();!(S=T.n()).done;){var P=S.value,D=P[0],A=p.substring(x,P.index);x=P.index+D.length;var B=C.length===0?A:C+A+D,R=this.calculateLabelDimensions(r,B),M=R.width;M<=f?C+=A+D:(C&&d.push(C),C=A+D)}}catch(q){T.e(q)}finally{T.f()}C.match(/^[\s\u200b]+$/)||d.push(C)}else d.push(p)}s("labelWrapCachedLines",d),n=s("labelWrapCachedText",d.join(`
`)),s("labelWrapKey",l)}else if(o==="ellipsis"){var L=r.pstyle("text-max-width").pfValue,I="",O="…",F=!1;if(this.calculateLabelDimensions(r,n).width<L)return n;for(var _=0;_<n.length;_++){var N=this.calculateLabelDimensions(r,I+n[_]+O).width;if(N>L)break;I+=n[_],_===n.length-1&&(F=!0)}return F||(I+=O),I}return n};Gr.getLabelJustification=function(r){var e=r.pstyle("text-justification").strValue,t=r.pstyle("text-halign").strValue;if(e==="auto")if(r.isNode())switch(t){case"left":return"right";case"right":return"left";default:return"center"}else return"center";else return e};Gr.calculateLabelDimensions=function(r,e){var t=this,a=t.cy.window(),n=a.document,i=0,s=r.pstyle("font-style").strValue,o=r.pstyle("font-size").pfValue,l=r.pstyle("font-family").strValue,u=r.pstyle("font-weight").strValue,v=this.labelCalcCanvas,f=this.labelCalcCanvasContext;if(!v){v=this.labelCalcCanvas=n.createElement("canvas"),f=this.labelCalcCanvasContext=v.getContext("2d");var c=v.style;c.position="absolute",c.left="-9999px",c.top="-9999px",c.zIndex="-1",c.visibility="hidden",c.pointerEvents="none"}f.font="".concat(s," ").concat(u," ").concat(o,"px ").concat(l);for(var h=0,d=0,m=e.split(`
`),g=0;g<m.length;g++){var p=m[g],y=f.measureText(p),b=Math.ceil(y.width),w=o;h=Math.max(b,h),d+=w}return h+=i,d+=i,{width:h,height:d}};Gr.calculateLabelAngle=function(r,e){var t=r._private,a=t.rscratch,n=r.isEdge(),i=e?e+"-":"",s=r.pstyle(i+"text-rotation"),o=s.strValue;return o==="none"?0:n&&o==="autorotate"?a.labelAutoAngle:o==="autorotate"?0:s.pfValue};Gr.calculateLabelAngles=function(r){var e=this,t=r.isEdge(),a=r._private,n=a.rscratch;n.labelAngle=e.calculateLabelAngle(r),t&&(n.sourceLabelAngle=e.calculateLabelAngle(r,"source"),n.targetLabelAngle=e.calculateLabelAngle(r,"target"))};var yf={},Ml=28,Ll=!1;yf.getNodeShape=function(r){var e=this,t=r.pstyle("shape").value;if(t==="cutrectangle"&&(r.width()<Ml||r.height()<Ml))return Ll||(Ie("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),Ll=!0),"rectangle";if(r.isParent())return t==="rectangle"||t==="roundrectangle"||t==="round-rectangle"||t==="cutrectangle"||t==="cut-rectangle"||t==="barrel"?t:"rectangle";if(t==="polygon"){var a=r.pstyle("shape-polygon-points").value;return e.nodeShapes.makePolygon(a).name}return t};var Xn={};Xn.registerCalculationListeners=function(){var r=this.cy,e=r.collection(),t=this,a=function(s){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(e.merge(s),o)for(var l=0;l<s.length;l++){var u=s[l],v=u._private,f=v.rstyle;f.clean=!1,f.cleanConnected=!1}};t.binder(r).on("bounds.* dirty.*",function(s){var o=s.target;a(o)}).on("style.* background.*",function(s){var o=s.target;a(o,!1)});var n=function(s){if(s){var o=t.onUpdateEleCalcsFns;e.cleanStyle();for(var l=0;l<e.length;l++){var u=e[l],v=u._private.rstyle;u.isNode()&&!v.cleanConnected&&(a(u.connectedEdges()),v.cleanConnected=!0)}if(o)for(var f=0;f<o.length;f++){var c=o[f];c(s,e)}t.recalculateRenderedStyle(e),e=r.collection()}};t.flushRenderedStyleQueue=function(){n(!0)},t.beforeRender(n,t.beforeRenderPriorities.eleCalcs)};Xn.onUpdateEleCalcs=function(r){var e=this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[];e.push(r)};Xn.recalculateRenderedStyle=function(r,e){var t=function(w){return w._private.rstyle.cleanConnected};if(r.length!==0){var a=[],n=[];if(!this.destroyed){e===void 0&&(e=!0);for(var i=0;i<r.length;i++){var s=r[i],o=s._private,l=o.rstyle;s.isEdge()&&(!t(s.source())||!t(s.target()))&&(l.clean=!1),s.isEdge()&&s.isBundledBezier()&&s.parallelEdges().some(function(b){return!b._private.rstyle.clean&&b.isBundledBezier()})&&(l.clean=!1),!(e&&l.clean||s.removed())&&s.pstyle("display").value!=="none"&&(o.group==="nodes"?n.push(s):a.push(s),l.clean=!0)}for(var u=0;u<n.length;u++){var v=n[u],f=v._private,c=f.rstyle,h=v.position();this.recalculateNodeLabelProjection(v),c.nodeX=h.x,c.nodeY=h.y,c.nodeW=v.pstyle("width").pfValue,c.nodeH=v.pstyle("height").pfValue}this.recalculateEdgeProjections(a);for(var d=0;d<a.length;d++){var m=a[d],g=m._private,p=g.rstyle,y=g.rscratch;p.srcX=y.arrowStartX,p.srcY=y.arrowStartY,p.tgtX=y.arrowEndX,p.tgtY=y.arrowEndY,p.midX=y.midX,p.midY=y.midY,p.labelAngle=y.labelAngle,p.sourceLabelAngle=y.sourceLabelAngle,p.targetLabelAngle=y.targetLabelAngle}}}};var Yn={};Yn.updateCachedGrabbedEles=function(){var r=this.cachedZSortedEles;if(r){r.drag=[],r.nondrag=[];for(var e=[],t=0;t<r.length;t++){var a=r[t],n=a._private.rscratch;a.grabbed()&&!a.isParent()?e.push(a):n.inDragLayer?r.drag.push(a):r.nondrag.push(a)}for(var t=0;t<e.length;t++){var a=e[t];r.drag.push(a)}}};Yn.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null};Yn.getCachedZSortedEles=function(r){if(r||!this.cachedZSortedEles){var e=this.cy.mutableElements().toArray();e.sort(Qv),e.interactive=e.filter(function(t){return t.interactive()}),this.cachedZSortedEles=e,this.updateCachedGrabbedEles()}else e=this.cachedZSortedEles;return e};var mf={};[Rt,Bn,yr,Ga,yo,Gr,yf,Xn,Yn].forEach(function(r){pe(mf,r)});var bf={};bf.getCachedImage=function(r,e,t){var a=this,n=a.imageCache=a.imageCache||{},i=n[r];if(i)return i.image.complete||i.image.addEventListener("load",t),i.image;i=n[r]=n[r]||{};var s=i.image=new Image;s.addEventListener("load",t),s.addEventListener("error",function(){s.error=!0});var o="data:",l=r.substring(0,o.length).toLowerCase()===o;return l||(e=e==="null"?null:e,s.crossOrigin=e),s.src=r,s};var ia={};ia.registerBinding=function(r,e,t,a){var n=Array.prototype.slice.apply(arguments,[1]);if(Array.isArray(r)){for(var i=[],s=0;s<r.length;s++){var o=r[s];if(o!==void 0){var l=this.binder(o);i.push(l.on.apply(l,n))}}return i}var l=this.binder(r);return l.on.apply(l,n)};ia.binder=function(r){var e=this,t=e.cy.window(),a=r===t||r===t.document||r===t.document.body||fc(r);if(e.supportsPassiveEvents==null){var n=!1;try{var i=Object.defineProperty({},"passive",{get:function(){return n=!0,!0}});t.addEventListener("test",null,i)}catch{}e.supportsPassiveEvents=n}var s=function(l,u,v){var f=Array.prototype.slice.call(arguments);return a&&e.supportsPassiveEvents&&(f[2]={capture:v??!1,passive:!1,once:!1}),e.bindings.push({target:r,args:f}),(r.addEventListener||r.on).apply(r,f),this};return{on:s,addEventListener:s,addListener:s,bind:s}};ia.nodeIsDraggable=function(r){return r&&r.isNode()&&!r.locked()&&r.grabbable()};ia.nodeIsGrabbable=function(r){return this.nodeIsDraggable(r)&&r.interactive()};ia.load=function(){var r=this,e=r.cy.window(),t=function(k){return k.selected()},a=function(k){var V=k.getRootNode();if(V&&V.nodeType===11&&V.host!==void 0)return V},n=function(k,V,W,$){k==null&&(k=r.cy);for(var K=0;K<V.length;K++){var se=V[K];k.emit({originalEvent:W,type:se,position:$})}},i=function(k){return k.shiftKey||k.metaKey||k.ctrlKey},s=function(k,V){var W=!0;if(r.cy.hasCompoundNodes()&&k&&k.pannable())for(var $=0;V&&$<V.length;$++){var k=V[$];if(k.isNode()&&k.isParent()&&!k.pannable()){W=!1;break}}else W=!0;return W},o=function(k){k[0]._private.grabbed=!0},l=function(k){k[0]._private.grabbed=!1},u=function(k){k[0]._private.rscratch.inDragLayer=!0},v=function(k){k[0]._private.rscratch.inDragLayer=!1},f=function(k){k[0]._private.rscratch.isGrabTarget=!0},c=function(k){k[0]._private.rscratch.isGrabTarget=!1},h=function(k,V){var W=V.addToList,$=W.has(k);!$&&k.grabbable()&&!k.locked()&&(W.merge(k),o(k))},d=function(k,V){if(k.cy().hasCompoundNodes()&&!(V.inDragLayer==null&&V.addToList==null)){var W=k.descendants();V.inDragLayer&&(W.forEach(u),W.connectedEdges().forEach(u)),V.addToList&&h(W,V)}},m=function(k,V){V=V||{};var W=k.cy().hasCompoundNodes();V.inDragLayer&&(k.forEach(u),k.neighborhood().stdFilter(function($){return!W||$.isEdge()}).forEach(u)),V.addToList&&k.forEach(function($){h($,V)}),d(k,V),y(k,{inDragLayer:V.inDragLayer}),r.updateCachedGrabbedEles()},g=m,p=function(k){k&&(r.getCachedZSortedEles().forEach(function(V){l(V),v(V),c(V)}),r.updateCachedGrabbedEles())},y=function(k,V){if(!(V.inDragLayer==null&&V.addToList==null)&&k.cy().hasCompoundNodes()){var W=k.ancestors().orphans();if(!W.same(k)){var $=W.descendants().spawnSelf().merge(W).unmerge(k).unmerge(k.descendants()),K=$.connectedEdges();V.inDragLayer&&(K.forEach(u),$.forEach(u)),V.addToList&&$.forEach(function(se){h(se,V)})}}},b=function(){document.activeElement!=null&&document.activeElement.blur!=null&&document.activeElement.blur()},w=typeof MutationObserver<"u",E=typeof ResizeObserver<"u";w?(r.removeObserver=new MutationObserver(function(Y){for(var k=0;k<Y.length;k++){var V=Y[k],W=V.removedNodes;if(W)for(var $=0;$<W.length;$++){var K=W[$];if(K===r.container){r.destroy();break}}}}),r.container.parentNode&&r.removeObserver.observe(r.container.parentNode,{childList:!0})):r.registerBinding(r.container,"DOMNodeRemoved",function(Y){r.destroy()});var C=Fa(function(){r.cy.resize()},100);w&&(r.styleObserver=new MutationObserver(C),r.styleObserver.observe(r.container,{attributes:!0})),r.registerBinding(e,"resize",C),E&&(r.resizeObserver=new ResizeObserver(C),r.resizeObserver.observe(r.container));var x=function(k,V){for(;k!=null;)V(k),k=k.parentNode},T=function(){r.invalidateContainerClientCoordsCache()};x(r.container,function(Y){r.registerBinding(Y,"transitionend",T),r.registerBinding(Y,"animationend",T),r.registerBinding(Y,"scroll",T)}),r.registerBinding(r.container,"contextmenu",function(Y){Y.preventDefault()});var S=function(){return r.selection[4]!==0},P=function(k){for(var V=r.findContainerClientCoords(),W=V[0],$=V[1],K=V[2],se=V[3],ee=k.touches?k.touches:[k],fe=!1,le=0;le<ee.length;le++){var ge=ee[le];if(W<=ge.clientX&&ge.clientX<=W+K&&$<=ge.clientY&&ge.clientY<=$+se){fe=!0;break}}if(!fe)return!1;for(var Te=r.container,Ee=k.target,ce=Ee.parentNode,De=!1;ce;){if(ce===Te){De=!0;break}ce=ce.parentNode}return!!De};r.registerBinding(r.container,"mousedown",function(k){if(P(k)&&!(r.hoverData.which===1&&k.which!==1)){k.preventDefault(),b(),r.hoverData.capture=!0,r.hoverData.which=k.which;var V=r.cy,W=[k.clientX,k.clientY],$=r.projectIntoViewport(W[0],W[1]),K=r.selection,se=r.findNearestElements($[0],$[1],!0,!1),ee=se[0],fe=r.dragData.possibleDragElements;r.hoverData.mdownPos=$,r.hoverData.mdownGPos=W;var le=function(Se){return{originalEvent:k,type:Se,position:{x:$[0],y:$[1]}}},ge=function(){r.hoverData.tapholdCancelled=!1,clearTimeout(r.hoverData.tapholdTimeout),r.hoverData.tapholdTimeout=setTimeout(function(){if(!r.hoverData.tapholdCancelled){var Se=r.hoverData.down;Se?Se.emit(le("taphold")):V.emit(le("taphold"))}},r.tapholdDuration)};if(k.which==3){r.hoverData.cxtStarted=!0;var Te={originalEvent:k,type:"cxttapstart",position:{x:$[0],y:$[1]}};ee?(ee.activate(),ee.emit(Te),r.hoverData.down=ee):V.emit(Te),r.hoverData.downTime=new Date().getTime(),r.hoverData.cxtDragged=!1}else if(k.which==1){ee&&ee.activate();{if(ee!=null&&r.nodeIsGrabbable(ee)){var Ee=function(Se){Se.emit(le("grab"))};if(f(ee),!ee.selected())fe=r.dragData.possibleDragElements=V.collection(),g(ee,{addToList:fe}),ee.emit(le("grabon")).emit(le("grab"));else{fe=r.dragData.possibleDragElements=V.collection();var ce=V.$(function(De){return De.isNode()&&De.selected()&&r.nodeIsGrabbable(De)});m(ce,{addToList:fe}),ee.emit(le("grabon")),ce.forEach(Ee)}r.redrawHint("eles",!0),r.redrawHint("drag",!0)}r.hoverData.down=ee,r.hoverData.downs=se,r.hoverData.downTime=new Date().getTime()}n(ee,["mousedown","tapstart","vmousedown"],k,{x:$[0],y:$[1]}),ee==null?(K[4]=1,r.data.bgActivePosistion={x:$[0],y:$[1]},r.redrawHint("select",!0),r.redraw()):ee.pannable()&&(K[4]=1),ge()}K[0]=K[2]=$[0],K[1]=K[3]=$[1]}},!1);var D=a(r.container);r.registerBinding([e,D],"mousemove",function(k){var V=r.hoverData.capture;if(!(!V&&!P(k))){var W=!1,$=r.cy,K=$.zoom(),se=[k.clientX,k.clientY],ee=r.projectIntoViewport(se[0],se[1]),fe=r.hoverData.mdownPos,le=r.hoverData.mdownGPos,ge=r.selection,Te=null;!r.hoverData.draggingEles&&!r.hoverData.dragging&&!r.hoverData.selecting&&(Te=r.findNearestElement(ee[0],ee[1],!0,!1));var Ee=r.hoverData.last,ce=r.hoverData.down,De=[ee[0]-ge[2],ee[1]-ge[3]],Se=r.dragData.possibleDragElements,Je;if(le){var Ue=se[0]-le[0],mr=Ue*Ue,Ye=se[1]-le[1],ir=Ye*Ye,je=mr+ir;r.hoverData.isOverThresholdDrag=Je=je>=r.desktopTapThreshold2}var lr=i(k);Je&&(r.hoverData.tapholdCancelled=!0);var jr=function(){var Br=r.hoverData.dragDelta=r.hoverData.dragDelta||[];Br.length===0?(Br.push(De[0]),Br.push(De[1])):(Br[0]+=De[0],Br[1]+=De[1])};W=!0,n(Te,["mousemove","vmousemove","tapdrag"],k,{x:ee[0],y:ee[1]});var Ze=function(Br){return{originalEvent:k,type:Br,position:{x:ee[0],y:ee[1]}}},Wr=function(){r.data.bgActivePosistion=void 0,r.hoverData.selecting||$.emit(Ze("boxstart")),ge[4]=1,r.hoverData.selecting=!0,r.redrawHint("select",!0),r.redraw()};if(r.hoverData.which===3){if(Je){var $r=Ze("cxtdrag");ce?ce.emit($r):$.emit($r),r.hoverData.cxtDragged=!0,(!r.hoverData.cxtOver||Te!==r.hoverData.cxtOver)&&(r.hoverData.cxtOver&&r.hoverData.cxtOver.emit(Ze("cxtdragout")),r.hoverData.cxtOver=Te,Te&&Te.emit(Ze("cxtdragover")))}}else if(r.hoverData.dragging){if(W=!0,$.panningEnabled()&&$.userPanningEnabled()){var Lt;if(r.hoverData.justStartedPan){var $a=r.hoverData.mdownPos;Lt={x:(ee[0]-$a[0])*K,y:(ee[1]-$a[1])*K},r.hoverData.justStartedPan=!1}else Lt={x:De[0]*K,y:De[1]*K};$.panBy(Lt),$.emit(Ze("dragpan")),r.hoverData.dragged=!0}ee=r.projectIntoViewport(k.clientX,k.clientY)}else if(ge[4]==1&&(ce==null||ce.pannable())){if(Je){if(!r.hoverData.dragging&&$.boxSelectionEnabled()&&(lr||!$.panningEnabled()||!$.userPanningEnabled()))Wr();else if(!r.hoverData.selecting&&$.panningEnabled()&&$.userPanningEnabled()){var mt=s(ce,r.hoverData.downs);mt&&(r.hoverData.dragging=!0,r.hoverData.justStartedPan=!0,ge[4]=0,r.data.bgActivePosistion=Ht(fe),r.redrawHint("select",!0),r.redraw())}ce&&ce.pannable()&&ce.active()&&ce.unactivate()}}else{if(ce&&ce.pannable()&&ce.active()&&ce.unactivate(),(!ce||!ce.grabbed())&&Te!=Ee&&(Ee&&n(Ee,["mouseout","tapdragout"],k,{x:ee[0],y:ee[1]}),Te&&n(Te,["mouseover","tapdragover"],k,{x:ee[0],y:ee[1]}),r.hoverData.last=Te),ce)if(Je){if($.boxSelectionEnabled()&&lr)ce&&ce.grabbed()&&(p(Se),ce.emit(Ze("freeon")),Se.emit(Ze("free")),r.dragData.didDrag&&(ce.emit(Ze("dragfreeon")),Se.emit(Ze("dragfree")))),Wr();else if(ce&&ce.grabbed()&&r.nodeIsDraggable(ce)){var Er=!r.dragData.didDrag;Er&&r.redrawHint("eles",!0),r.dragData.didDrag=!0,r.hoverData.draggingEles||m(Se,{inDragLayer:!0});var hr={x:0,y:0};if(te(De[0])&&te(De[1])&&(hr.x+=De[0],hr.y+=De[1],Er)){var Cr=r.hoverData.dragDelta;Cr&&te(Cr[0])&&te(Cr[1])&&(hr.x+=Cr[0],hr.y+=Cr[1])}r.hoverData.draggingEles=!0,Se.silentShift(hr).emit(Ze("position")).emit(Ze("drag")),r.redrawHint("drag",!0),r.redraw()}}else jr();W=!0}if(ge[2]=ee[0],ge[3]=ee[1],W)return k.stopPropagation&&k.stopPropagation(),k.preventDefault&&k.preventDefault(),!1}},!1);var A,B,R;r.registerBinding(e,"mouseup",function(k){if(!(r.hoverData.which===1&&k.which!==1&&r.hoverData.capture)){var V=r.hoverData.capture;if(V){r.hoverData.capture=!1;var W=r.cy,$=r.projectIntoViewport(k.clientX,k.clientY),K=r.selection,se=r.findNearestElement($[0],$[1],!0,!1),ee=r.dragData.possibleDragElements,fe=r.hoverData.down,le=i(k);r.data.bgActivePosistion&&(r.redrawHint("select",!0),r.redraw()),r.hoverData.tapholdCancelled=!0,r.data.bgActivePosistion=void 0,fe&&fe.unactivate();var ge=function(Ue){return{originalEvent:k,type:Ue,position:{x:$[0],y:$[1]}}};if(r.hoverData.which===3){var Te=ge("cxttapend");if(fe?fe.emit(Te):W.emit(Te),!r.hoverData.cxtDragged){var Ee=ge("cxttap");fe?fe.emit(Ee):W.emit(Ee)}r.hoverData.cxtDragged=!1,r.hoverData.which=null}else if(r.hoverData.which===1){if(n(se,["mouseup","tapend","vmouseup"],k,{x:$[0],y:$[1]}),!r.dragData.didDrag&&!r.hoverData.dragged&&!r.hoverData.selecting&&!r.hoverData.isOverThresholdDrag&&(n(fe,["click","tap","vclick"],k,{x:$[0],y:$[1]}),B=!1,k.timeStamp-R<=W.multiClickDebounceTime()?(A&&clearTimeout(A),B=!0,R=null,n(fe,["dblclick","dbltap","vdblclick"],k,{x:$[0],y:$[1]})):(A=setTimeout(function(){B||n(fe,["oneclick","onetap","voneclick"],k,{x:$[0],y:$[1]})},W.multiClickDebounceTime()),R=k.timeStamp)),fe==null&&!r.dragData.didDrag&&!r.hoverData.selecting&&!r.hoverData.dragged&&!i(k)&&(W.$(t).unselect(["tapunselect"]),ee.length>0&&r.redrawHint("eles",!0),r.dragData.possibleDragElements=ee=W.collection()),se==fe&&!r.dragData.didDrag&&!r.hoverData.selecting&&se!=null&&se._private.selectable&&(r.hoverData.dragging||(W.selectionType()==="additive"||le?se.selected()?se.unselect(["tapunselect"]):se.select(["tapselect"]):le||(W.$(t).unmerge(se).unselect(["tapunselect"]),se.select(["tapselect"]))),r.redrawHint("eles",!0)),r.hoverData.selecting){var ce=W.collection(r.getAllInBox(K[0],K[1],K[2],K[3]));r.redrawHint("select",!0),ce.length>0&&r.redrawHint("eles",!0),W.emit(ge("boxend"));var De=function(Ue){return Ue.selectable()&&!Ue.selected()};W.selectionType()==="additive"||le||W.$(t).unmerge(ce).unselect(),ce.emit(ge("box")).stdFilter(De).select().emit(ge("boxselect")),r.redraw()}if(r.hoverData.dragging&&(r.hoverData.dragging=!1,r.redrawHint("select",!0),r.redrawHint("eles",!0),r.redraw()),!K[4]){r.redrawHint("drag",!0),r.redrawHint("eles",!0);var Se=fe&&fe.grabbed();p(ee),Se&&(fe.emit(ge("freeon")),ee.emit(ge("free")),r.dragData.didDrag&&(fe.emit(ge("dragfreeon")),ee.emit(ge("dragfree"))))}}K[4]=0,r.hoverData.down=null,r.hoverData.cxtStarted=!1,r.hoverData.draggingEles=!1,r.hoverData.selecting=!1,r.hoverData.isOverThresholdDrag=!1,r.dragData.didDrag=!1,r.hoverData.dragged=!1,r.hoverData.dragDelta=[],r.hoverData.mdownPos=null,r.hoverData.mdownGPos=null,r.hoverData.which=null}}},!1);var M=[],L=4,I,O=1e5,F=function(k,V){for(var W=0;W<k.length;W++)if(k[W]%V!==0)return!1;return!0},_=function(k){for(var V=Math.abs(k[0]),W=1;W<k.length;W++)if(Math.abs(k[W])!==V)return!1;return!0},N=function(k){var V=!1,W=k.deltaY;if(W==null&&(k.wheelDeltaY!=null?W=k.wheelDeltaY/4:k.wheelDelta!=null&&(W=k.wheelDelta/4)),I==null)if(M.length>=L){var $=M;if(I=F($,5),!I){var K=Math.abs($[0]);I=_($)&&K>5}if(I)for(var se=0;se<$.length;se++)O=Math.min(Math.abs($[se]),O)}else M.push(W),V=!0;else I&&(O=Math.min(Math.abs(W),O));if(!r.scrollingPage){var ee=r.cy,fe=ee.zoom(),le=ee.pan(),ge=r.projectIntoViewport(k.clientX,k.clientY),Te=[ge[0]*fe+le.x,ge[1]*fe+le.y];if(r.hoverData.draggingEles||r.hoverData.dragging||r.hoverData.cxtStarted||S()){k.preventDefault();return}if(ee.panningEnabled()&&ee.userPanningEnabled()&&ee.zoomingEnabled()&&ee.userZoomingEnabled()){k.preventDefault(),r.data.wheelZooming=!0,clearTimeout(r.data.wheelTimeout),r.data.wheelTimeout=setTimeout(function(){r.data.wheelZooming=!1,r.redrawHint("eles",!0),r.redraw()},150);var Ee;V&&Math.abs(W)>5&&(W=to(W)*5),Ee=W/-250,I&&(Ee/=O,Ee*=3),Ee=Ee*r.wheelSensitivity;var ce=k.deltaMode===1;ce&&(Ee*=33);var De=ee.zoom()*Math.pow(10,Ee);k.type==="gesturechange"&&(De=r.gestureStartZoom*k.scale),ee.zoom({level:De,renderedPosition:{x:Te[0],y:Te[1]}}),ee.emit({type:k.type==="gesturechange"?"pinchzoom":"scrollzoom",originalEvent:k,position:{x:ge[0],y:ge[1]}})}}};r.registerBinding(r.container,"wheel",N,!0),r.registerBinding(e,"scroll",function(k){r.scrollingPage=!0,clearTimeout(r.scrollingPageTimeout),r.scrollingPageTimeout=setTimeout(function(){r.scrollingPage=!1},250)},!0),r.registerBinding(r.container,"gesturestart",function(k){r.gestureStartZoom=r.cy.zoom(),r.hasTouchStarted||k.preventDefault()},!0),r.registerBinding(r.container,"gesturechange",function(Y){r.hasTouchStarted||N(Y)},!0),r.registerBinding(r.container,"mouseout",function(k){var V=r.projectIntoViewport(k.clientX,k.clientY);r.cy.emit({originalEvent:k,type:"mouseout",position:{x:V[0],y:V[1]}})},!1),r.registerBinding(r.container,"mouseover",function(k){var V=r.projectIntoViewport(k.clientX,k.clientY);r.cy.emit({originalEvent:k,type:"mouseover",position:{x:V[0],y:V[1]}})},!1);var q,U,X,j,J,re,ae,Z,z,G,H,Q,ne,be=function(k,V,W,$){return Math.sqrt((W-k)*(W-k)+($-V)*($-V))},Fe=function(k,V,W,$){return(W-k)*(W-k)+($-V)*($-V)},Be;r.registerBinding(r.container,"touchstart",Be=function(k){if(r.hasTouchStarted=!0,!!P(k)){b(),r.touchData.capture=!0,r.data.bgActivePosistion=void 0;var V=r.cy,W=r.touchData.now,$=r.touchData.earlier;if(k.touches[0]){var K=r.projectIntoViewport(k.touches[0].clientX,k.touches[0].clientY);W[0]=K[0],W[1]=K[1]}if(k.touches[1]){var K=r.projectIntoViewport(k.touches[1].clientX,k.touches[1].clientY);W[2]=K[0],W[3]=K[1]}if(k.touches[2]){var K=r.projectIntoViewport(k.touches[2].clientX,k.touches[2].clientY);W[4]=K[0],W[5]=K[1]}var se=function(lr){return{originalEvent:k,type:lr,position:{x:W[0],y:W[1]}}};if(k.touches[1]){r.touchData.singleTouchMoved=!0,p(r.dragData.touchDragEles);var ee=r.findContainerClientCoords();z=ee[0],G=ee[1],H=ee[2],Q=ee[3],q=k.touches[0].clientX-z,U=k.touches[0].clientY-G,X=k.touches[1].clientX-z,j=k.touches[1].clientY-G,ne=0<=q&&q<=H&&0<=X&&X<=H&&0<=U&&U<=Q&&0<=j&&j<=Q;var fe=V.pan(),le=V.zoom();J=be(q,U,X,j),re=Fe(q,U,X,j),ae=[(q+X)/2,(U+j)/2],Z=[(ae[0]-fe.x)/le,(ae[1]-fe.y)/le];var ge=200,Te=ge*ge;if(re<Te&&!k.touches[2]){var Ee=r.findNearestElement(W[0],W[1],!0,!0),ce=r.findNearestElement(W[2],W[3],!0,!0);Ee&&Ee.isNode()?(Ee.activate().emit(se("cxttapstart")),r.touchData.start=Ee):ce&&ce.isNode()?(ce.activate().emit(se("cxttapstart")),r.touchData.start=ce):V.emit(se("cxttapstart")),r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxt=!0,r.touchData.cxtDragged=!1,r.data.bgActivePosistion=void 0,r.redraw();return}}if(k.touches[2])V.boxSelectionEnabled()&&k.preventDefault();else if(!k.touches[1]){if(k.touches[0]){var De=r.findNearestElements(W[0],W[1],!0,!0),Se=De[0];if(Se!=null&&(Se.activate(),r.touchData.start=Se,r.touchData.starts=De,r.nodeIsGrabbable(Se))){var Je=r.dragData.touchDragEles=V.collection(),Ue=null;r.redrawHint("eles",!0),r.redrawHint("drag",!0),Se.selected()?(Ue=V.$(function(je){return je.selected()&&r.nodeIsGrabbable(je)}),m(Ue,{addToList:Je})):g(Se,{addToList:Je}),f(Se),Se.emit(se("grabon")),Ue?Ue.forEach(function(je){je.emit(se("grab"))}):Se.emit(se("grab"))}n(Se,["touchstart","tapstart","vmousedown"],k,{x:W[0],y:W[1]}),Se==null&&(r.data.bgActivePosistion={x:K[0],y:K[1]},r.redrawHint("select",!0),r.redraw()),r.touchData.singleTouchMoved=!1,r.touchData.singleTouchStartTime=+new Date,clearTimeout(r.touchData.tapholdTimeout),r.touchData.tapholdTimeout=setTimeout(function(){r.touchData.singleTouchMoved===!1&&!r.pinching&&!r.touchData.selecting&&n(r.touchData.start,["taphold"],k,{x:W[0],y:W[1]})},r.tapholdDuration)}}if(k.touches.length>=1){for(var mr=r.touchData.startPosition=[null,null,null,null,null,null],Ye=0;Ye<W.length;Ye++)mr[Ye]=$[Ye]=W[Ye];var ir=k.touches[0];r.touchData.startGPosition=[ir.clientX,ir.clientY]}}},!1);var oe;r.registerBinding(e,"touchmove",oe=function(k){var V=r.touchData.capture;if(!(!V&&!P(k))){var W=r.selection,$=r.cy,K=r.touchData.now,se=r.touchData.earlier,ee=$.zoom();if(k.touches[0]){var fe=r.projectIntoViewport(k.touches[0].clientX,k.touches[0].clientY);K[0]=fe[0],K[1]=fe[1]}if(k.touches[1]){var fe=r.projectIntoViewport(k.touches[1].clientX,k.touches[1].clientY);K[2]=fe[0],K[3]=fe[1]}if(k.touches[2]){var fe=r.projectIntoViewport(k.touches[2].clientX,k.touches[2].clientY);K[4]=fe[0],K[5]=fe[1]}var le=function(Zf){return{originalEvent:k,type:Zf,position:{x:K[0],y:K[1]}}},ge=r.touchData.startGPosition,Te;if(V&&k.touches[0]&&ge){for(var Ee=[],ce=0;ce<K.length;ce++)Ee[ce]=K[ce]-se[ce];var De=k.touches[0].clientX-ge[0],Se=De*De,Je=k.touches[0].clientY-ge[1],Ue=Je*Je,mr=Se+Ue;Te=mr>=r.touchTapThreshold2}if(V&&r.touchData.cxt){k.preventDefault();var Ye=k.touches[0].clientX-z,ir=k.touches[0].clientY-G,je=k.touches[1].clientX-z,lr=k.touches[1].clientY-G,jr=Fe(Ye,ir,je,lr),Ze=jr/re,Wr=150,$r=Wr*Wr,Lt=1.5,$a=Lt*Lt;if(Ze>=$a||jr>=$r){r.touchData.cxt=!1,r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var mt=le("cxttapend");r.touchData.start?(r.touchData.start.unactivate().emit(mt),r.touchData.start=null):$.emit(mt)}}if(V&&r.touchData.cxt){var mt=le("cxtdrag");r.data.bgActivePosistion=void 0,r.redrawHint("select",!0),r.touchData.start?r.touchData.start.emit(mt):$.emit(mt),r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxtDragged=!0;var Er=r.findNearestElement(K[0],K[1],!0,!0);(!r.touchData.cxtOver||Er!==r.touchData.cxtOver)&&(r.touchData.cxtOver&&r.touchData.cxtOver.emit(le("cxtdragout")),r.touchData.cxtOver=Er,Er&&Er.emit(le("cxtdragover")))}else if(V&&k.touches[2]&&$.boxSelectionEnabled())k.preventDefault(),r.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,r.touchData.selecting||$.emit(le("boxstart")),r.touchData.selecting=!0,r.touchData.didSelect=!0,W[4]=1,!W||W.length===0||W[0]===void 0?(W[0]=(K[0]+K[2]+K[4])/3,W[1]=(K[1]+K[3]+K[5])/3,W[2]=(K[0]+K[2]+K[4])/3+1,W[3]=(K[1]+K[3]+K[5])/3+1):(W[2]=(K[0]+K[2]+K[4])/3,W[3]=(K[1]+K[3]+K[5])/3),r.redrawHint("select",!0),r.redraw();else if(V&&k.touches[1]&&!r.touchData.didSelect&&$.zoomingEnabled()&&$.panningEnabled()&&$.userZoomingEnabled()&&$.userPanningEnabled()){k.preventDefault(),r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var hr=r.dragData.touchDragEles;if(hr){r.redrawHint("drag",!0);for(var Cr=0;Cr<hr.length;Cr++){var oa=hr[Cr]._private;oa.grabbed=!1,oa.rscratch.inDragLayer=!1}}var Br=r.touchData.start,Ye=k.touches[0].clientX-z,ir=k.touches[0].clientY-G,je=k.touches[1].clientX-z,lr=k.touches[1].clientY-G,wo=be(Ye,ir,je,lr),_f=wo/J;if(ne){var Gf=Ye-q,Hf=ir-U,Wf=je-X,$f=lr-j,Uf=(Gf+Wf)/2,Kf=(Hf+$f)/2,ua=$.zoom(),Zn=ua*_f,Ua=$.pan(),xo=Z[0]*ua+Ua.x,Eo=Z[1]*ua+Ua.y,Xf={x:-Zn/ua*(xo-Ua.x-Uf)+xo,y:-Zn/ua*(Eo-Ua.y-Kf)+Eo};if(Br&&Br.active()){var hr=r.dragData.touchDragEles;p(hr),r.redrawHint("drag",!0),r.redrawHint("eles",!0),Br.unactivate().emit(le("freeon")),hr.emit(le("free")),r.dragData.didDrag&&(Br.emit(le("dragfreeon")),hr.emit(le("dragfree")))}$.viewport({zoom:Zn,pan:Xf,cancelOnFailedZoom:!0}),$.emit(le("pinchzoom")),J=wo,q=Ye,U=ir,X=je,j=lr,r.pinching=!0}if(k.touches[0]){var fe=r.projectIntoViewport(k.touches[0].clientX,k.touches[0].clientY);K[0]=fe[0],K[1]=fe[1]}if(k.touches[1]){var fe=r.projectIntoViewport(k.touches[1].clientX,k.touches[1].clientY);K[2]=fe[0],K[3]=fe[1]}if(k.touches[2]){var fe=r.projectIntoViewport(k.touches[2].clientX,k.touches[2].clientY);K[4]=fe[0],K[5]=fe[1]}}else if(k.touches[0]&&!r.touchData.didSelect){var Mr=r.touchData.start,Qn=r.touchData.last,Er;if(!r.hoverData.draggingEles&&!r.swipePanning&&(Er=r.findNearestElement(K[0],K[1],!0,!0)),V&&Mr!=null&&k.preventDefault(),V&&Mr!=null&&r.nodeIsDraggable(Mr))if(Te){var hr=r.dragData.touchDragEles,Co=!r.dragData.didDrag;Co&&m(hr,{inDragLayer:!0}),r.dragData.didDrag=!0;var la={x:0,y:0};if(te(Ee[0])&&te(Ee[1])&&(la.x+=Ee[0],la.y+=Ee[1],Co)){r.redrawHint("eles",!0);var Lr=r.touchData.dragDelta;Lr&&te(Lr[0])&&te(Lr[1])&&(la.x+=Lr[0],la.y+=Lr[1])}r.hoverData.draggingEles=!0,hr.silentShift(la).emit(le("position")).emit(le("drag")),r.redrawHint("drag",!0),r.touchData.startPosition[0]==se[0]&&r.touchData.startPosition[1]==se[1]&&r.redrawHint("eles",!0),r.redraw()}else{var Lr=r.touchData.dragDelta=r.touchData.dragDelta||[];Lr.length===0?(Lr.push(Ee[0]),Lr.push(Ee[1])):(Lr[0]+=Ee[0],Lr[1]+=Ee[1])}if(n(Mr||Er,["touchmove","tapdrag","vmousemove"],k,{x:K[0],y:K[1]}),(!Mr||!Mr.grabbed())&&Er!=Qn&&(Qn&&Qn.emit(le("tapdragout")),Er&&Er.emit(le("tapdragover"))),r.touchData.last=Er,V)for(var Cr=0;Cr<K.length;Cr++)K[Cr]&&r.touchData.startPosition[Cr]&&Te&&(r.touchData.singleTouchMoved=!0);if(V&&(Mr==null||Mr.pannable())&&$.panningEnabled()&&$.userPanningEnabled()){var Yf=s(Mr,r.touchData.starts);Yf&&(k.preventDefault(),r.data.bgActivePosistion||(r.data.bgActivePosistion=Ht(r.touchData.startPosition)),r.swipePanning?($.panBy({x:Ee[0]*ee,y:Ee[1]*ee}),$.emit(le("dragpan"))):Te&&(r.swipePanning=!0,$.panBy({x:De*ee,y:Je*ee}),$.emit(le("dragpan")),Mr&&(Mr.unactivate(),r.redrawHint("select",!0),r.touchData.start=null)));var fe=r.projectIntoViewport(k.touches[0].clientX,k.touches[0].clientY);K[0]=fe[0],K[1]=fe[1]}}for(var ce=0;ce<K.length;ce++)se[ce]=K[ce];V&&k.touches.length>0&&!r.hoverData.draggingEles&&!r.swipePanning&&r.data.bgActivePosistion!=null&&(r.data.bgActivePosistion=void 0,r.redrawHint("select",!0),r.redraw())}},!1);var ve;r.registerBinding(e,"touchcancel",ve=function(k){var V=r.touchData.start;r.touchData.capture=!1,V&&V.unactivate()});var he,ye,me,we;if(r.registerBinding(e,"touchend",he=function(k){var V=r.touchData.start,W=r.touchData.capture;if(W)k.touches.length===0&&(r.touchData.capture=!1),k.preventDefault();else return;var $=r.selection;r.swipePanning=!1,r.hoverData.draggingEles=!1;var K=r.cy,se=K.zoom(),ee=r.touchData.now,fe=r.touchData.earlier;if(k.touches[0]){var le=r.projectIntoViewport(k.touches[0].clientX,k.touches[0].clientY);ee[0]=le[0],ee[1]=le[1]}if(k.touches[1]){var le=r.projectIntoViewport(k.touches[1].clientX,k.touches[1].clientY);ee[2]=le[0],ee[3]=le[1]}if(k.touches[2]){var le=r.projectIntoViewport(k.touches[2].clientX,k.touches[2].clientY);ee[4]=le[0],ee[5]=le[1]}var ge=function($r){return{originalEvent:k,type:$r,position:{x:ee[0],y:ee[1]}}};V&&V.unactivate();var Te;if(r.touchData.cxt){if(Te=ge("cxttapend"),V?V.emit(Te):K.emit(Te),!r.touchData.cxtDragged){var Ee=ge("cxttap");V?V.emit(Ee):K.emit(Ee)}r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxt=!1,r.touchData.start=null,r.redraw();return}if(!k.touches[2]&&K.boxSelectionEnabled()&&r.touchData.selecting){r.touchData.selecting=!1;var ce=K.collection(r.getAllInBox($[0],$[1],$[2],$[3]));$[0]=void 0,$[1]=void 0,$[2]=void 0,$[3]=void 0,$[4]=0,r.redrawHint("select",!0),K.emit(ge("boxend"));var De=function($r){return $r.selectable()&&!$r.selected()};ce.emit(ge("box")).stdFilter(De).select().emit(ge("boxselect")),ce.nonempty()&&r.redrawHint("eles",!0),r.redraw()}if(V!=null&&V.unactivate(),k.touches[2])r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);else if(!k.touches[1]){if(!k.touches[0]){if(!k.touches[0]){r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var Se=r.dragData.touchDragEles;if(V!=null){var Je=V._private.grabbed;p(Se),r.redrawHint("drag",!0),r.redrawHint("eles",!0),Je&&(V.emit(ge("freeon")),Se.emit(ge("free")),r.dragData.didDrag&&(V.emit(ge("dragfreeon")),Se.emit(ge("dragfree")))),n(V,["touchend","tapend","vmouseup","tapdragout"],k,{x:ee[0],y:ee[1]}),V.unactivate(),r.touchData.start=null}else{var Ue=r.findNearestElement(ee[0],ee[1],!0,!0);n(Ue,["touchend","tapend","vmouseup","tapdragout"],k,{x:ee[0],y:ee[1]})}var mr=r.touchData.startPosition[0]-ee[0],Ye=mr*mr,ir=r.touchData.startPosition[1]-ee[1],je=ir*ir,lr=Ye+je,jr=lr*se*se;r.touchData.singleTouchMoved||(V||K.$(":selected").unselect(["tapunselect"]),n(V,["tap","vclick"],k,{x:ee[0],y:ee[1]}),ye=!1,k.timeStamp-we<=K.multiClickDebounceTime()?(me&&clearTimeout(me),ye=!0,we=null,n(V,["dbltap","vdblclick"],k,{x:ee[0],y:ee[1]})):(me=setTimeout(function(){ye||n(V,["onetap","voneclick"],k,{x:ee[0],y:ee[1]})},K.multiClickDebounceTime()),we=k.timeStamp)),V!=null&&!r.dragData.didDrag&&V._private.selectable&&jr<r.touchTapThreshold2&&!r.pinching&&(K.selectionType()==="single"?(K.$(t).unmerge(V).unselect(["tapunselect"]),V.select(["tapselect"])):V.selected()?V.unselect(["tapunselect"]):V.select(["tapselect"]),r.redrawHint("eles",!0)),r.touchData.singleTouchMoved=!0}}}for(var Ze=0;Ze<ee.length;Ze++)fe[Ze]=ee[Ze];r.dragData.didDrag=!1,k.touches.length===0&&(r.touchData.dragDelta=[],r.touchData.startPosition=[null,null,null,null,null,null],r.touchData.startGPosition=null,r.touchData.didSelect=!1),k.touches.length<2&&(k.touches.length===1&&(r.touchData.startGPosition=[k.touches[0].clientX,k.touches[0].clientY]),r.pinching=!1,r.redrawHint("eles",!0),r.redraw())},!1),typeof TouchEvent>"u"){var xe=[],Pe=function(k){return{clientX:k.clientX,clientY:k.clientY,force:1,identifier:k.pointerId,pageX:k.pageX,pageY:k.pageY,radiusX:k.width/2,radiusY:k.height/2,screenX:k.screenX,screenY:k.screenY,target:k.target}},Ve=function(k){return{event:k,touch:Pe(k)}},Xe=function(k){xe.push(Ve(k))},Oe=function(k){for(var V=0;V<xe.length;V++){var W=xe[V];if(W.event.pointerId===k.pointerId){xe.splice(V,1);return}}},He=function(k){var V=xe.filter(function(W){return W.event.pointerId===k.pointerId})[0];V.event=k,V.touch=Pe(k)},ke=function(k){k.touches=xe.map(function(V){return V.touch})},ue=function(k){return k.pointerType==="mouse"||k.pointerType===4};r.registerBinding(r.container,"pointerdown",function(Y){ue(Y)||(Y.preventDefault(),Xe(Y),ke(Y),Be(Y))}),r.registerBinding(r.container,"pointerup",function(Y){ue(Y)||(Oe(Y),ke(Y),he(Y))}),r.registerBinding(r.container,"pointercancel",function(Y){ue(Y)||(Oe(Y),ke(Y),ve(Y))}),r.registerBinding(r.container,"pointermove",function(Y){ue(Y)||(Y.preventDefault(),He(Y),ke(Y),oe(Y))})}};var Qr={};Qr.generatePolygon=function(r,e){return this.nodeShapes[r]={renderer:this,name:r,points:e,draw:function(a,n,i,s,o,l){this.renderer.nodeShapeImpl("polygon",a,n,i,s,o,this.points)},intersectLine:function(a,n,i,s,o,l,u,v){return Da(o,l,this.points,a,n,i/2,s/2,u)},checkPoint:function(a,n,i,s,o,l,u,v){return Zr(a,n,this.points,l,u,s,o,[0,-1],i)},hasMiterBounds:r!=="rectangle",miterBounds:function(a,n,i,s,o,l){return kd(this.points,a,n,i,s,o)}}};Qr.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i)},intersectLine:function(e,t,a,n,i,s,o,l){return Id(i,s,e,t,a/2+o,n/2+o)},checkPoint:function(e,t,a,n,i,s,o,l){return St(e,t,n,i,s,o,a)}}};Qr.generateRoundPolygon=function(r,e){return this.nodeShapes[r]={renderer:this,name:r,points:e,getOrCreateCorners:function(a,n,i,s,o,l,u){if(l[u]!==void 0&&l[u+"-cx"]===a&&l[u+"-cy"]===n)return l[u];l[u]=new Array(e.length/2),l[u+"-cx"]=a,l[u+"-cy"]=n;var v=i/2,f=s/2;o=o==="auto"?bv(i,s):o;for(var c=new Array(e.length/2),h=0;h<e.length/2;h++)c[h]={x:a+v*e[h*2],y:n+f*e[h*2+1]};var d,m,g,p,y=c.length;for(m=c[y-1],d=0;d<y;d++)g=c[d%y],p=c[(d+1)%y],l[u][d]=po(m,g,p,o),m=g,g=p;return l[u]},draw:function(a,n,i,s,o,l,u){this.renderer.nodeShapeImpl("round-polygon",a,n,i,s,o,this.points,this.getOrCreateCorners(n,i,s,o,l,u,"drawCorners"))},intersectLine:function(a,n,i,s,o,l,u,v,f){return Nd(o,l,this.points,a,n,i,s,u,this.getOrCreateCorners(a,n,i,s,v,f,"corners"))},checkPoint:function(a,n,i,s,o,l,u,v,f){return Ld(a,n,this.points,l,u,s,o,this.getOrCreateCorners(l,u,s,o,v,f,"corners"))}}};Qr.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:br(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,this.points,s)},intersectLine:function(e,t,a,n,i,s,o,l){return yv(i,s,e,t,a,n,o,l)},checkPoint:function(e,t,a,n,i,s,o,l){var u=n/2,v=i/2;l=l==="auto"?lt(n,i):l,l=Math.min(u,v,l);var f=l*2;return!!(Zr(e,t,this.points,s,o,n,i-f,[0,-1],a)||Zr(e,t,this.points,s,o,n-f,i,[0,-1],a)||St(e,t,f,f,s-u+l,o-v+l,a)||St(e,t,f,f,s+u-l,o-v+l,a)||St(e,t,f,f,s+u-l,o+v-l,a)||St(e,t,f,f,s-u+l,o+v-l,a))}}};Qr.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:no(),points:br(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,null,s)},generateCutTrianglePts:function(e,t,a,n,i){var s=i==="auto"?this.cornerLength:i,o=t/2,l=e/2,u=a-l,v=a+l,f=n-o,c=n+o;return{topLeft:[u,f+s,u+s,f,u+s,f+s],topRight:[v-s,f,v,f+s,v-s,f+s],bottomRight:[v,c-s,v-s,c,v-s,c-s],bottomLeft:[u+s,c,u,c-s,u+s,c-s]}},intersectLine:function(e,t,a,n,i,s,o,l){var u=this.generateCutTrianglePts(a+2*o,n+2*o,e,t,l),v=[].concat.apply([],[u.topLeft.splice(0,4),u.topRight.splice(0,4),u.bottomRight.splice(0,4),u.bottomLeft.splice(0,4)]);return Da(i,s,v,e,t)},checkPoint:function(e,t,a,n,i,s,o,l){var u=l==="auto"?this.cornerLength:l;if(Zr(e,t,this.points,s,o,n,i-2*u,[0,-1],a)||Zr(e,t,this.points,s,o,n-2*u,i,[0,-1],a))return!0;var v=this.generateCutTrianglePts(n,i,s,o);return Sr(e,t,v.topLeft)||Sr(e,t,v.topRight)||Sr(e,t,v.bottomRight)||Sr(e,t,v.bottomLeft)}}};Qr.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:br(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i)},intersectLine:function(e,t,a,n,i,s,o,l){var u=.15,v=.5,f=.85,c=this.generateBarrelBezierPts(a+2*o,n+2*o,e,t),h=function(g){var p=Ut({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},u),y=Ut({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},v),b=Ut({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},f);return[g[0],g[1],p.x,p.y,y.x,y.y,b.x,b.y,g[4],g[5]]},d=[].concat(h(c.topLeft),h(c.topRight),h(c.bottomRight),h(c.bottomLeft));return Da(i,s,d,e,t)},generateBarrelBezierPts:function(e,t,a,n){var i=t/2,s=e/2,o=a-s,l=a+s,u=n-i,v=n+i,f=As(e,t),c=f.heightOffset,h=f.widthOffset,d=f.ctrlPtOffsetPct*e,m={topLeft:[o,u+c,o+d,u,o+h,u],topRight:[l-h,u,l-d,u,l,u+c],bottomRight:[l,v-c,l-d,v,l-h,v],bottomLeft:[o+h,v,o+d,v,o,v-c]};return m.topLeft.isTop=!0,m.topRight.isTop=!0,m.bottomLeft.isBottom=!0,m.bottomRight.isBottom=!0,m},checkPoint:function(e,t,a,n,i,s,o,l){var u=As(n,i),v=u.heightOffset,f=u.widthOffset;if(Zr(e,t,this.points,s,o,n,i-2*v,[0,-1],a)||Zr(e,t,this.points,s,o,n-2*f,i,[0,-1],a))return!0;for(var c=this.generateBarrelBezierPts(n,i,s,o),h=function(T,S,P){var D=P[4],A=P[2],B=P[0],R=P[5],M=P[1],L=Math.min(D,B),I=Math.max(D,B),O=Math.min(R,M),F=Math.max(R,M);if(L<=T&&T<=I&&O<=S&&S<=F){var _=zd(D,A,B),N=Pd(_[0],_[1],_[2],T),q=N.filter(function(U){return 0<=U&&U<=1});if(q.length>0)return q[0]}return null},d=Object.keys(c),m=0;m<d.length;m++){var g=d[m],p=c[g],y=h(e,t,p);if(y!=null){var b=p[5],w=p[3],E=p[1],C=sr(b,w,E,y);if(p.isTop&&C<=t||p.isBottom&&t<=C)return!0}}return!1}}};Qr.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:br(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,this.points,s)},intersectLine:function(e,t,a,n,i,s,o,l){var u=e-(a/2+o),v=t-(n/2+o),f=v,c=e+(a/2+o),h=nt(i,s,e,t,u,v,c,f,!1);return h.length>0?h:yv(i,s,e,t,a,n,o,l)},checkPoint:function(e,t,a,n,i,s,o,l){l=l==="auto"?lt(n,i):l;var u=2*l;if(Zr(e,t,this.points,s,o,n,i-u,[0,-1],a)||Zr(e,t,this.points,s,o,n-u,i,[0,-1],a))return!0;var v=n/2+2*a,f=i/2+2*a,c=[s-v,o-f,s-v,o,s+v,o,s+v,o-f];return!!(Sr(e,t,c)||St(e,t,u,u,s+n/2-l,o+i/2-l,a)||St(e,t,u,u,s-n/2+l,o+i/2-l,a))}}};Qr.registerNodeShapes=function(){var r=this.nodeShapes={},e=this;this.generateEllipse(),this.generatePolygon("triangle",br(3,0)),this.generateRoundPolygon("round-triangle",br(3,0)),this.generatePolygon("rectangle",br(4,0)),r.square=r.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();{var t=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",t),this.generateRoundPolygon("round-diamond",t)}this.generatePolygon("pentagon",br(5,0)),this.generateRoundPolygon("round-pentagon",br(5,0)),this.generatePolygon("hexagon",br(6,0)),this.generateRoundPolygon("round-hexagon",br(6,0)),this.generatePolygon("heptagon",br(7,0)),this.generateRoundPolygon("round-heptagon",br(7,0)),this.generatePolygon("octagon",br(8,0)),this.generateRoundPolygon("round-octagon",br(8,0));var a=new Array(20);{var n=Ps(5,0),i=Ps(5,Math.PI/5),s=.5*(3-Math.sqrt(5));s*=1.57;for(var o=0;o<i.length/2;o++)i[o*2]*=s,i[o*2+1]*=s;for(var o=0;o<20/4;o++)a[o*4]=n[o*2],a[o*4+1]=n[o*2+1],a[o*4+2]=i[o*2],a[o*4+3]=i[o*2+1]}a=mv(a),this.generatePolygon("star",a),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);{var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l),this.generateRoundPolygon("round-tag",l)}r.makePolygon=function(u){var v=u.join("$"),f="polygon-"+v,c;return(c=this[f])?c:e.generatePolygon(f,u)}};var Ha={};Ha.timeToRender=function(){return this.redrawTotalTime/this.redrawCount};Ha.redraw=function(r){r=r||hv();var e=this;e.averageRedrawTime===void 0&&(e.averageRedrawTime=0),e.lastRedrawTime===void 0&&(e.lastRedrawTime=0),e.lastDrawTime===void 0&&(e.lastDrawTime=0),e.requestedFrame=!0,e.renderOptions=r};Ha.beforeRender=function(r,e){if(!this.destroyed){e==null&&We("Priority is not optional for beforeRender");var t=this.beforeRenderCallbacks;t.push({fn:r,priority:e}),t.sort(function(a,n){return n.priority-a.priority})}};var Il=function(e,t,a){for(var n=e.beforeRenderCallbacks,i=0;i<n.length;i++)n[i].fn(t,a)};Ha.startRenderLoop=function(){var r=this,e=r.cy;if(!r.renderLoopStarted){r.renderLoopStarted=!0;var t=function(n){if(!r.destroyed){if(!e.batching())if(r.requestedFrame&&!r.skipFrame){Il(r,!0,n);var i=Yr();r.render(r.renderOptions);var s=r.lastDrawTime=Yr();r.averageRedrawTime===void 0&&(r.averageRedrawTime=s-i),r.redrawCount===void 0&&(r.redrawCount=0),r.redrawCount++,r.redrawTotalTime===void 0&&(r.redrawTotalTime=0);var o=s-i;r.redrawTotalTime+=o,r.lastRedrawTime=o,r.averageRedrawTime=r.averageRedrawTime/2+o/2,r.requestedFrame=!1}else Il(r,!1,n);r.skipFrame=!1,wn(t)}};wn(t)}};var ey=function(e){this.init(e)},wf=ey,sa=wf.prototype;sa.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"];sa.init=function(r){var e=this;e.options=r,e.cy=r.cy;var t=e.container=r.cy.container(),a=e.cy.window();if(a){var n=a.document,i=n.head,s="__________cytoscape_stylesheet",o="__________cytoscape_container",l=n.getElementById(s)!=null;if(t.className.indexOf(o)<0&&(t.className=(t.className||"")+" "+o),!l){var u=n.createElement("style");u.id=s,u.textContent="."+o+" { position: relative; }",i.insertBefore(u,i.children[0])}var v=a.getComputedStyle(t),f=v.getPropertyValue("position");f==="static"&&Ie("A Cytoscape container has style position:static and so can not use UI extensions properly")}e.selection=[void 0,void 0,void 0,void 0,0],e.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],e.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},e.dragData={possibleDragElements:[]},e.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},e.redraws=0,e.showFps=r.showFps,e.debug=r.debug,e.webgl=r.webgl,e.hideEdgesOnViewport=r.hideEdgesOnViewport,e.textureOnViewport=r.textureOnViewport,e.wheelSensitivity=r.wheelSensitivity,e.motionBlurEnabled=r.motionBlur,e.forcedPixelRatio=te(r.pixelRatio)?r.pixelRatio:null,e.motionBlur=r.motionBlur,e.motionBlurOpacity=r.motionBlurOpacity,e.motionBlurTransparency=1-e.motionBlurOpacity,e.motionBlurPxRatio=1,e.mbPxRBlurry=1,e.minMbLowQualFrames=4,e.fullQualityMb=!1,e.clearedForMotionBlur=[],e.desktopTapThreshold=r.desktopTapThreshold,e.desktopTapThreshold2=r.desktopTapThreshold*r.desktopTapThreshold,e.touchTapThreshold=r.touchTapThreshold,e.touchTapThreshold2=r.touchTapThreshold*r.touchTapThreshold,e.tapholdDuration=500,e.bindings=[],e.beforeRenderCallbacks=[],e.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},e.registerNodeShapes(),e.registerArrowShapes(),e.registerCalculationListeners()};sa.notify=function(r,e){var t=this,a=t.cy;if(!this.destroyed){if(r==="init"){t.load();return}if(r==="destroy"){t.destroy();return}(r==="add"||r==="remove"||r==="move"&&a.hasCompoundNodes()||r==="load"||r==="zorder"||r==="mount")&&t.invalidateCachedZSortedEles(),r==="viewport"&&t.redrawHint("select",!0),r==="gc"&&t.redrawHint("gc",!0),(r==="load"||r==="resize"||r==="mount")&&(t.invalidateContainerClientCoordsCache(),t.matchCanvasSize(t.container)),t.redrawHint("eles",!0),t.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()}};sa.destroy=function(){var r=this;r.destroyed=!0,r.cy.stopAnimationLoop();for(var e=0;e<r.bindings.length;e++){var t=r.bindings[e],a=t,n=a.target;(n.off||n.removeEventListener).apply(n,a.args)}if(r.bindings=[],r.beforeRenderCallbacks=[],r.onUpdateEleCalcsFns=[],r.removeObserver&&r.removeObserver.disconnect(),r.styleObserver&&r.styleObserver.disconnect(),r.resizeObserver&&r.resizeObserver.disconnect(),r.labelCalcDiv)try{document.body.removeChild(r.labelCalcDiv)}catch{}};sa.isHeadless=function(){return!1};[go,mf,bf,ia,Qr,Ha].forEach(function(r){pe(sa,r)});var bs=1e3/60,xf={setupDequeueing:function(e){return function(){var a=this,n=this.renderer;if(!a.dequeueingSetup){a.dequeueingSetup=!0;var i=Fa(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),s=function(u,v){var f=Yr(),c=n.averageRedrawTime,h=n.lastRedrawTime,d=[],m=n.cy.extent(),g=n.getPixelRatio();for(u||n.flushRenderedStyleQueue();;){var p=Yr(),y=p-f,b=p-v;if(h<bs){var w=bs-(u?c:0);if(b>=e.deqFastCost*w)break}else if(u){if(y>=e.deqCost*h||y>=e.deqAvgCost*c)break}else if(b>=e.deqNoDrawCost*bs)break;var E=e.deq(a,g,m);if(E.length>0)for(var C=0;C<E.length;C++)d.push(E[C]);else break}d.length>0&&(e.onDeqd(a,d),!u&&e.shouldRedraw(a,d,g,m)&&i())},o=e.priority||js;n.beforeRender(s,o(a))}}}},ry=function(){function r(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:xn;dt(this,r),this.idsByKey=new Xr,this.keyForId=new Xr,this.cachesByLvl=new Xr,this.lvls=[],this.getKey=e,this.doesEleInvalidateKey=t}return ht(r,[{key:"getIdsFor",value:function(t){t==null&&We("Can not get id list for null key");var a=this.idsByKey,n=this.idsByKey.get(t);return n||(n=new ra,a.set(t,n)),n}},{key:"addIdForKey",value:function(t,a){t!=null&&this.getIdsFor(t).add(a)}},{key:"deleteIdForKey",value:function(t,a){t!=null&&this.getIdsFor(t).delete(a)}},{key:"getNumberOfIdsForKey",value:function(t){return t==null?0:this.getIdsFor(t).size}},{key:"updateKeyMappingFor",value:function(t){var a=t.id(),n=this.keyForId.get(a),i=this.getKey(t);this.deleteIdForKey(n,a),this.addIdForKey(i,a),this.keyForId.set(a,i)}},{key:"deleteKeyMappingFor",value:function(t){var a=t.id(),n=this.keyForId.get(a);this.deleteIdForKey(n,a),this.keyForId.delete(a)}},{key:"keyHasChangedFor",value:function(t){var a=t.id(),n=this.keyForId.get(a),i=this.getKey(t);return n!==i}},{key:"isInvalid",value:function(t){return this.keyHasChangedFor(t)||this.doesEleInvalidateKey(t)}},{key:"getCachesAt",value:function(t){var a=this.cachesByLvl,n=this.lvls,i=a.get(t);return i||(i=new Xr,a.set(t,i),n.push(t)),i}},{key:"getCache",value:function(t,a){return this.getCachesAt(a).get(t)}},{key:"get",value:function(t,a){var n=this.getKey(t),i=this.getCache(n,a);return i!=null&&this.updateKeyMappingFor(t),i}},{key:"getForCachedKey",value:function(t,a){var n=this.keyForId.get(t.id()),i=this.getCache(n,a);return i}},{key:"hasCache",value:function(t,a){return this.getCachesAt(a).has(t)}},{key:"has",value:function(t,a){var n=this.getKey(t);return this.hasCache(n,a)}},{key:"setCache",value:function(t,a,n){n.key=t,this.getCachesAt(a).set(t,n)}},{key:"set",value:function(t,a,n){var i=this.getKey(t);this.setCache(i,a,n),this.updateKeyMappingFor(t)}},{key:"deleteCache",value:function(t,a){this.getCachesAt(a).delete(t)}},{key:"delete",value:function(t,a){var n=this.getKey(t);this.deleteCache(n,a)}},{key:"invalidateKey",value:function(t){var a=this;this.lvls.forEach(function(n){return a.deleteCache(t,n)})}},{key:"invalidate",value:function(t){var a=t.id(),n=this.keyForId.get(a);this.deleteKeyMappingFor(t);var i=this.doesEleInvalidateKey(t);return i&&this.invalidateKey(n),i||this.getNumberOfIdsForKey(n)===0}}])}(),Ol=25,nn=50,pn=-4,Hs=3,Ef=7.99,ty=8,ay=1024,ny=1024,iy=1024,sy=.2,oy=.8,uy=10,ly=.15,vy=.1,fy=.9,cy=.9,dy=100,hy=1,$t={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},gy=cr({getKey:null,doesEleInvalidateKey:xn,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:fv,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),ba=function(e,t){var a=this;a.renderer=e,a.onDequeues=[];var n=gy(t);pe(a,n),a.lookup=new ry(n.getKey,n.doesEleInvalidateKey),a.setupDequeueing()},nr=ba.prototype;nr.reasons=$t;nr.getTextureQueue=function(r){var e=this;return e.eleImgCaches=e.eleImgCaches||{},e.eleImgCaches[r]=e.eleImgCaches[r]||[]};nr.getRetiredTextureQueue=function(r){var e=this,t=e.eleImgCaches.retired=e.eleImgCaches.retired||{},a=t[r]=t[r]||[];return a};nr.getElementQueue=function(){var r=this,e=r.eleCacheQueue=r.eleCacheQueue||new Va(function(t,a){return a.reqs-t.reqs});return e};nr.getElementKeyToQueue=function(){var r=this,e=r.eleKeyToCacheQueue=r.eleKeyToCacheQueue||{};return e};nr.getElement=function(r,e,t,a,n){var i=this,s=this.renderer,o=s.cy.zoom(),l=this.lookup;if(!e||e.w===0||e.h===0||isNaN(e.w)||isNaN(e.h)||!r.visible()||r.removed()||!i.allowEdgeTxrCaching&&r.isEdge()||!i.allowParentTxrCaching&&r.isParent())return null;if(a==null&&(a=Math.ceil(ro(o*t))),a<pn)a=pn;else if(o>=Ef||a>Hs)return null;var u=Math.pow(2,a),v=e.h*u,f=e.w*u,c=s.eleTextBiggerThanMin(r,u);if(!this.isVisible(r,c))return null;var h=l.get(r,a);if(h&&h.invalidated&&(h.invalidated=!1,h.texture.invalidatedWidth-=h.width),h)return h;var d;if(v<=Ol?d=Ol:v<=nn?d=nn:d=Math.ceil(v/nn)*nn,v>iy||f>ny)return null;var m=i.getTextureQueue(d),g=m[m.length-2],p=function(){return i.recycleTexture(d,f)||i.addTexture(d,f)};g||(g=m[m.length-1]),g||(g=p()),g.width-g.usedWidth<f&&(g=p());for(var y=function(L){return L&&L.scaledLabelShown===c},b=n&&n===$t.dequeue,w=n&&n===$t.highQuality,E=n&&n===$t.downscale,C,x=a+1;x<=Hs;x++){var T=l.get(r,x);if(T){C=T;break}}var S=C&&C.level===a+1?C:null,P=function(){g.context.drawImage(S.texture.canvas,S.x,0,S.width,S.height,g.usedWidth,0,f,v)};if(g.context.setTransform(1,0,0,1,0,0),g.context.clearRect(g.usedWidth,0,f,d),y(S))P();else if(y(C))if(w){for(var D=C.level;D>a;D--)S=i.getElement(r,e,t,D,$t.downscale);P()}else return i.queueElement(r,C.level-1),C;else{var A;if(!b&&!w&&!E)for(var B=a-1;B>=pn;B--){var R=l.get(r,B);if(R){A=R;break}}if(y(A))return i.queueElement(r,a),A;g.context.translate(g.usedWidth,0),g.context.scale(u,u),this.drawElement(g.context,r,e,c,!1),g.context.scale(1/u,1/u),g.context.translate(-g.usedWidth,0)}return h={x:g.usedWidth,texture:g,level:a,scale:u,width:f,height:v,scaledLabelShown:c},g.usedWidth+=Math.ceil(f+ty),g.eleCaches.push(h),l.set(r,a,h),i.checkTextureFullness(g),h};nr.invalidateElements=function(r){for(var e=0;e<r.length;e++)this.invalidateElement(r[e])};nr.invalidateElement=function(r){var e=this,t=e.lookup,a=[],n=t.isInvalid(r);if(n){for(var i=pn;i<=Hs;i++){var s=t.getForCachedKey(r,i);s&&a.push(s)}var o=t.invalidate(r);if(o)for(var l=0;l<a.length;l++){var u=a[l],v=u.texture;v.invalidatedWidth+=u.width,u.invalidated=!0,e.checkTextureUtility(v)}e.removeFromQueue(r)}};nr.checkTextureUtility=function(r){r.invalidatedWidth>=sy*r.width&&this.retireTexture(r)};nr.checkTextureFullness=function(r){var e=this,t=e.getTextureQueue(r.height);r.usedWidth/r.width>oy&&r.fullnessChecks>=uy?ut(t,r):r.fullnessChecks++};nr.retireTexture=function(r){var e=this,t=r.height,a=e.getTextureQueue(t),n=this.lookup;ut(a,r),r.retired=!0;for(var i=r.eleCaches,s=0;s<i.length;s++){var o=i[s];n.deleteCache(o.key,o.level)}eo(i);var l=e.getRetiredTextureQueue(t);l.push(r)};nr.addTexture=function(r,e){var t=this,a=t.getTextureQueue(r),n={};return a.push(n),n.eleCaches=[],n.height=r,n.width=Math.max(ay,e),n.usedWidth=0,n.invalidatedWidth=0,n.fullnessChecks=0,n.canvas=t.renderer.makeOffscreenCanvas(n.width,n.height),n.context=n.canvas.getContext("2d"),n};nr.recycleTexture=function(r,e){for(var t=this,a=t.getTextureQueue(r),n=t.getRetiredTextureQueue(r),i=0;i<n.length;i++){var s=n[i];if(s.width>=e)return s.retired=!1,s.usedWidth=0,s.invalidatedWidth=0,s.fullnessChecks=0,eo(s.eleCaches),s.context.setTransform(1,0,0,1,0,0),s.context.clearRect(0,0,s.width,s.height),ut(n,s),a.push(s),s}};nr.queueElement=function(r,e){var t=this,a=t.getElementQueue(),n=t.getElementKeyToQueue(),i=this.getKey(r),s=n[i];if(s)s.level=Math.max(s.level,e),s.eles.merge(r),s.reqs++,a.updateItem(s);else{var o={eles:r.spawn().merge(r),level:e,reqs:1,key:i};a.push(o),n[i]=o}};nr.dequeue=function(r){for(var e=this,t=e.getElementQueue(),a=e.getElementKeyToQueue(),n=[],i=e.lookup,s=0;s<hy&&t.size()>0;s++){var o=t.pop(),l=o.key,u=o.eles[0],v=i.hasCache(u,o.level);if(a[l]=null,v)continue;n.push(o);var f=e.getBoundingBox(u);e.getElement(u,f,r,o.level,$t.dequeue)}return n};nr.removeFromQueue=function(r){var e=this,t=e.getElementQueue(),a=e.getElementKeyToQueue(),n=this.getKey(r),i=a[n];i!=null&&(i.eles.length===1?(i.reqs=Js,t.updateItem(i),t.pop(),a[n]=null):i.eles.unmerge(r))};nr.onDequeue=function(r){this.onDequeues.push(r)};nr.offDequeue=function(r){ut(this.onDequeues,r)};nr.setupDequeueing=xf.setupDequeueing({deqRedrawThreshold:dy,deqCost:ly,deqAvgCost:vy,deqNoDrawCost:fy,deqFastCost:cy,deq:function(e,t,a){return e.dequeue(t,a)},onDeqd:function(e,t){for(var a=0;a<e.onDequeues.length;a++){var n=e.onDequeues[a];n(t)}},shouldRedraw:function(e,t,a,n){for(var i=0;i<t.length;i++)for(var s=t[i].eles,o=0;o<s.length;o++){var l=s[o].boundingBox();if(ao(l,n))return!0}return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var py=1,xa=-4,Pn=2,yy=3.99,my=50,by=50,wy=.15,xy=.1,Ey=.9,Cy=.9,Ty=1,Nl=250,Sy=4e3*4e3,zl=32767,ky=!0,Cf=function(e){var t=this,a=t.renderer=e,n=a.cy;t.layersByLevel={},t.firstGet=!0,t.lastInvalidationTime=Yr()-2*Nl,t.skipping=!1,t.eleTxrDeqs=n.collection(),t.scheduleElementRefinement=Fa(function(){t.refineElementTextures(t.eleTxrDeqs),t.eleTxrDeqs.unmerge(t.eleTxrDeqs)},by),a.beforeRender(function(s,o){o-t.lastInvalidationTime<=Nl?t.skipping=!0:t.skipping=!1},a.beforeRenderPriorities.lyrTxrSkip);var i=function(o,l){return l.reqs-o.reqs};t.layersQueue=new Va(i),t.setupDequeueing()},dr=Cf.prototype,Fl=0,Dy=Math.pow(2,53)-1;dr.makeLayer=function(r,e){var t=Math.pow(2,e),a=Math.ceil(r.w*t),n=Math.ceil(r.h*t),i=this.renderer.makeOffscreenCanvas(a,n),s={id:Fl=++Fl%Dy,bb:r,level:e,width:a,height:n,canvas:i,context:i.getContext("2d"),eles:[],elesQueue:[],reqs:0},o=s.context,l=-s.bb.x1,u=-s.bb.y1;return o.scale(t,t),o.translate(l,u),s};dr.getLayers=function(r,e,t){var a=this,n=a.renderer,i=n.cy,s=i.zoom(),o=a.firstGet;if(a.firstGet=!1,t==null){if(t=Math.ceil(ro(s*e)),t<xa)t=xa;else if(s>=yy||t>Pn)return null}a.validateLayersElesOrdering(t,r);var l=a.layersByLevel,u=Math.pow(2,t),v=l[t]=l[t]||[],f,c=a.levelIsComplete(t,r),h,d=function(){var P=function(M){if(a.validateLayersElesOrdering(M,r),a.levelIsComplete(M,r))return h=l[M],!0},D=function(M){if(!h)for(var L=t+M;xa<=L&&L<=Pn&&!P(L);L+=M);};D(1),D(-1);for(var A=v.length-1;A>=0;A--){var B=v[A];B.invalid&&ut(v,B)}};if(!c)d();else return v;var m=function(){if(!f){f=wr();for(var P=0;P<r.length;P++)xd(f,r[P].boundingBox())}return f},g=function(P){P=P||{};var D=P.after;m();var A=Math.ceil(f.w*u),B=Math.ceil(f.h*u);if(A>zl||B>zl)return null;var R=A*B;if(R>Sy)return null;var M=a.makeLayer(f,t);if(D!=null){var L=v.indexOf(D)+1;v.splice(L,0,M)}else(P.insert===void 0||P.insert)&&v.unshift(M);return M};if(a.skipping&&!o)return null;for(var p=null,y=r.length/py,b=!o,w=0;w<r.length;w++){var E=r[w],C=E._private.rscratch,x=C.imgLayerCaches=C.imgLayerCaches||{},T=x[t];if(T){p=T;continue}if((!p||p.eles.length>=y||!Cd(p.bb,E.boundingBox()))&&(p=g({insert:!0,after:p}),!p))return null;h||b?a.queueLayer(p,E):a.drawEleInLayer(p,E,t,e),p.eles.push(E),x[t]=p}return h||(b?null:v)};dr.getEleLevelForLayerLevel=function(r,e){return r};dr.drawEleInLayer=function(r,e,t,a){var n=this,i=this.renderer,s=r.context,o=e.boundingBox();o.w===0||o.h===0||!e.visible()||(t=n.getEleLevelForLayerLevel(t,a),i.setImgSmoothing(s,!1),i.drawCachedElement(s,e,null,null,t,ky),i.setImgSmoothing(s,!0))};dr.levelIsComplete=function(r,e){var t=this,a=t.layersByLevel[r];if(!a||a.length===0)return!1;for(var n=0,i=0;i<a.length;i++){var s=a[i];if(s.reqs>0||s.invalid)return!1;n+=s.eles.length}return n===e.length};dr.validateLayersElesOrdering=function(r,e){var t=this.layersByLevel[r];if(t)for(var a=0;a<t.length;a++){for(var n=t[a],i=-1,s=0;s<e.length;s++)if(n.eles[0]===e[s]){i=s;break}if(i<0){this.invalidateLayer(n);continue}for(var o=i,s=0;s<n.eles.length;s++)if(n.eles[s]!==e[o+s]){this.invalidateLayer(n);break}}};dr.updateElementsInLayers=function(r,e){for(var t=this,a=Ia(r[0]),n=0;n<r.length;n++)for(var i=a?null:r[n],s=a?r[n]:r[n].ele,o=s._private.rscratch,l=o.imgLayerCaches=o.imgLayerCaches||{},u=xa;u<=Pn;u++){var v=l[u];v&&(i&&t.getEleLevelForLayerLevel(v.level)!==i.level||e(v,s,i))}};dr.haveLayers=function(){for(var r=this,e=!1,t=xa;t<=Pn;t++){var a=r.layersByLevel[t];if(a&&a.length>0){e=!0;break}}return e};dr.invalidateElements=function(r){var e=this;r.length!==0&&(e.lastInvalidationTime=Yr(),!(r.length===0||!e.haveLayers())&&e.updateElementsInLayers(r,function(a,n,i){e.invalidateLayer(a)}))};dr.invalidateLayer=function(r){if(this.lastInvalidationTime=Yr(),!r.invalid){var e=r.level,t=r.eles,a=this.layersByLevel[e];ut(a,r),r.elesQueue=[],r.invalid=!0,r.replacement&&(r.replacement.invalid=!0);for(var n=0;n<t.length;n++){var i=t[n]._private.rscratch.imgLayerCaches;i&&(i[e]=null)}}};dr.refineElementTextures=function(r){var e=this;e.updateElementsInLayers(r,function(a,n,i){var s=a.replacement;if(s||(s=a.replacement=e.makeLayer(a.bb,a.level),s.replaces=a,s.eles=a.eles),!s.reqs)for(var o=0;o<s.eles.length;o++)e.queueLayer(s,s.eles[o])})};dr.enqueueElementRefinement=function(r){this.eleTxrDeqs.merge(r),this.scheduleElementRefinement()};dr.queueLayer=function(r,e){var t=this,a=t.layersQueue,n=r.elesQueue,i=n.hasId=n.hasId||{};if(!r.replacement){if(e){if(i[e.id()])return;n.push(e),i[e.id()]=!0}r.reqs?(r.reqs++,a.updateItem(r)):(r.reqs=1,a.push(r))}};dr.dequeue=function(r){for(var e=this,t=e.layersQueue,a=[],n=0;n<Ty&&t.size()!==0;){var i=t.peek();if(i.replacement){t.pop();continue}if(i.replaces&&i!==i.replaces.replacement){t.pop();continue}if(i.invalid){t.pop();continue}var s=i.elesQueue.shift();s&&(e.drawEleInLayer(i,s,i.level,r),n++),a.length===0&&a.push(!0),i.elesQueue.length===0&&(t.pop(),i.reqs=0,i.replaces&&e.applyLayerReplacement(i),e.requestRedraw())}return a};dr.applyLayerReplacement=function(r){var e=this,t=e.layersByLevel[r.level],a=r.replaces,n=t.indexOf(a);if(!(n<0||a.invalid)){t[n]=r;for(var i=0;i<r.eles.length;i++){var s=r.eles[i]._private,o=s.imgLayerCaches=s.imgLayerCaches||{};o&&(o[r.level]=r)}e.requestRedraw()}};dr.requestRedraw=Fa(function(){var r=this.renderer;r.redrawHint("eles",!0),r.redrawHint("drag",!0),r.redraw()},100);dr.setupDequeueing=xf.setupDequeueing({deqRedrawThreshold:my,deqCost:wy,deqAvgCost:xy,deqNoDrawCost:Ey,deqFastCost:Cy,deq:function(e,t){return e.dequeue(t)},onDeqd:js,shouldRedraw:fv,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var Tf={},Vl;function By(r,e){for(var t=0;t<e.length;t++){var a=e[t];r.lineTo(a.x,a.y)}}function Py(r,e,t){for(var a,n=0;n<e.length;n++){var i=e[n];n===0&&(a=i),r.lineTo(i.x,i.y)}r.quadraticCurveTo(t.x,t.y,a.x,a.y)}function ql(r,e,t){r.beginPath&&r.beginPath();for(var a=e,n=0;n<a.length;n++){var i=a[n];r.lineTo(i.x,i.y)}var s=t,o=t[0];r.moveTo(o.x,o.y);for(var n=1;n<s.length;n++){var i=s[n];r.lineTo(i.x,i.y)}r.closePath&&r.closePath()}function Ay(r,e,t,a,n){r.beginPath&&r.beginPath(),r.arc(t,a,n,0,Math.PI*2,!1);var i=e,s=i[0];r.moveTo(s.x,s.y);for(var o=0;o<i.length;o++){var l=i[o];r.lineTo(l.x,l.y)}r.closePath&&r.closePath()}function Ry(r,e,t,a){r.arc(e,t,a,0,Math.PI*2,!1)}Tf.arrowShapeImpl=function(r){return(Vl||(Vl={polygon:By,"triangle-backcurve":Py,"triangle-tee":ql,"circle-triangle":Ay,"triangle-cross":ql,circle:Ry}))[r]};var Hr={};Hr.drawElement=function(r,e,t,a,n,i){var s=this;e.isNode()?s.drawNode(r,e,t,a,n,i):s.drawEdge(r,e,t,a,n,i)};Hr.drawElementOverlay=function(r,e){var t=this;e.isNode()?t.drawNodeOverlay(r,e):t.drawEdgeOverlay(r,e)};Hr.drawElementUnderlay=function(r,e){var t=this;e.isNode()?t.drawNodeUnderlay(r,e):t.drawEdgeUnderlay(r,e)};Hr.drawCachedElementPortion=function(r,e,t,a,n,i,s,o){var l=this,u=t.getBoundingBox(e);if(!(u.w===0||u.h===0)){var v=t.getElement(e,u,a,n,i);if(v!=null){var f=o(l,e);if(f===0)return;var c=s(l,e),h=u.x1,d=u.y1,m=u.w,g=u.h,p,y,b,w,E;if(c!==0){var C=t.getRotationPoint(e);b=C.x,w=C.y,r.translate(b,w),r.rotate(c),E=l.getImgSmoothing(r),E||l.setImgSmoothing(r,!0);var x=t.getRotationOffset(e);p=x.x,y=x.y}else p=h,y=d;var T;f!==1&&(T=r.globalAlpha,r.globalAlpha=T*f),r.drawImage(v.texture.canvas,v.x,0,v.width,v.height,p,y,m,g),f!==1&&(r.globalAlpha=T),c!==0&&(r.rotate(-c),r.translate(-b,-w),E||l.setImgSmoothing(r,!1))}else t.drawElement(r,e)}};var My=function(){return 0},Ly=function(e,t){return e.getTextAngle(t,null)},Iy=function(e,t){return e.getTextAngle(t,"source")},Oy=function(e,t){return e.getTextAngle(t,"target")},Ny=function(e,t){return t.effectiveOpacity()},ws=function(e,t){return t.pstyle("text-opacity").pfValue*t.effectiveOpacity()};Hr.drawCachedElement=function(r,e,t,a,n,i){var s=this,o=s.data,l=o.eleTxrCache,u=o.lblTxrCache,v=o.slbTxrCache,f=o.tlbTxrCache,c=e.boundingBox(),h=i===!0?l.reasons.highQuality:null;if(!(c.w===0||c.h===0||!e.visible())&&(!a||ao(c,a))){var d=e.isEdge(),m=e.element()._private.rscratch.badLine;s.drawElementUnderlay(r,e),s.drawCachedElementPortion(r,e,l,t,n,h,My,Ny),(!d||!m)&&s.drawCachedElementPortion(r,e,u,t,n,h,Ly,ws),d&&!m&&(s.drawCachedElementPortion(r,e,v,t,n,h,Iy,ws),s.drawCachedElementPortion(r,e,f,t,n,h,Oy,ws)),s.drawElementOverlay(r,e)}};Hr.drawElements=function(r,e){for(var t=this,a=0;a<e.length;a++){var n=e[a];t.drawElement(r,n)}};Hr.drawCachedElements=function(r,e,t,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];n.drawCachedElement(r,s,t,a)}};Hr.drawCachedNodes=function(r,e,t,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];s.isNode()&&n.drawCachedElement(r,s,t,a)}};Hr.drawLayeredElements=function(r,e,t,a){var n=this,i=n.data.lyrTxrCache.getLayers(e,t);if(i)for(var s=0;s<i.length;s++){var o=i[s],l=o.bb;l.w===0||l.h===0||r.drawImage(o.canvas,l.x1,l.y1,l.w,l.h)}else n.drawCachedElements(r,e,t,a)};var Jr={};Jr.drawEdge=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o=e._private.rscratch;if(!(i&&!e.visible())&&!(o.badLine||o.allpts==null||isNaN(o.allpts[0]))){var l;t&&(l=t,r.translate(-l.x1,-l.y1));var u=i?e.pstyle("opacity").value:1,v=i?e.pstyle("line-opacity").value:1,f=e.pstyle("curve-style").value,c=e.pstyle("line-style").value,h=e.pstyle("width").pfValue,d=e.pstyle("line-cap").value,m=e.pstyle("line-outline-width").value,g=e.pstyle("line-outline-color").value,p=u*v,y=u*v,b=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p;f==="straight-triangle"?(s.eleStrokeStyle(r,e,M),s.drawEdgeTrianglePath(e,r,o.allpts)):(r.lineWidth=h,r.lineCap=d,s.eleStrokeStyle(r,e,M),s.drawEdgePath(e,r,o.allpts,c),r.lineCap="butt")},w=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p;if(r.lineWidth=h+m,r.lineCap=d,m>0)s.colorStrokeStyle(r,g[0],g[1],g[2],M);else{r.lineCap="butt";return}f==="straight-triangle"?s.drawEdgeTrianglePath(e,r,o.allpts):(s.drawEdgePath(e,r,o.allpts,c),r.lineCap="butt")},E=function(){n&&s.drawEdgeOverlay(r,e)},C=function(){n&&s.drawEdgeUnderlay(r,e)},x=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:y;s.drawArrowheads(r,e,M)},T=function(){s.drawElementText(r,e,null,a)};r.lineJoin="round";var S=e.pstyle("ghost").value==="yes";if(S){var P=e.pstyle("ghost-offset-x").pfValue,D=e.pstyle("ghost-offset-y").pfValue,A=e.pstyle("ghost-opacity").value,B=p*A;r.translate(P,D),b(B),x(B),r.translate(-P,-D)}else w();C(),b(),x(),E(),T(),t&&r.translate(l.x1,l.y1)}};var Sf=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,a){if(a.visible()){var n=a.pstyle("".concat(e,"-opacity")).value;if(n!==0){var i=this,s=i.usePaths(),o=a._private.rscratch,l=a.pstyle("".concat(e,"-padding")).pfValue,u=2*l,v=a.pstyle("".concat(e,"-color")).value;t.lineWidth=u,o.edgeType==="self"&&!s?t.lineCap="butt":t.lineCap="round",i.colorStrokeStyle(t,v[0],v[1],v[2],n),i.drawEdgePath(a,t,o.allpts,"solid")}}}};Jr.drawEdgeOverlay=Sf("overlay");Jr.drawEdgeUnderlay=Sf("underlay");Jr.drawEdgePath=function(r,e,t,a){var n=r._private.rscratch,i=e,s,o=!1,l=this.usePaths(),u=r.pstyle("line-dash-pattern").pfValue,v=r.pstyle("line-dash-offset").pfValue;if(l){var f=t.join("$"),c=n.pathCacheKey&&n.pathCacheKey===f;c?(s=e=n.pathCache,o=!0):(s=e=new Path2D,n.pathCacheKey=f,n.pathCache=s)}if(i.setLineDash)switch(a){case"dotted":i.setLineDash([1,1]);break;case"dashed":i.setLineDash(u),i.lineDashOffset=v;break;case"solid":i.setLineDash([]);break}if(!o&&!n.badLine)switch(e.beginPath&&e.beginPath(),e.moveTo(t[0],t[1]),n.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var h=2;h+3<t.length;h+=4)e.quadraticCurveTo(t[h],t[h+1],t[h+2],t[h+3]);break;case"straight":case"haystack":for(var d=2;d+1<t.length;d+=2)e.lineTo(t[d],t[d+1]);break;case"segments":if(n.isRound){var m=kr(n.roundCorners),g;try{for(m.s();!(g=m.n()).done;){var p=g.value;df(e,p)}}catch(b){m.e(b)}finally{m.f()}e.lineTo(t[t.length-2],t[t.length-1])}else for(var y=2;y+1<t.length;y+=2)e.lineTo(t[y],t[y+1]);break}e=i,l?e.stroke(s):e.stroke(),e.setLineDash&&e.setLineDash([])};Jr.drawEdgeTrianglePath=function(r,e,t){e.fillStyle=e.strokeStyle;for(var a=r.pstyle("width").pfValue,n=0;n+1<t.length;n+=2){var i=[t[n+2]-t[n],t[n+3]-t[n+1]],s=Math.sqrt(i[0]*i[0]+i[1]*i[1]),o=[i[1]/s,-i[0]/s],l=[o[0]*a/2,o[1]*a/2];e.beginPath(),e.moveTo(t[n]-l[0],t[n+1]-l[1]),e.lineTo(t[n]+l[0],t[n+1]+l[1]),e.lineTo(t[n+2],t[n+3]),e.closePath(),e.fill()}};Jr.drawArrowheads=function(r,e,t){var a=e._private.rscratch,n=a.edgeType==="haystack";n||this.drawArrowhead(r,e,"source",a.arrowStartX,a.arrowStartY,a.srcArrowAngle,t),this.drawArrowhead(r,e,"mid-target",a.midX,a.midY,a.midtgtArrowAngle,t),this.drawArrowhead(r,e,"mid-source",a.midX,a.midY,a.midsrcArrowAngle,t),n||this.drawArrowhead(r,e,"target",a.arrowEndX,a.arrowEndY,a.tgtArrowAngle,t)};Jr.drawArrowhead=function(r,e,t,a,n,i,s){if(!(isNaN(a)||a==null||isNaN(n)||n==null||isNaN(i)||i==null)){var o=this,l=e.pstyle(t+"-arrow-shape").value;if(l!=="none"){var u=e.pstyle(t+"-arrow-fill").value==="hollow"?"both":"filled",v=e.pstyle(t+"-arrow-fill").value,f=e.pstyle("width").pfValue,c=e.pstyle(t+"-arrow-width"),h=c.value==="match-line"?f:c.pfValue;c.units==="%"&&(h*=f);var d=e.pstyle("opacity").value;s===void 0&&(s=d);var m=r.globalCompositeOperation;(s!==1||v==="hollow")&&(r.globalCompositeOperation="destination-out",o.colorFillStyle(r,255,255,255,1),o.colorStrokeStyle(r,255,255,255,1),o.drawArrowShape(e,r,u,f,l,h,a,n,i),r.globalCompositeOperation=m);var g=e.pstyle(t+"-arrow-color").value;o.colorFillStyle(r,g[0],g[1],g[2],s),o.colorStrokeStyle(r,g[0],g[1],g[2],s),o.drawArrowShape(e,r,v,f,l,h,a,n,i)}}};Jr.drawArrowShape=function(r,e,t,a,n,i,s,o,l){var u=this,v=this.usePaths()&&n!=="triangle-cross",f=!1,c,h=e,d={x:s,y:o},m=r.pstyle("arrow-scale").value,g=this.getArrowWidth(a,m),p=u.arrowShapes[n];if(v){var y=u.arrowPathCache=u.arrowPathCache||[],b=kt(n),w=y[b];w!=null?(c=e=w,f=!0):(c=e=new Path2D,y[b]=c)}f||(e.beginPath&&e.beginPath(),v?p.draw(e,1,0,{x:0,y:0},1):p.draw(e,g,l,d,a),e.closePath&&e.closePath()),e=h,v&&(e.translate(s,o),e.rotate(l),e.scale(g,g)),(t==="filled"||t==="both")&&(v?e.fill(c):e.fill()),(t==="hollow"||t==="both")&&(e.lineWidth=i/(v?g:1),e.lineJoin="miter",v?e.stroke(c):e.stroke()),v&&(e.scale(1/g,1/g),e.rotate(-l),e.translate(-s,-o))};var mo={};mo.safeDrawImage=function(r,e,t,a,n,i,s,o,l,u){if(!(n<=0||i<=0||l<=0||u<=0))try{r.drawImage(e,t,a,n,i,s,o,l,u)}catch(v){Ie(v)}};mo.drawInscribedImage=function(r,e,t,a,n){var i=this,s=t.position(),o=s.x,l=s.y,u=t.cy().style(),v=u.getIndexedStyle.bind(u),f=v(t,"background-fit","value",a),c=v(t,"background-repeat","value",a),h=t.width(),d=t.height(),m=t.padding()*2,g=h+(v(t,"background-width-relative-to","value",a)==="inner"?0:m),p=d+(v(t,"background-height-relative-to","value",a)==="inner"?0:m),y=t._private.rscratch,b=v(t,"background-clip","value",a),w=b==="node",E=v(t,"background-image-opacity","value",a)*n,C=v(t,"background-image-smoothing","value",a),x=t.pstyle("corner-radius").value;x!=="auto"&&(x=t.pstyle("corner-radius").pfValue);var T=e.width||e.cachedW,S=e.height||e.cachedH;(T==null||S==null)&&(document.body.appendChild(e),T=e.cachedW=e.width||e.offsetWidth,S=e.cachedH=e.height||e.offsetHeight,document.body.removeChild(e));var P=T,D=S;if(v(t,"background-width","value",a)!=="auto"&&(v(t,"background-width","units",a)==="%"?P=v(t,"background-width","pfValue",a)*g:P=v(t,"background-width","pfValue",a)),v(t,"background-height","value",a)!=="auto"&&(v(t,"background-height","units",a)==="%"?D=v(t,"background-height","pfValue",a)*p:D=v(t,"background-height","pfValue",a)),!(P===0||D===0)){if(f==="contain"){var A=Math.min(g/P,p/D);P*=A,D*=A}else if(f==="cover"){var A=Math.max(g/P,p/D);P*=A,D*=A}var B=o-g/2,R=v(t,"background-position-x","units",a),M=v(t,"background-position-x","pfValue",a);R==="%"?B+=(g-P)*M:B+=M;var L=v(t,"background-offset-x","units",a),I=v(t,"background-offset-x","pfValue",a);L==="%"?B+=(g-P)*I:B+=I;var O=l-p/2,F=v(t,"background-position-y","units",a),_=v(t,"background-position-y","pfValue",a);F==="%"?O+=(p-D)*_:O+=_;var N=v(t,"background-offset-y","units",a),q=v(t,"background-offset-y","pfValue",a);N==="%"?O+=(p-D)*q:O+=q,y.pathCache&&(B-=o,O-=l,o=0,l=0);var U=r.globalAlpha;r.globalAlpha=E;var X=i.getImgSmoothing(r),j=!1;if(C==="no"&&X?(i.setImgSmoothing(r,!1),j=!0):C==="yes"&&!X&&(i.setImgSmoothing(r,!0),j=!0),c==="no-repeat")w&&(r.save(),y.pathCache?r.clip(y.pathCache):(i.nodeShapes[i.getNodeShape(t)].draw(r,o,l,g,p,x,y),r.clip())),i.safeDrawImage(r,e,0,0,T,S,B,O,P,D),w&&r.restore();else{var J=r.createPattern(e,c);r.fillStyle=J,i.nodeShapes[i.getNodeShape(t)].draw(r,o,l,g,p,x,y),r.translate(B,O),r.fill(),r.translate(-B,-O)}r.globalAlpha=U,j&&i.setImgSmoothing(r,X)}};var Mt={};Mt.eleTextBiggerThanMin=function(r,e){if(!e){var t=r.cy().zoom(),a=this.getPixelRatio(),n=Math.ceil(ro(t*a));e=Math.pow(2,n)}var i=r.pstyle("font-size").pfValue*e,s=r.pstyle("min-zoomed-font-size").pfValue;return!(i<s)};Mt.drawElementText=function(r,e,t,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this;if(a==null){if(i&&!s.eleTextBiggerThanMin(e))return}else if(a===!1)return;if(e.isNode()){var o=e.pstyle("label");if(!o||!o.value)return;var l=s.getLabelJustification(e);r.textAlign=l,r.textBaseline="bottom"}else{var u=e.element()._private.rscratch.badLine,v=e.pstyle("label"),f=e.pstyle("source-label"),c=e.pstyle("target-label");if(u||(!v||!v.value)&&(!f||!f.value)&&(!c||!c.value))return;r.textAlign="center",r.textBaseline="bottom"}var h=!t,d;t&&(d=t,r.translate(-d.x1,-d.y1)),n==null?(s.drawText(r,e,null,h,i),e.isEdge()&&(s.drawText(r,e,"source",h,i),s.drawText(r,e,"target",h,i))):s.drawText(r,e,n,h,i),t&&r.translate(d.x1,d.y1)};Mt.getFontCache=function(r){var e;this.fontCaches=this.fontCaches||[];for(var t=0;t<this.fontCaches.length;t++)if(e=this.fontCaches[t],e.context===r)return e;return e={context:r},this.fontCaches.push(e),e};Mt.setupTextStyle=function(r,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,a=e.pstyle("font-style").strValue,n=e.pstyle("font-size").pfValue+"px",i=e.pstyle("font-family").strValue,s=e.pstyle("font-weight").strValue,o=t?e.effectiveOpacity()*e.pstyle("text-opacity").value:1,l=e.pstyle("text-outline-opacity").value*o,u=e.pstyle("color").value,v=e.pstyle("text-outline-color").value;r.font=a+" "+s+" "+n+" "+i,r.lineJoin="round",this.colorFillStyle(r,u[0],u[1],u[2],o),this.colorStrokeStyle(r,v[0],v[1],v[2],l)};function xs(r,e,t,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:5,s=arguments.length>6?arguments[6]:void 0;r.beginPath(),r.moveTo(e+i,t),r.lineTo(e+a-i,t),r.quadraticCurveTo(e+a,t,e+a,t+i),r.lineTo(e+a,t+n-i),r.quadraticCurveTo(e+a,t+n,e+a-i,t+n),r.lineTo(e+i,t+n),r.quadraticCurveTo(e,t+n,e,t+n-i),r.lineTo(e,t+i),r.quadraticCurveTo(e,t,e+i,t),r.closePath(),s?r.stroke():r.fill()}Mt.getTextAngle=function(r,e){var t,a=r._private,n=a.rscratch,i=e?e+"-":"",s=r.pstyle(i+"text-rotation");if(s.strValue==="autorotate"){var o=Tr(n,"labelAngle",e);t=r.isEdge()?o:0}else s.strValue==="none"?t=0:t=s.pfValue;return t};Mt.drawText=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=e._private,s=i.rscratch,o=n?e.effectiveOpacity():1;if(!(n&&(o===0||e.pstyle("text-opacity").value===0))){t==="main"&&(t=null);var l=Tr(s,"labelX",t),u=Tr(s,"labelY",t),v,f,c=this.getLabelText(e,t);if(c!=null&&c!==""&&!isNaN(l)&&!isNaN(u)){this.setupTextStyle(r,e,n);var h=t?t+"-":"",d=Tr(s,"labelWidth",t),m=Tr(s,"labelHeight",t),g=e.pstyle(h+"text-margin-x").pfValue,p=e.pstyle(h+"text-margin-y").pfValue,y=e.isEdge(),b=e.pstyle("text-halign").value,w=e.pstyle("text-valign").value;y&&(b="center",w="center"),l+=g,u+=p;var E;switch(a?E=this.getTextAngle(e,t):E=0,E!==0&&(v=l,f=u,r.translate(v,f),r.rotate(E),l=0,u=0),w){case"top":break;case"center":u+=m/2;break;case"bottom":u+=m;break}var C=e.pstyle("text-background-opacity").value,x=e.pstyle("text-border-opacity").value,T=e.pstyle("text-border-width").pfValue,S=e.pstyle("text-background-padding").pfValue,P=e.pstyle("text-background-shape").strValue,D=P.indexOf("round")===0,A=2;if(C>0||T>0&&x>0){var B=l-S;switch(b){case"left":B-=d;break;case"center":B-=d/2;break}var R=u-m-S,M=d+2*S,L=m+2*S;if(C>0){var I=r.fillStyle,O=e.pstyle("text-background-color").value;r.fillStyle="rgba("+O[0]+","+O[1]+","+O[2]+","+C*o+")",D?xs(r,B,R,M,L,A):r.fillRect(B,R,M,L),r.fillStyle=I}if(T>0&&x>0){var F=r.strokeStyle,_=r.lineWidth,N=e.pstyle("text-border-color").value,q=e.pstyle("text-border-style").value;if(r.strokeStyle="rgba("+N[0]+","+N[1]+","+N[2]+","+x*o+")",r.lineWidth=T,r.setLineDash)switch(q){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash([4,2]);break;case"double":r.lineWidth=T/4,r.setLineDash([]);break;case"solid":r.setLineDash([]);break}if(D?xs(r,B,R,M,L,A,"stroke"):r.strokeRect(B,R,M,L),q==="double"){var U=T/2;D?xs(r,B+U,R+U,M-U*2,L-U*2,A,"stroke"):r.strokeRect(B+U,R+U,M-U*2,L-U*2)}r.setLineDash&&r.setLineDash([]),r.lineWidth=_,r.strokeStyle=F}}var X=2*e.pstyle("text-outline-width").pfValue;if(X>0&&(r.lineWidth=X),e.pstyle("text-wrap").value==="wrap"){var j=Tr(s,"labelWrapCachedLines",t),J=Tr(s,"labelLineHeight",t),re=d/2,ae=this.getLabelJustification(e);switch(ae==="auto"||(b==="left"?ae==="left"?l+=-d:ae==="center"&&(l+=-re):b==="center"?ae==="left"?l+=-re:ae==="right"&&(l+=re):b==="right"&&(ae==="center"?l+=re:ae==="right"&&(l+=d))),w){case"top":u-=(j.length-1)*J;break;case"center":case"bottom":u-=(j.length-1)*J;break}for(var Z=0;Z<j.length;Z++)X>0&&r.strokeText(j[Z],l,u),r.fillText(j[Z],l,u),u+=J}else X>0&&r.strokeText(c,l,u),r.fillText(c,l,u);E!==0&&(r.rotate(-E),r.translate(-v,-f))}}};var pt={};pt.drawNode=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o,l,u=e._private,v=u.rscratch,f=e.position();if(!(!te(f.x)||!te(f.y))&&!(i&&!e.visible())){var c=i?e.effectiveOpacity():1,h=s.usePaths(),d,m=!1,g=e.padding();o=e.width()+2*g,l=e.height()+2*g;var p;t&&(p=t,r.translate(-p.x1,-p.y1));for(var y=e.pstyle("background-image"),b=y.value,w=new Array(b.length),E=new Array(b.length),C=0,x=0;x<b.length;x++){var T=b[x],S=w[x]=T!=null&&T!=="none";if(S){var P=e.cy().style().getIndexedStyle(e,"background-image-crossorigin","value",x);C++,E[x]=s.getCachedImage(T,P,function(){u.backgroundTimestamp=Date.now(),e.emitAndNotify("background")})}}var D=e.pstyle("background-blacken").value,A=e.pstyle("border-width").pfValue,B=e.pstyle("background-opacity").value*c,R=e.pstyle("border-color").value,M=e.pstyle("border-style").value,L=e.pstyle("border-join").value,I=e.pstyle("border-cap").value,O=e.pstyle("border-position").value,F=e.pstyle("border-dash-pattern").pfValue,_=e.pstyle("border-dash-offset").pfValue,N=e.pstyle("border-opacity").value*c,q=e.pstyle("outline-width").pfValue,U=e.pstyle("outline-color").value,X=e.pstyle("outline-style").value,j=e.pstyle("outline-opacity").value*c,J=e.pstyle("outline-offset").value,re=e.pstyle("corner-radius").value;re!=="auto"&&(re=e.pstyle("corner-radius").pfValue);var ae=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:B;s.eleFillStyle(r,e,ue)},Z=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:N;s.colorStrokeStyle(r,R[0],R[1],R[2],ue)},z=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:j;s.colorStrokeStyle(r,U[0],U[1],U[2],ue)},G=function(ue,Y,k,V){var W=s.nodePathCache=s.nodePathCache||[],$=vv(k==="polygon"?k+","+V.join(","):k,""+Y,""+ue,""+re),K=W[$],se,ee=!1;return K!=null?(se=K,ee=!0,v.pathCache=se):(se=new Path2D,W[$]=v.pathCache=se),{path:se,cacheHit:ee}},H=e.pstyle("shape").strValue,Q=e.pstyle("shape-polygon-points").pfValue;if(h){r.translate(f.x,f.y);var ne=G(o,l,H,Q);d=ne.path,m=ne.cacheHit}var be=function(){if(!m){var ue=f;h&&(ue={x:0,y:0}),s.nodeShapes[s.getNodeShape(e)].draw(d||r,ue.x,ue.y,o,l,re,v)}h?r.fill(d):r.fill()},Fe=function(){for(var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,Y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,k=u.backgrounding,V=0,W=0;W<E.length;W++){var $=e.cy().style().getIndexedStyle(e,"background-image-containment","value",W);if(Y&&$==="over"||!Y&&$==="inside"){V++;continue}w[W]&&E[W].complete&&!E[W].error&&(V++,s.drawInscribedImage(r,E[W],e,W,ue))}u.backgrounding=V!==C,k!==u.backgrounding&&e.updateStyle(!1)},Be=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,Y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:c;s.hasPie(e)&&(s.drawPie(r,e,Y),ue&&(h||s.nodeShapes[s.getNodeShape(e)].draw(r,f.x,f.y,o,l,re,v)))},oe=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,Y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:c;s.hasStripe(e)&&(r.save(),h?r.clip(v.pathCache):(s.nodeShapes[s.getNodeShape(e)].draw(r,f.x,f.y,o,l,re,v),r.clip()),s.drawStripe(r,e,Y),r.restore(),ue&&(h||s.nodeShapes[s.getNodeShape(e)].draw(r,f.x,f.y,o,l,re,v)))},ve=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,Y=(D>0?D:-D)*ue,k=D>0?0:255;D!==0&&(s.colorFillStyle(r,k,k,k,Y),h?r.fill(d):r.fill())},he=function(){if(A>0){if(r.lineWidth=A,r.lineCap=I,r.lineJoin=L,r.setLineDash)switch(M){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash(F),r.lineDashOffset=_;break;case"solid":case"double":r.setLineDash([]);break}if(O!=="center"){if(r.save(),r.lineWidth*=2,O==="inside")h?r.clip(d):r.clip();else{var ue=new Path2D;ue.rect(-o/2-A,-l/2-A,o+2*A,l+2*A),ue.addPath(d),r.clip(ue,"evenodd")}h?r.stroke(d):r.stroke(),r.restore()}else h?r.stroke(d):r.stroke();if(M==="double"){r.lineWidth=A/3;var Y=r.globalCompositeOperation;r.globalCompositeOperation="destination-out",h?r.stroke(d):r.stroke(),r.globalCompositeOperation=Y}r.setLineDash&&r.setLineDash([])}},ye=function(){if(q>0){if(r.lineWidth=q,r.lineCap="butt",r.setLineDash)switch(X){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash([4,2]);break;case"solid":case"double":r.setLineDash([]);break}var ue=f;h&&(ue={x:0,y:0});var Y=s.getNodeShape(e),k=A;O==="inside"&&(k=0),O==="outside"&&(k*=2);var V=(o+k+(q+J))/o,W=(l+k+(q+J))/l,$=o*V,K=l*W,se=s.nodeShapes[Y].points,ee;if(h){var fe=G($,K,Y,se);ee=fe.path}if(Y==="ellipse")s.drawEllipsePath(ee||r,ue.x,ue.y,$,K);else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(Y)){var le=0,ge=0,Te=0;Y==="round-diamond"?le=(k+J+q)*1.4:Y==="round-heptagon"?(le=(k+J+q)*1.075,Te=-(k/2+J+q)/35):Y==="round-hexagon"?le=(k+J+q)*1.12:Y==="round-pentagon"?(le=(k+J+q)*1.13,Te=-(k/2+J+q)/15):Y==="round-tag"?(le=(k+J+q)*1.12,ge=(k/2+q+J)*.07):Y==="round-triangle"&&(le=(k+J+q)*(Math.PI/2),Te=-(k+J/2+q)/Math.PI),le!==0&&(V=(o+le)/o,$=o*V,["round-hexagon","round-tag"].includes(Y)||(W=(l+le)/l,K=l*W)),re=re==="auto"?bv($,K):re;for(var Ee=$/2,ce=K/2,De=re+(k+q+J)/2,Se=new Array(se.length/2),Je=new Array(se.length/2),Ue=0;Ue<se.length/2;Ue++)Se[Ue]={x:ue.x+ge+Ee*se[Ue*2],y:ue.y+Te+ce*se[Ue*2+1]};var mr,Ye,ir,je,lr=Se.length;for(Ye=Se[lr-1],mr=0;mr<lr;mr++)ir=Se[mr%lr],je=Se[(mr+1)%lr],Je[mr]=po(Ye,ir,je,De),Ye=ir,ir=je;s.drawRoundPolygonPath(ee||r,ue.x+ge,ue.y+Te,o*V,l*W,se,Je)}else if(["roundrectangle","round-rectangle"].includes(Y))re=re==="auto"?lt($,K):re,s.drawRoundRectanglePath(ee||r,ue.x,ue.y,$,K,re+(k+q+J)/2);else if(["cutrectangle","cut-rectangle"].includes(Y))re=re==="auto"?no():re,s.drawCutRectanglePath(ee||r,ue.x,ue.y,$,K,null,re+(k+q+J)/4);else if(["bottomroundrectangle","bottom-round-rectangle"].includes(Y))re=re==="auto"?lt($,K):re,s.drawBottomRoundRectanglePath(ee||r,ue.x,ue.y,$,K,re+(k+q+J)/2);else if(Y==="barrel")s.drawBarrelPath(ee||r,ue.x,ue.y,$,K);else if(Y.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(Y)){var jr=(k+q+J)/o;se=En(Cn(se,jr)),s.drawPolygonPath(ee||r,ue.x,ue.y,o,l,se)}else{var Ze=(k+q+J)/o;se=En(Cn(se,-Ze)),s.drawPolygonPath(ee||r,ue.x,ue.y,o,l,se)}if(h?r.stroke(ee):r.stroke(),X==="double"){r.lineWidth=k/3;var Wr=r.globalCompositeOperation;r.globalCompositeOperation="destination-out",h?r.stroke(ee):r.stroke(),r.globalCompositeOperation=Wr}r.setLineDash&&r.setLineDash([])}},me=function(){n&&s.drawNodeOverlay(r,e,f,o,l)},we=function(){n&&s.drawNodeUnderlay(r,e,f,o,l)},xe=function(){s.drawElementText(r,e,null,a)},Pe=e.pstyle("ghost").value==="yes";if(Pe){var Ve=e.pstyle("ghost-offset-x").pfValue,Xe=e.pstyle("ghost-offset-y").pfValue,Oe=e.pstyle("ghost-opacity").value,He=Oe*c;r.translate(Ve,Xe),z(),ye(),ae(Oe*B),be(),Fe(He,!0),Z(Oe*N),he(),Be(D!==0||A!==0),oe(D!==0||A!==0),Fe(He,!1),ve(He),r.translate(-Ve,-Xe)}h&&r.translate(-f.x,-f.y),we(),h&&r.translate(f.x,f.y),z(),ye(),ae(),be(),Fe(c,!0),Z(),he(),Be(D!==0||A!==0),oe(D!==0||A!==0),Fe(c,!1),ve(),h&&r.translate(-f.x,-f.y),xe(),me(),t&&r.translate(p.x1,p.y1)}};var kf=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,a,n,i,s){var o=this;if(a.visible()){var l=a.pstyle("".concat(e,"-padding")).pfValue,u=a.pstyle("".concat(e,"-opacity")).value,v=a.pstyle("".concat(e,"-color")).value,f=a.pstyle("".concat(e,"-shape")).value,c=a.pstyle("".concat(e,"-corner-radius")).value;if(u>0){if(n=n||a.position(),i==null||s==null){var h=a.padding();i=a.width()+2*h,s=a.height()+2*h}o.colorFillStyle(t,v[0],v[1],v[2],u),o.nodeShapes[f].draw(t,n.x,n.y,i+l*2,s+l*2,c),t.fill()}}}};pt.drawNodeOverlay=kf("overlay");pt.drawNodeUnderlay=kf("underlay");pt.hasPie=function(r){return r=r[0],r._private.hasPie};pt.hasStripe=function(r){return r=r[0],r._private.hasStripe};pt.drawPie=function(r,e,t,a){e=e[0],a=a||e.position();var n=e.cy().style(),i=e.pstyle("pie-size"),s=e.pstyle("pie-hole"),o=e.pstyle("pie-start-angle").pfValue,l=a.x,u=a.y,v=e.width(),f=e.height(),c=Math.min(v,f)/2,h,d=0,m=this.usePaths();if(m&&(l=0,u=0),i.units==="%"?c=c*i.pfValue:i.pfValue!==void 0&&(c=i.pfValue/2),s.units==="%"?h=c*s.pfValue:s.pfValue!==void 0&&(h=s.pfValue/2),!(h>=c))for(var g=1;g<=n.pieBackgroundN;g++){var p=e.pstyle("pie-"+g+"-background-size").value,y=e.pstyle("pie-"+g+"-background-color").value,b=e.pstyle("pie-"+g+"-background-opacity").value*t,w=p/100;w+d>1&&(w=1-d);var E=1.5*Math.PI+2*Math.PI*d;E+=o;var C=2*Math.PI*w,x=E+C;p===0||d>=1||d+w>1||(h===0?(r.beginPath(),r.moveTo(l,u),r.arc(l,u,c,E,x),r.closePath()):(r.beginPath(),r.arc(l,u,c,E,x),r.arc(l,u,h,x,E,!0),r.closePath()),this.colorFillStyle(r,y[0],y[1],y[2],b),r.fill(),d+=w)}};pt.drawStripe=function(r,e,t,a){e=e[0],a=a||e.position();var n=e.cy().style(),i=a.x,s=a.y,o=e.width(),l=e.height(),u=0,v=this.usePaths();r.save();var f=e.pstyle("stripe-direction").value,c=e.pstyle("stripe-size");switch(f){case"vertical":break;case"righward":r.rotate(-Math.PI/2);break}var h=o,d=l;c.units==="%"?(h=h*c.pfValue,d=d*c.pfValue):c.pfValue!==void 0&&(h=c.pfValue,d=c.pfValue),v&&(i=0,s=0),s-=h/2,i-=d/2;for(var m=1;m<=n.stripeBackgroundN;m++){var g=e.pstyle("stripe-"+m+"-background-size").value,p=e.pstyle("stripe-"+m+"-background-color").value,y=e.pstyle("stripe-"+m+"-background-opacity").value*t,b=g/100;b+u>1&&(b=1-u),!(g===0||u>=1||u+b>1)&&(r.beginPath(),r.rect(i,s+d*u,h,d*b),r.closePath(),this.colorFillStyle(r,p[0],p[1],p[2],y),r.fill(),u+=b)}r.restore()};var xr={},zy=100;xr.getPixelRatio=function(){var r=this.data.contexts[0];if(this.forcedPixelRatio!=null)return this.forcedPixelRatio;var e=this.cy.window(),t=r.backingStorePixelRatio||r.webkitBackingStorePixelRatio||r.mozBackingStorePixelRatio||r.msBackingStorePixelRatio||r.oBackingStorePixelRatio||r.backingStorePixelRatio||1;return(e.devicePixelRatio||1)/t};xr.paintCache=function(r){for(var e=this.paintCaches=this.paintCaches||[],t=!0,a,n=0;n<e.length;n++)if(a=e[n],a.context===r){t=!1;break}return t&&(a={context:r},e.push(a)),a};xr.createGradientStyleFor=function(r,e,t,a,n){var i,s=this.usePaths(),o=t.pstyle(e+"-gradient-stop-colors").value,l=t.pstyle(e+"-gradient-stop-positions").pfValue;if(a==="radial-gradient")if(t.isEdge()){var u=t.sourceEndpoint(),v=t.targetEndpoint(),f=t.midpoint(),c=Dt(u,f),h=Dt(v,f);i=r.createRadialGradient(f.x,f.y,0,f.x,f.y,Math.max(c,h))}else{var d=s?{x:0,y:0}:t.position(),m=t.paddedWidth(),g=t.paddedHeight();i=r.createRadialGradient(d.x,d.y,0,d.x,d.y,Math.max(m,g))}else if(t.isEdge()){var p=t.sourceEndpoint(),y=t.targetEndpoint();i=r.createLinearGradient(p.x,p.y,y.x,y.y)}else{var b=s?{x:0,y:0}:t.position(),w=t.paddedWidth(),E=t.paddedHeight(),C=w/2,x=E/2,T=t.pstyle("background-gradient-direction").value;switch(T){case"to-bottom":i=r.createLinearGradient(b.x,b.y-x,b.x,b.y+x);break;case"to-top":i=r.createLinearGradient(b.x,b.y+x,b.x,b.y-x);break;case"to-left":i=r.createLinearGradient(b.x+C,b.y,b.x-C,b.y);break;case"to-right":i=r.createLinearGradient(b.x-C,b.y,b.x+C,b.y);break;case"to-bottom-right":case"to-right-bottom":i=r.createLinearGradient(b.x-C,b.y-x,b.x+C,b.y+x);break;case"to-top-right":case"to-right-top":i=r.createLinearGradient(b.x-C,b.y+x,b.x+C,b.y-x);break;case"to-bottom-left":case"to-left-bottom":i=r.createLinearGradient(b.x+C,b.y-x,b.x-C,b.y+x);break;case"to-top-left":case"to-left-top":i=r.createLinearGradient(b.x+C,b.y+x,b.x-C,b.y-x);break}}if(!i)return null;for(var S=l.length===o.length,P=o.length,D=0;D<P;D++)i.addColorStop(S?l[D]:D/(P-1),"rgba("+o[D][0]+","+o[D][1]+","+o[D][2]+","+n+")");return i};xr.gradientFillStyle=function(r,e,t,a){var n=this.createGradientStyleFor(r,"background",e,t,a);if(!n)return null;r.fillStyle=n};xr.colorFillStyle=function(r,e,t,a,n){r.fillStyle="rgba("+e+","+t+","+a+","+n+")"};xr.eleFillStyle=function(r,e,t){var a=e.pstyle("background-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientFillStyle(r,e,a,t);else{var n=e.pstyle("background-color").value;this.colorFillStyle(r,n[0],n[1],n[2],t)}};xr.gradientStrokeStyle=function(r,e,t,a){var n=this.createGradientStyleFor(r,"line",e,t,a);if(!n)return null;r.strokeStyle=n};xr.colorStrokeStyle=function(r,e,t,a,n){r.strokeStyle="rgba("+e+","+t+","+a+","+n+")"};xr.eleStrokeStyle=function(r,e,t){var a=e.pstyle("line-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientStrokeStyle(r,e,a,t);else{var n=e.pstyle("line-color").value;this.colorStrokeStyle(r,n[0],n[1],n[2],t)}};xr.matchCanvasSize=function(r){var e=this,t=e.data,a=e.findContainerClientCoords(),n=a[2],i=a[3],s=e.getPixelRatio(),o=e.motionBlurPxRatio;(r===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE]||r===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG])&&(s=o);var l=n*s,u=i*s,v;if(!(l===e.canvasWidth&&u===e.canvasHeight)){e.fontCaches=null;var f=t.canvasContainer;f.style.width=n+"px",f.style.height=i+"px";for(var c=0;c<e.CANVAS_LAYERS;c++)v=t.canvases[c],v.width=l,v.height=u,v.style.width=n+"px",v.style.height=i+"px";for(var c=0;c<e.BUFFER_COUNT;c++)v=t.bufferCanvases[c],v.width=l,v.height=u,v.style.width=n+"px",v.style.height=i+"px";e.textureMult=1,s<=1&&(v=t.bufferCanvases[e.TEXTURE_BUFFER],e.textureMult=2,v.width=l*e.textureMult,v.height=u*e.textureMult),e.canvasWidth=l,e.canvasHeight=u,e.pixelRatio=s}};xr.renderTo=function(r,e,t,a){this.render({forcedContext:r,forcedZoom:e,forcedPan:t,drawAllLayers:!0,forcedPxRatio:a})};xr.clearCanvas=function(){var r=this,e=r.data;function t(a){a.clearRect(0,0,r.canvasWidth,r.canvasHeight)}t(e.contexts[r.NODE]),t(e.contexts[r.DRAG])};xr.render=function(r){var e=this;r=r||hv();var t=e.cy,a=r.forcedContext,n=r.drawAllLayers,i=r.drawOnlyNodeLayer,s=r.forcedZoom,o=r.forcedPan,l=r.forcedPxRatio===void 0?this.getPixelRatio():r.forcedPxRatio,u=e.data,v=u.canvasNeedsRedraw,f=e.textureOnViewport&&!a&&(e.pinching||e.hoverData.dragging||e.swipePanning||e.data.wheelZooming),c=r.motionBlur!==void 0?r.motionBlur:e.motionBlur,h=e.motionBlurPxRatio,d=t.hasCompoundNodes(),m=e.hoverData.draggingEles,g=!!(e.hoverData.selecting||e.touchData.selecting);c=c&&!a&&e.motionBlurEnabled&&!g;var p=c;a||(e.prevPxRatio!==l&&(e.invalidateContainerClientCoordsCache(),e.matchCanvasSize(e.container),e.redrawHint("eles",!0),e.redrawHint("drag",!0)),e.prevPxRatio=l),!a&&e.motionBlurTimeout&&clearTimeout(e.motionBlurTimeout),c&&(e.mbFrames==null&&(e.mbFrames=0),e.mbFrames++,e.mbFrames<3&&(p=!1),e.mbFrames>e.minMbLowQualFrames&&(e.motionBlurPxRatio=e.mbPxRBlurry)),e.clearingMotionBlur&&(e.motionBlurPxRatio=1),e.textureDrawLastFrame&&!f&&(v[e.NODE]=!0,v[e.SELECT_BOX]=!0);var y=t.style(),b=t.zoom(),w=s!==void 0?s:b,E=t.pan(),C={x:E.x,y:E.y},x={zoom:b,pan:{x:E.x,y:E.y}},T=e.prevViewport,S=T===void 0||x.zoom!==T.zoom||x.pan.x!==T.pan.x||x.pan.y!==T.pan.y;!S&&!(m&&!d)&&(e.motionBlurPxRatio=1),o&&(C=o),w*=l,C.x*=l,C.y*=l;var P=e.getCachedZSortedEles();function D(Z,z,G,H,Q){var ne=Z.globalCompositeOperation;Z.globalCompositeOperation="destination-out",e.colorFillStyle(Z,255,255,255,e.motionBlurTransparency),Z.fillRect(z,G,H,Q),Z.globalCompositeOperation=ne}function A(Z,z){var G,H,Q,ne;!e.clearingMotionBlur&&(Z===u.bufferContexts[e.MOTIONBLUR_BUFFER_NODE]||Z===u.bufferContexts[e.MOTIONBLUR_BUFFER_DRAG])?(G={x:E.x*h,y:E.y*h},H=b*h,Q=e.canvasWidth*h,ne=e.canvasHeight*h):(G=C,H=w,Q=e.canvasWidth,ne=e.canvasHeight),Z.setTransform(1,0,0,1,0,0),z==="motionBlur"?D(Z,0,0,Q,ne):!a&&(z===void 0||z)&&Z.clearRect(0,0,Q,ne),n||(Z.translate(G.x,G.y),Z.scale(H,H)),o&&Z.translate(o.x,o.y),s&&Z.scale(s,s)}if(f||(e.textureDrawLastFrame=!1),f){if(e.textureDrawLastFrame=!0,!e.textureCache){e.textureCache={},e.textureCache.bb=t.mutableElements().boundingBox(),e.textureCache.texture=e.data.bufferCanvases[e.TEXTURE_BUFFER];var B=e.data.bufferContexts[e.TEXTURE_BUFFER];B.setTransform(1,0,0,1,0,0),B.clearRect(0,0,e.canvasWidth*e.textureMult,e.canvasHeight*e.textureMult),e.render({forcedContext:B,drawOnlyNodeLayer:!0,forcedPxRatio:l*e.textureMult});var x=e.textureCache.viewport={zoom:t.zoom(),pan:t.pan(),width:e.canvasWidth,height:e.canvasHeight};x.mpan={x:(0-x.pan.x)/x.zoom,y:(0-x.pan.y)/x.zoom}}v[e.DRAG]=!1,v[e.NODE]=!1;var R=u.contexts[e.NODE],M=e.textureCache.texture,x=e.textureCache.viewport;R.setTransform(1,0,0,1,0,0),c?D(R,0,0,x.width,x.height):R.clearRect(0,0,x.width,x.height);var L=y.core("outside-texture-bg-color").value,I=y.core("outside-texture-bg-opacity").value;e.colorFillStyle(R,L[0],L[1],L[2],I),R.fillRect(0,0,x.width,x.height);var b=t.zoom();A(R,!1),R.clearRect(x.mpan.x,x.mpan.y,x.width/x.zoom/l,x.height/x.zoom/l),R.drawImage(M,x.mpan.x,x.mpan.y,x.width/x.zoom/l,x.height/x.zoom/l)}else e.textureOnViewport&&!a&&(e.textureCache=null);var O=t.extent(),F=e.pinching||e.hoverData.dragging||e.swipePanning||e.data.wheelZooming||e.hoverData.draggingEles||e.cy.animated(),_=e.hideEdgesOnViewport&&F,N=[];if(N[e.NODE]=!v[e.NODE]&&c&&!e.clearedForMotionBlur[e.NODE]||e.clearingMotionBlur,N[e.NODE]&&(e.clearedForMotionBlur[e.NODE]=!0),N[e.DRAG]=!v[e.DRAG]&&c&&!e.clearedForMotionBlur[e.DRAG]||e.clearingMotionBlur,N[e.DRAG]&&(e.clearedForMotionBlur[e.DRAG]=!0),v[e.NODE]||n||i||N[e.NODE]){var q=c&&!N[e.NODE]&&h!==1,R=a||(q?e.data.bufferContexts[e.MOTIONBLUR_BUFFER_NODE]:u.contexts[e.NODE]),U=c&&!q?"motionBlur":void 0;A(R,U),_?e.drawCachedNodes(R,P.nondrag,l,O):e.drawLayeredElements(R,P.nondrag,l,O),e.debug&&e.drawDebugPoints(R,P.nondrag),!n&&!c&&(v[e.NODE]=!1)}if(!i&&(v[e.DRAG]||n||N[e.DRAG])){var q=c&&!N[e.DRAG]&&h!==1,R=a||(q?e.data.bufferContexts[e.MOTIONBLUR_BUFFER_DRAG]:u.contexts[e.DRAG]);A(R,c&&!q?"motionBlur":void 0),_?e.drawCachedNodes(R,P.drag,l,O):e.drawCachedElements(R,P.drag,l,O),e.debug&&e.drawDebugPoints(R,P.drag),!n&&!c&&(v[e.DRAG]=!1)}if(this.drawSelectionRectangle(r,A),c&&h!==1){var X=u.contexts[e.NODE],j=e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE],J=u.contexts[e.DRAG],re=e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG],ae=function(z,G,H){z.setTransform(1,0,0,1,0,0),H||!p?z.clearRect(0,0,e.canvasWidth,e.canvasHeight):D(z,0,0,e.canvasWidth,e.canvasHeight);var Q=h;z.drawImage(G,0,0,e.canvasWidth*Q,e.canvasHeight*Q,0,0,e.canvasWidth,e.canvasHeight)};(v[e.NODE]||N[e.NODE])&&(ae(X,j,N[e.NODE]),v[e.NODE]=!1),(v[e.DRAG]||N[e.DRAG])&&(ae(J,re,N[e.DRAG]),v[e.DRAG]=!1)}e.prevViewport=x,e.clearingMotionBlur&&(e.clearingMotionBlur=!1,e.motionBlurCleared=!0,e.motionBlur=!0),c&&(e.motionBlurTimeout=setTimeout(function(){e.motionBlurTimeout=null,e.clearedForMotionBlur[e.NODE]=!1,e.clearedForMotionBlur[e.DRAG]=!1,e.motionBlur=!1,e.clearingMotionBlur=!f,e.mbFrames=0,v[e.NODE]=!0,v[e.DRAG]=!0,e.redraw()},zy)),a||t.emit("render")};var ha;xr.drawSelectionRectangle=function(r,e){var t=this,a=t.cy,n=t.data,i=a.style(),s=r.drawOnlyNodeLayer,o=r.drawAllLayers,l=n.canvasNeedsRedraw,u=r.forcedContext;if(t.showFps||!s&&l[t.SELECT_BOX]&&!o){var v=u||n.contexts[t.SELECT_BOX];if(e(v),t.selection[4]==1&&(t.hoverData.selecting||t.touchData.selecting)){var f=t.cy.zoom(),c=i.core("selection-box-border-width").value/f;v.lineWidth=c,v.fillStyle="rgba("+i.core("selection-box-color").value[0]+","+i.core("selection-box-color").value[1]+","+i.core("selection-box-color").value[2]+","+i.core("selection-box-opacity").value+")",v.fillRect(t.selection[0],t.selection[1],t.selection[2]-t.selection[0],t.selection[3]-t.selection[1]),c>0&&(v.strokeStyle="rgba("+i.core("selection-box-border-color").value[0]+","+i.core("selection-box-border-color").value[1]+","+i.core("selection-box-border-color").value[2]+","+i.core("selection-box-opacity").value+")",v.strokeRect(t.selection[0],t.selection[1],t.selection[2]-t.selection[0],t.selection[3]-t.selection[1]))}if(n.bgActivePosistion&&!t.hoverData.selecting){var f=t.cy.zoom(),h=n.bgActivePosistion;v.fillStyle="rgba("+i.core("active-bg-color").value[0]+","+i.core("active-bg-color").value[1]+","+i.core("active-bg-color").value[2]+","+i.core("active-bg-opacity").value+")",v.beginPath(),v.arc(h.x,h.y,i.core("active-bg-size").pfValue/f,0,2*Math.PI),v.fill()}var d=t.lastRedrawTime;if(t.showFps&&d){d=Math.round(d);var m=Math.round(1e3/d),g="1 frame = "+d+" ms = "+m+" fps";if(v.setTransform(1,0,0,1,0,0),v.fillStyle="rgba(255, 0, 0, 0.75)",v.strokeStyle="rgba(255, 0, 0, 0.75)",v.font="30px Arial",!ha){var p=v.measureText(g);ha=p.actualBoundingBoxAscent}v.fillText(g,0,ha);var y=60;v.strokeRect(0,ha+10,250,20),v.fillRect(0,ha+10,250*Math.min(m/y,1),20)}o||(l[t.SELECT_BOX]=!1)}};function _l(r,e,t){var a=r.createShader(e);if(r.shaderSource(a,t),r.compileShader(a),!r.getShaderParameter(a,r.COMPILE_STATUS))throw new Error(r.getShaderInfoLog(a));return a}function Fy(r,e,t){var a=_l(r,r.VERTEX_SHADER,e),n=_l(r,r.FRAGMENT_SHADER,t),i=r.createProgram();if(r.attachShader(i,a),r.attachShader(i,n),r.linkProgram(i),!r.getProgramParameter(i,r.LINK_STATUS))throw new Error("Could not initialize shaders");return i}function Vy(r,e,t){t===void 0&&(t=e);var a=r.makeOffscreenCanvas(e,t),n=a.context=a.getContext("2d");return a.clear=function(){return n.clearRect(0,0,a.width,a.height)},a.clear(),a}function bo(r){var e=r.pixelRatio,t=r.cy.zoom(),a=r.cy.pan();return{zoom:t*e,pan:{x:a.x*e,y:a.y*e}}}function qy(r){var e=r.pixelRatio,t=r.cy.zoom();return t*e}function _y(r,e,t,a,n){var i=a*t+e.x,s=n*t+e.y;return s=Math.round(r.canvasHeight-s),[i,s]}function Gy(r){return r.pstyle("background-fill").value!=="solid"||r.pstyle("background-image").strValue!=="none"?!1:r.pstyle("border-width").value===0||r.pstyle("border-opacity").value===0?!0:r.pstyle("border-style").value==="solid"}function Hy(r,e){if(r.length!==e.length)return!1;for(var t=0;t<r.length;t++)if(r[t]!==e[t])return!1;return!0}function wt(r,e,t){var a=r[0]/255,n=r[1]/255,i=r[2]/255,s=e,o=t||new Array(4);return o[0]=a*s,o[1]=n*s,o[2]=i*s,o[3]=s,o}function Vt(r,e){var t=e||new Array(4);return t[0]=(r>>0&255)/255,t[1]=(r>>8&255)/255,t[2]=(r>>16&255)/255,t[3]=(r>>24&255)/255,t}function Wy(r){return r[0]+(r[1]<<8)+(r[2]<<16)+(r[3]<<24)}function $y(r,e){var t=r.createTexture();return t.buffer=function(a){r.bindTexture(r.TEXTURE_2D,t),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_S,r.CLAMP_TO_EDGE),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_T,r.CLAMP_TO_EDGE),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MAG_FILTER,r.LINEAR),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MIN_FILTER,r.LINEAR_MIPMAP_NEAREST),r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,r.RGBA,r.UNSIGNED_BYTE,a),r.generateMipmap(r.TEXTURE_2D),r.bindTexture(r.TEXTURE_2D,null)},t.deleteTexture=function(){r.deleteTexture(t)},t}function Df(r,e){switch(e){case"float":return[1,r.FLOAT,4];case"vec2":return[2,r.FLOAT,4];case"vec3":return[3,r.FLOAT,4];case"vec4":return[4,r.FLOAT,4];case"int":return[1,r.INT,4];case"ivec2":return[2,r.INT,4]}}function Bf(r,e,t){switch(e){case r.FLOAT:return new Float32Array(t);case r.INT:return new Int32Array(t)}}function Uy(r,e,t,a,n,i){switch(e){case r.FLOAT:return new Float32Array(t.buffer,i*a,n);case r.INT:return new Int32Array(t.buffer,i*a,n)}}function Ky(r,e,t,a){var n=Df(r,e),i=rr(n,2),s=i[0],o=i[1],l=Bf(r,o,a),u=r.createBuffer();return r.bindBuffer(r.ARRAY_BUFFER,u),r.bufferData(r.ARRAY_BUFFER,l,r.STATIC_DRAW),o===r.FLOAT?r.vertexAttribPointer(t,s,o,!1,0,0):o===r.INT&&r.vertexAttribIPointer(t,s,o,0,0),r.enableVertexAttribArray(t),r.bindBuffer(r.ARRAY_BUFFER,null),u}function Fr(r,e,t,a){var n=Df(r,t),i=rr(n,3),s=i[0],o=i[1],l=i[2],u=Bf(r,o,e*s),v=s*l,f=r.createBuffer();r.bindBuffer(r.ARRAY_BUFFER,f),r.bufferData(r.ARRAY_BUFFER,e*v,r.DYNAMIC_DRAW),r.enableVertexAttribArray(a),o===r.FLOAT?r.vertexAttribPointer(a,s,o,!1,v,0):o===r.INT&&r.vertexAttribIPointer(a,s,o,v,0),r.vertexAttribDivisor(a,1),r.bindBuffer(r.ARRAY_BUFFER,null);for(var c=new Array(e),h=0;h<e;h++)c[h]=Uy(r,o,u,v,s,h);return f.dataArray=u,f.stride=v,f.size=s,f.getView=function(d){return c[d]},f.setPoint=function(d,m,g){var p=c[d];p[0]=m,p[1]=g},f.bufferSubData=function(d){r.bindBuffer(r.ARRAY_BUFFER,f),d?r.bufferSubData(r.ARRAY_BUFFER,0,u,0,d*s):r.bufferSubData(r.ARRAY_BUFFER,0,u)},f}function Xy(r,e,t){for(var a=9,n=new Float32Array(e*a),i=new Array(e),s=0;s<e;s++){var o=s*a*4;i[s]=new Float32Array(n.buffer,o,a)}var l=r.createBuffer();r.bindBuffer(r.ARRAY_BUFFER,l),r.bufferData(r.ARRAY_BUFFER,n.byteLength,r.DYNAMIC_DRAW);for(var u=0;u<3;u++){var v=t+u;r.enableVertexAttribArray(v),r.vertexAttribPointer(v,3,r.FLOAT,!1,3*12,u*12),r.vertexAttribDivisor(v,1)}return r.bindBuffer(r.ARRAY_BUFFER,null),l.getMatrixView=function(f){return i[f]},l.setData=function(f,c){i[c].set(f,0)},l.bufferSubData=function(){r.bindBuffer(r.ARRAY_BUFFER,l),r.bufferSubData(r.ARRAY_BUFFER,0,n)},l}function Yy(r){var e=r.createFramebuffer();r.bindFramebuffer(r.FRAMEBUFFER,e);var t=r.createTexture();return r.bindTexture(r.TEXTURE_2D,t),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MIN_FILTER,r.LINEAR),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_S,r.CLAMP_TO_EDGE),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_T,r.CLAMP_TO_EDGE),r.framebufferTexture2D(r.FRAMEBUFFER,r.COLOR_ATTACHMENT0,r.TEXTURE_2D,t,0),r.bindFramebuffer(r.FRAMEBUFFER,null),e.setFramebufferAttachmentSizes=function(a,n){r.bindTexture(r.TEXTURE_2D,t),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,a,n,0,r.RGBA,r.UNSIGNED_BYTE,null)},e}var Gl=typeof Float32Array<"u"?Float32Array:Array;Math.hypot||(Math.hypot=function(){for(var r=0,e=arguments.length;e--;)r+=arguments[e]*arguments[e];return Math.sqrt(r)});function Es(){var r=new Gl(9);return Gl!=Float32Array&&(r[1]=0,r[2]=0,r[3]=0,r[5]=0,r[6]=0,r[7]=0),r[0]=1,r[4]=1,r[8]=1,r}function Hl(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=1,r[5]=0,r[6]=0,r[7]=0,r[8]=1,r}function Zy(r,e,t){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=t[0],h=t[1],d=t[2],m=t[3],g=t[4],p=t[5],y=t[6],b=t[7],w=t[8];return r[0]=c*a+h*s+d*u,r[1]=c*n+h*o+d*v,r[2]=c*i+h*l+d*f,r[3]=m*a+g*s+p*u,r[4]=m*n+g*o+p*v,r[5]=m*i+g*l+p*f,r[6]=y*a+b*s+w*u,r[7]=y*n+b*o+w*v,r[8]=y*i+b*l+w*f,r}function yn(r,e,t){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=t[0],h=t[1];return r[0]=a,r[1]=n,r[2]=i,r[3]=s,r[4]=o,r[5]=l,r[6]=c*a+h*s+u,r[7]=c*n+h*o+v,r[8]=c*i+h*l+f,r}function Wl(r,e,t){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=Math.sin(t),h=Math.cos(t);return r[0]=h*a+c*s,r[1]=h*n+c*o,r[2]=h*i+c*l,r[3]=h*s-c*a,r[4]=h*o-c*n,r[5]=h*l-c*i,r[6]=u,r[7]=v,r[8]=f,r}function Ws(r,e,t){var a=t[0],n=t[1];return r[0]=a*e[0],r[1]=a*e[1],r[2]=a*e[2],r[3]=n*e[3],r[4]=n*e[4],r[5]=n*e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r}function Qy(r,e,t){return r[0]=2/e,r[1]=0,r[2]=0,r[3]=0,r[4]=-2/t,r[5]=0,r[6]=-1,r[7]=1,r[8]=1,r}var Jy=function(){function r(e,t,a,n){dt(this,r),this.debugID=Math.floor(Math.random()*1e4),this.r=e,this.texSize=t,this.texRows=a,this.texHeight=Math.floor(t/a),this.enableWrapping=!0,this.locked=!1,this.texture=null,this.needsBuffer=!0,this.freePointer={x:0,row:0},this.keyToLocation=new Map,this.canvas=n(e,t,t),this.scratch=n(e,t,this.texHeight,"scratch")}return ht(r,[{key:"lock",value:function(){this.locked=!0}},{key:"getKeys",value:function(){return new Set(this.keyToLocation.keys())}},{key:"getScale",value:function(t){var a=t.w,n=t.h,i=this.texHeight,s=this.texSize,o=i/n,l=a*o,u=n*o;return l>s&&(o=s/a,l=a*o,u=n*o),{scale:o,texW:l,texH:u}}},{key:"draw",value:function(t,a,n){var i=this;if(this.locked)throw new Error("can't draw, atlas is locked");var s=this.texSize,o=this.texRows,l=this.texHeight,u=this.getScale(a),v=u.scale,f=u.texW,c=u.texH,h=function(b,w){if(n&&w){var E=w.context,C=b.x,x=b.row,T=C,S=l*x;E.save(),E.translate(T,S),E.scale(v,v),n(E,a),E.restore()}},d=[null,null],m=function(){h(i.freePointer,i.canvas),d[0]={x:i.freePointer.x,y:i.freePointer.row*l,w:f,h:c},d[1]={x:i.freePointer.x+f,y:i.freePointer.row*l,w:0,h:c},i.freePointer.x+=f,i.freePointer.x==s&&(i.freePointer.x=0,i.freePointer.row++)},g=function(){var b=i.scratch,w=i.canvas;b.clear(),h({x:0,row:0},b);var E=s-i.freePointer.x,C=f-E,x=l;{var T=i.freePointer.x,S=i.freePointer.row*l,P=E;w.context.drawImage(b,0,0,P,x,T,S,P,x),d[0]={x:T,y:S,w:P,h:c}}{var D=E,A=(i.freePointer.row+1)*l,B=C;w&&w.context.drawImage(b,D,0,B,x,0,A,B,x),d[1]={x:0,y:A,w:B,h:c}}i.freePointer.x=C,i.freePointer.row++},p=function(){i.freePointer.x=0,i.freePointer.row++};if(this.freePointer.x+f<=s)m();else{if(this.freePointer.row>=o-1)return!1;this.freePointer.x===s?(p(),m()):this.enableWrapping?g():(p(),m())}return this.keyToLocation.set(t,d),this.needsBuffer=!0,d}},{key:"getOffsets",value:function(t){return this.keyToLocation.get(t)}},{key:"isEmpty",value:function(){return this.freePointer.x===0&&this.freePointer.row===0}},{key:"canFit",value:function(t){if(this.locked)return!1;var a=this.texSize,n=this.texRows,i=this.getScale(t),s=i.texW;return this.freePointer.x+s>a?this.freePointer.row<n-1:!0}},{key:"bufferIfNeeded",value:function(t){this.texture||(this.texture=$y(t,this.debugID)),this.needsBuffer&&(this.texture.buffer(this.canvas),this.needsBuffer=!1,this.locked&&(this.canvas=null,this.scratch=null))}},{key:"dispose",value:function(){this.texture&&(this.texture.deleteTexture(),this.texture=null),this.canvas=null,this.scratch=null,this.locked=!0}}])}(),jy=function(){function r(e,t,a,n){dt(this,r),this.r=e,this.texSize=t,this.texRows=a,this.createTextureCanvas=n,this.atlases=[],this.styleKeyToAtlas=new Map,this.markedKeys=new Set}return ht(r,[{key:"getKeys",value:function(){return new Set(this.styleKeyToAtlas.keys())}},{key:"_createAtlas",value:function(){var t=this.r,a=this.texSize,n=this.texRows,i=this.createTextureCanvas;return new Jy(t,a,n,i)}},{key:"_getScratchCanvas",value:function(){if(!this.scratch){var t=this.r,a=this.texSize,n=this.texRows,i=this.createTextureCanvas,s=Math.floor(a/n);this.scratch=i(t,a,s,"scratch")}return this.scratch}},{key:"draw",value:function(t,a,n){var i=this.styleKeyToAtlas.get(t);return i||(i=this.atlases[this.atlases.length-1],(!i||!i.canFit(a))&&(i&&i.lock(),i=this._createAtlas(),this.atlases.push(i)),i.draw(t,a,n),this.styleKeyToAtlas.set(t,i)),i}},{key:"getAtlas",value:function(t){return this.styleKeyToAtlas.get(t)}},{key:"hasAtlas",value:function(t){return this.styleKeyToAtlas.has(t)}},{key:"markKeyForGC",value:function(t){this.markedKeys.add(t)}},{key:"gc",value:function(){var t=this,a=this.markedKeys;if(a.size===0){console.log("nothing to garbage collect");return}var n=[],i=new Map,s=null,o=kr(this.atlases),l;try{var u=function(){var f=l.value,c=f.getKeys(),h=em(a,c);if(h.size===0)return n.push(f),c.forEach(function(E){return i.set(E,f)}),1;s||(s=t._createAtlas(),n.push(s));var d=kr(c),m;try{for(d.s();!(m=d.n()).done;){var g=m.value;if(!h.has(g)){var p=f.getOffsets(g),y=rr(p,2),b=y[0],w=y[1];s.canFit({w:b.w+w.w,h:b.h})||(s.lock(),s=t._createAtlas(),n.push(s)),f.canvas&&(t._copyTextureToNewAtlas(g,f,s),i.set(g,s))}}}catch(E){d.e(E)}finally{d.f()}f.dispose()};for(o.s();!(l=o.n()).done;)u()}catch(v){o.e(v)}finally{o.f()}this.atlases=n,this.styleKeyToAtlas=i,this.markedKeys=new Set}},{key:"_copyTextureToNewAtlas",value:function(t,a,n){var i=a.getOffsets(t),s=rr(i,2),o=s[0],l=s[1];if(l.w===0)n.draw(t,o,function(c){c.drawImage(a.canvas,o.x,o.y,o.w,o.h,0,0,o.w,o.h)});else{var u=this._getScratchCanvas();u.clear(),u.context.drawImage(a.canvas,o.x,o.y,o.w,o.h,0,0,o.w,o.h),u.context.drawImage(a.canvas,l.x,l.y,l.w,l.h,o.w,0,l.w,l.h);var v=o.w+l.w,f=o.h;n.draw(t,{w:v,h:f},function(c){c.drawImage(u,0,0,v,f,0,0,v,f)})}}},{key:"getCounts",value:function(){return{keyCount:this.styleKeyToAtlas.size,atlasCount:new Set(this.styleKeyToAtlas.values()).size}}}])}();function em(r,e){return r.intersection?r.intersection(e):new Set(mn(r).filter(function(t){return e.has(t)}))}var rm=function(){function r(e,t){dt(this,r),this.r=e,this.globalOptions=t,this.atlasSize=t.webglTexSize,this.maxAtlasesPerBatch=t.webglTexPerBatch,this.renderTypes=new Map,this.collections=new Map,this.typeAndIdToKey=new Map}return ht(r,[{key:"getAtlasSize",value:function(){return this.atlasSize}},{key:"addAtlasCollection",value:function(t,a){var n=this.globalOptions,i=n.webglTexSize,s=n.createTextureCanvas,o=a.texRows,l=this._cacheScratchCanvas(s),u=new jy(this.r,i,o,l);this.collections.set(t,u)}},{key:"addRenderType",value:function(t,a){var n=a.collection;if(!this.collections.has(n))throw new Error("invalid atlas collection name '".concat(n,"'"));var i=this.collections.get(n),s=pe({type:t,atlasCollection:i},a);this.renderTypes.set(t,s)}},{key:"getRenderTypeOpts",value:function(t){return this.renderTypes.get(t)}},{key:"getAtlasCollection",value:function(t){return this.collections.get(t)}},{key:"_cacheScratchCanvas",value:function(t){var a=-1,n=-1,i=null;return function(s,o,l,u){return u?((!i||o!=a||l!=n)&&(a=o,n=l,i=t(s,o,l)),i):t(s,o,l)}}},{key:"_key",value:function(t,a){return"".concat(t,"-").concat(a)}},{key:"invalidate",value:function(t){var a=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.forceRedraw,s=i===void 0?!1:i,o=n.filterEle,l=o===void 0?function(){return!0}:o,u=n.filterType,v=u===void 0?function(){return!0}:u,f=!1,c=!1,h=kr(t),d;try{for(h.s();!(d=h.n()).done;){var m=d.value;if(l(m)){var g=kr(this.renderTypes.values()),p;try{var y=function(){var w=p.value,E=w.type;if(v(E)){var C=a.collections.get(w.collection),x=w.getKey(m),T=Array.isArray(x)?x:[x];if(s)T.forEach(function(A){return C.markKeyForGC(A)}),c=!0;else{var S=w.getID?w.getID(m):m.id(),P=a._key(E,S),D=a.typeAndIdToKey.get(P);D!==void 0&&!Hy(T,D)&&(f=!0,a.typeAndIdToKey.delete(P),D.forEach(function(A){return C.markKeyForGC(A)}))}}};for(g.s();!(p=g.n()).done;)y()}catch(b){g.e(b)}finally{g.f()}}}}catch(b){h.e(b)}finally{h.f()}return c&&(this.gc(),f=!1),f}},{key:"gc",value:function(){var t=kr(this.collections.values()),a;try{for(t.s();!(a=t.n()).done;){var n=a.value;n.gc()}}catch(i){t.e(i)}finally{t.f()}}},{key:"getOrCreateAtlas",value:function(t,a,n,i){var s=this.renderTypes.get(a),o=this.collections.get(s.collection),l=!1,u=o.draw(i,n,function(c){s.drawClipped?(c.save(),c.beginPath(),c.rect(0,0,n.w,n.h),c.clip(),s.drawElement(c,t,n,!0,!0),c.restore()):s.drawElement(c,t,n,!0,!0),l=!0});if(l){var v=s.getID?s.getID(t):t.id(),f=this._key(a,v);this.typeAndIdToKey.has(f)?this.typeAndIdToKey.get(f).push(i):this.typeAndIdToKey.set(f,[i])}return u}},{key:"getAtlasInfo",value:function(t,a){var n=this,i=this.renderTypes.get(a),s=i.getKey(t),o=Array.isArray(s)?s:[s];return o.map(function(l){var u=i.getBoundingBox(t,l),v=n.getOrCreateAtlas(t,a,u,l),f=v.getOffsets(l),c=rr(f,2),h=c[0],d=c[1];return{atlas:v,tex:h,tex1:h,tex2:d,bb:u}})}},{key:"getDebugInfo",value:function(){var t=[],a=kr(this.collections),n;try{for(a.s();!(n=a.n()).done;){var i=rr(n.value,2),s=i[0],o=i[1],l=o.getCounts(),u=l.keyCount,v=l.atlasCount;t.push({type:s,keyCount:u,atlasCount:v})}}catch(f){a.e(f)}finally{a.f()}return t}}])}(),tm=function(){function r(e){dt(this,r),this.globalOptions=e,this.atlasSize=e.webglTexSize,this.maxAtlasesPerBatch=e.webglTexPerBatch,this.batchAtlases=[]}return ht(r,[{key:"getMaxAtlasesPerBatch",value:function(){return this.maxAtlasesPerBatch}},{key:"getAtlasSize",value:function(){return this.atlasSize}},{key:"getIndexArray",value:function(){return Array.from({length:this.maxAtlasesPerBatch},function(t,a){return a})}},{key:"startBatch",value:function(){this.batchAtlases=[]}},{key:"getAtlasCount",value:function(){return this.batchAtlases.length}},{key:"getAtlases",value:function(){return this.batchAtlases}},{key:"canAddToCurrentBatch",value:function(t){return this.batchAtlases.length===this.maxAtlasesPerBatch?this.batchAtlases.includes(t):!0}},{key:"getAtlasIndexForBatch",value:function(t){var a=this.batchAtlases.indexOf(t);if(a<0){if(this.batchAtlases.length===this.maxAtlasesPerBatch)throw new Error("cannot add more atlases to batch");this.batchAtlases.push(t),a=this.batchAtlases.length-1}return a}}])}(),am=`
  float circleSD(vec2 p, float r) {
    return distance(vec2(0), p) - r; // signed distance
  }
`,nm=`
  float rectangleSD(vec2 p, vec2 b) {
    vec2 d = abs(p)-b;
    return distance(vec2(0),max(d,0.0)) + min(max(d.x,d.y),0.0);
  }
`,im=`
  float roundRectangleSD(vec2 p, vec2 b, vec4 cr) {
    cr.xy = (p.x > 0.0) ? cr.xy : cr.zw;
    cr.x  = (p.y > 0.0) ? cr.x  : cr.y;
    vec2 q = abs(p) - b + cr.x;
    return min(max(q.x, q.y), 0.0) + distance(vec2(0), max(q, 0.0)) - cr.x;
  }
`,sm=`
  float ellipseSD(vec2 p, vec2 ab) {
    p = abs( p ); // symmetry

    // find root with Newton solver
    vec2 q = ab*(p-ab);
    float w = (q.x<q.y)? 1.570796327 : 0.0;
    for( int i=0; i<5; i++ ) {
      vec2 cs = vec2(cos(w),sin(w));
      vec2 u = ab*vec2( cs.x,cs.y);
      vec2 v = ab*vec2(-cs.y,cs.x);
      w = w + dot(p-u,v)/(dot(p-u,u)+dot(v,v));
    }
    
    // compute final point and distance
    float d = length(p-ab*vec2(cos(w),sin(w)));
    
    // return signed distance
    return (dot(p/ab,p/ab)>1.0) ? d : -d;
  }
`,Ea={SCREEN:{name:"screen",screen:!0},PICKING:{name:"picking",picking:!0}},An={IGNORE:1,USE_BB:2},Cs=0,$l=1,Ul=2,Ts=3,qt=4,sn=5,ga=6,pa=7,om=function(){function r(e,t,a){dt(this,r),this.r=e,this.gl=t,this.maxInstances=a.webglBatchSize,this.atlasSize=a.webglTexSize,this.bgColor=a.bgColor,this.debug=a.webglDebug,this.batchDebugInfo=[],a.enableWrapping=!0,a.createTextureCanvas=Vy,this.atlasManager=new rm(e,a),this.batchManager=new tm(a),this.simpleShapeOptions=new Map,this.program=this._createShaderProgram(Ea.SCREEN),this.pickingProgram=this._createShaderProgram(Ea.PICKING),this.vao=this._createVAO()}return ht(r,[{key:"addAtlasCollection",value:function(t,a){this.atlasManager.addAtlasCollection(t,a)}},{key:"addTextureAtlasRenderType",value:function(t,a){this.atlasManager.addRenderType(t,a)}},{key:"addSimpleShapeRenderType",value:function(t,a){this.simpleShapeOptions.set(t,a)}},{key:"invalidate",value:function(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=a.type,i=this.atlasManager;return n?i.invalidate(t,{filterType:function(o){return o===n},forceRedraw:!0}):i.invalidate(t)}},{key:"gc",value:function(){this.atlasManager.gc()}},{key:"_createShaderProgram",value:function(t){var a=this.gl,n=`#version 300 es
      precision highp float;

      uniform mat3 uPanZoomMatrix;
      uniform int  uAtlasSize;
      
      // instanced
      in vec2 aPosition; // a vertex from the unit square
      
      in mat3 aTransform; // used to transform verticies, eg into a bounding box
      in int aVertType; // the type of thing we are rendering

      // the z-index that is output when using picking mode
      in vec4 aIndex;
      
      // For textures
      in int aAtlasId; // which shader unit/atlas to use
      in vec4 aTex; // x/y/w/h of texture in atlas

      // for edges
      in vec4 aPointAPointB;
      in vec4 aPointCPointD;
      in vec2 aLineWidth; // also used for node border width

      // simple shapes
      in vec4 aCornerRadius; // for round-rectangle [top-right, bottom-right, top-left, bottom-left]
      in vec4 aColor; // also used for edges
      in vec4 aBorderColor; // aLineWidth is used for border width

      // output values passed to the fragment shader
      out vec2 vTexCoord;
      out vec4 vColor;
      out vec2 vPosition;
      // flat values are not interpolated
      flat out int vAtlasId; 
      flat out int vVertType;
      flat out vec2 vTopRight;
      flat out vec2 vBotLeft;
      flat out vec4 vCornerRadius;
      flat out vec4 vBorderColor;
      flat out vec2 vBorderWidth;
      flat out vec4 vIndex;
      
      void main(void) {
        int vid = gl_VertexID;
        vec2 position = aPosition; // TODO make this a vec3, simplifies some code below

        if(aVertType == `.concat(Cs,`) {
          float texX = aTex.x; // texture coordinates
          float texY = aTex.y;
          float texW = aTex.z;
          float texH = aTex.w;

          if(vid == 1 || vid == 2 || vid == 4) {
            texX += texW;
          }
          if(vid == 2 || vid == 4 || vid == 5) {
            texY += texH;
          }

          float d = float(uAtlasSize);
          vTexCoord = vec2(texX / d, texY / d); // tex coords must be between 0 and 1

          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
        }
        else if(aVertType == `).concat(qt," || aVertType == ").concat(pa,` 
             || aVertType == `).concat(sn," || aVertType == ").concat(ga,`) { // simple shapes

          // the bounding box is needed by the fragment shader
          vBotLeft  = (aTransform * vec3(0, 0, 1)).xy; // flat
          vTopRight = (aTransform * vec3(1, 1, 1)).xy; // flat
          vPosition = (aTransform * vec3(position, 1)).xy; // will be interpolated

          // calculations are done in the fragment shader, just pass these along
          vColor = aColor;
          vCornerRadius = aCornerRadius;
          vBorderColor = aBorderColor;
          vBorderWidth = aLineWidth;

          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
        }
        else if(aVertType == `).concat($l,`) {
          vec2 source = aPointAPointB.xy;
          vec2 target = aPointAPointB.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          // stretch the unit square into a long skinny rectangle
          vec2 xBasis = target - source;
          vec2 yBasis = normalize(vec2(-xBasis.y, xBasis.x));
          vec2 point = source + xBasis * position.x + yBasis * aLineWidth[0] * position.y;

          gl_Position = vec4(uPanZoomMatrix * vec3(point, 1.0), 1.0);
          vColor = aColor;
        } 
        else if(aVertType == `).concat(Ul,`) {
          vec2 pointA = aPointAPointB.xy;
          vec2 pointB = aPointAPointB.zw;
          vec2 pointC = aPointCPointD.xy;
          vec2 pointD = aPointCPointD.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          vec2 p0, p1, p2, pos;
          if(position.x == 0.0) { // The left side of the unit square
            p0 = pointA;
            p1 = pointB;
            p2 = pointC;
            pos = position;
          } else { // The right side of the unit square, use same approach but flip the geometry upside down
            p0 = pointD;
            p1 = pointC;
            p2 = pointB;
            pos = vec2(0.0, -position.y);
          }

          vec2 p01 = p1 - p0;
          vec2 p12 = p2 - p1;
          vec2 p21 = p1 - p2;

          // Find the normal vector.
          vec2 tangent = normalize(normalize(p12) + normalize(p01));
          vec2 normal = vec2(-tangent.y, tangent.x);

          // Find the vector perpendicular to p0 -> p1.
          vec2 p01Norm = normalize(vec2(-p01.y, p01.x));

          // Determine the bend direction.
          float sigma = sign(dot(p01 + p21, normal));
          float width = aLineWidth[0];

          if(sign(pos.y) == -sigma) {
            // This is an intersecting vertex. Adjust the position so that there's no overlap.
            vec2 point = 0.5 * width * normal * -sigma / dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          } else {
            // This is a non-intersecting vertex. Treat it like a mitre join.
            vec2 point = 0.5 * width * normal * sigma * dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          }

          vColor = aColor;
        } 
        else if(aVertType == `).concat(Ts,` && vid < 3) {
          // massage the first triangle into an edge arrow
          if(vid == 0)
            position = vec2(-0.15, -0.3);
          if(vid == 1)
            position = vec2(  0.0,  0.0);
          if(vid == 2)
            position = vec2( 0.15, -0.3);

          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
          vColor = aColor;
        }
        else {
          gl_Position = vec4(2.0, 0.0, 0.0, 1.0); // discard vertex by putting it outside webgl clip space
        }

        vAtlasId = aAtlasId;
        vVertType = aVertType;
        vIndex = aIndex;
      }
    `),i=this.batchManager.getIndexArray(),s=`#version 300 es
      precision highp float;

      // declare texture unit for each texture atlas in the batch
      `.concat(i.map(function(u){return"uniform sampler2D uTexture".concat(u,";")}).join(`
	`),`

      uniform vec4 uBGColor;
      uniform float uZoom;

      in vec2 vTexCoord;
      in vec4 vColor;
      in vec2 vPosition; // model coordinates

      flat in int vAtlasId;
      flat in vec4 vIndex;
      flat in int vVertType;
      flat in vec2 vTopRight;
      flat in vec2 vBotLeft;
      flat in vec4 vCornerRadius;
      flat in vec4 vBorderColor;
      flat in vec2 vBorderWidth;

      out vec4 outColor;

      `).concat(am,`
      `).concat(nm,`
      `).concat(im,`
      `).concat(sm,`

      vec4 blend(vec4 top, vec4 bot) { // blend colors with premultiplied alpha
        return vec4( 
          top.rgb + (bot.rgb * (1.0 - top.a)),
          top.a   + (bot.a   * (1.0 - top.a)) 
        );
      }

      vec4 distInterp(vec4 cA, vec4 cB, float d) { // interpolate color using Signed Distance
        // scale to the zoom level so that borders don't look blurry when zoomed in
        // note 1.5 is an aribitrary value chosen because it looks good
        return mix(cA, cB, 1.0 - smoothstep(0.0, 1.5 / uZoom, abs(d))); 
      }

      void main(void) {
        if(vVertType == `).concat(Cs,`) {
          // look up the texel from the texture unit
          `).concat(i.map(function(u){return"if(vAtlasId == ".concat(u,") outColor = texture(uTexture").concat(u,", vTexCoord);")}).join(`
	else `),`
        } 
        else if(vVertType == `).concat(Ts,`) {
          // mimics how canvas renderer uses context.globalCompositeOperation = 'destination-out';
          outColor = blend(vColor, uBGColor);
          outColor.a = 1.0; // make opaque, masks out line under arrow
        }
        else if(vVertType == `).concat(qt,` && vBorderWidth == vec2(0.0)) { // simple rectangle with no border
          outColor = vColor; // unit square is already transformed to the rectangle, nothing else needs to be done
        }
        else if(vVertType == `).concat(qt," || vVertType == ").concat(pa,` 
          || vVertType == `).concat(sn," || vVertType == ").concat(ga,`) { // use SDF

          float outerBorder = vBorderWidth[0];
          float innerBorder = vBorderWidth[1];
          float borderPadding = outerBorder * 2.0;
          float w = vTopRight.x - vBotLeft.x - borderPadding;
          float h = vTopRight.y - vBotLeft.y - borderPadding;
          vec2 b = vec2(w/2.0, h/2.0); // half width, half height
          vec2 p = vPosition - vec2(vTopRight.x - b[0] - outerBorder, vTopRight.y - b[1] - outerBorder); // translate to center

          float d; // signed distance
          if(vVertType == `).concat(qt,`) {
            d = rectangleSD(p, b);
          } else if(vVertType == `).concat(pa,` && w == h) {
            d = circleSD(p, b.x); // faster than ellipse
          } else if(vVertType == `).concat(pa,`) {
            d = ellipseSD(p, b);
          } else {
            d = roundRectangleSD(p, b, vCornerRadius.wzyx);
          }

          // use the distance to interpolate a color to smooth the edges of the shape, doesn't need multisampling
          // we must smooth colors inwards, because we can't change pixels outside the shape's bounding box
          if(d > 0.0) {
            if(d > outerBorder) {
              discard;
            } else {
              outColor = distInterp(vBorderColor, vec4(0), d - outerBorder);
            }
          } else {
            if(d > innerBorder) {
              vec4 outerColor = outerBorder == 0.0 ? vec4(0) : vBorderColor;
              vec4 innerBorderColor = blend(vBorderColor, vColor);
              outColor = distInterp(innerBorderColor, outerColor, d);
            } 
            else {
              vec4 outerColor;
              if(innerBorder == 0.0 && outerBorder == 0.0) {
                outerColor = vec4(0);
              } else if(innerBorder == 0.0) {
                outerColor = vBorderColor;
              } else {
                outerColor = blend(vBorderColor, vColor);
              }
              outColor = distInterp(vColor, outerColor, d - innerBorder);
            }
          }
        }
        else {
          outColor = vColor;
        }

        `).concat(t.picking?`if(outColor.a == 0.0) discard;
             else outColor = vIndex;`:"",`
      }
    `),o=Fy(a,n,s);o.aPosition=a.getAttribLocation(o,"aPosition"),o.aIndex=a.getAttribLocation(o,"aIndex"),o.aVertType=a.getAttribLocation(o,"aVertType"),o.aTransform=a.getAttribLocation(o,"aTransform"),o.aAtlasId=a.getAttribLocation(o,"aAtlasId"),o.aTex=a.getAttribLocation(o,"aTex"),o.aPointAPointB=a.getAttribLocation(o,"aPointAPointB"),o.aPointCPointD=a.getAttribLocation(o,"aPointCPointD"),o.aLineWidth=a.getAttribLocation(o,"aLineWidth"),o.aColor=a.getAttribLocation(o,"aColor"),o.aCornerRadius=a.getAttribLocation(o,"aCornerRadius"),o.aBorderColor=a.getAttribLocation(o,"aBorderColor"),o.uPanZoomMatrix=a.getUniformLocation(o,"uPanZoomMatrix"),o.uAtlasSize=a.getUniformLocation(o,"uAtlasSize"),o.uBGColor=a.getUniformLocation(o,"uBGColor"),o.uZoom=a.getUniformLocation(o,"uZoom"),o.uTextures=[];for(var l=0;l<this.batchManager.getMaxAtlasesPerBatch();l++)o.uTextures.push(a.getUniformLocation(o,"uTexture".concat(l)));return o}},{key:"_createVAO",value:function(){var t=[0,0,1,0,1,1,0,0,1,1,0,1];this.vertexCount=t.length/2;var a=this.maxInstances,n=this.gl,i=this.program,s=n.createVertexArray();return n.bindVertexArray(s),Ky(n,"vec2",i.aPosition,t),this.transformBuffer=Xy(n,a,i.aTransform),this.indexBuffer=Fr(n,a,"vec4",i.aIndex),this.vertTypeBuffer=Fr(n,a,"int",i.aVertType),this.atlasIdBuffer=Fr(n,a,"int",i.aAtlasId),this.texBuffer=Fr(n,a,"vec4",i.aTex),this.pointAPointBBuffer=Fr(n,a,"vec4",i.aPointAPointB),this.pointCPointDBuffer=Fr(n,a,"vec4",i.aPointCPointD),this.lineWidthBuffer=Fr(n,a,"vec2",i.aLineWidth),this.colorBuffer=Fr(n,a,"vec4",i.aColor),this.cornerRadiusBuffer=Fr(n,a,"vec4",i.aCornerRadius),this.borderColorBuffer=Fr(n,a,"vec4",i.aBorderColor),n.bindVertexArray(null),s}},{key:"buffers",get:function(){var t=this;return this._buffers||(this._buffers=Object.keys(this).filter(function(a){return at(a,"Buffer")}).map(function(a){return t[a]})),this._buffers}},{key:"startFrame",value:function(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ea.SCREEN;this.panZoomMatrix=t,this.renderTarget=a,this.batchDebugInfo=[],this.wrappedCount=0,this.simpleCount=0,this.startBatch()}},{key:"startBatch",value:function(){this.instanceCount=0,this.batchManager.startBatch()}},{key:"endFrame",value:function(){this.endBatch()}},{key:"_isVisible",value:function(t,a){return t.visible()?a&&a.isVisible?a.isVisible(t):!0:!1}},{key:"drawTexture",value:function(t,a,n){var i=this.atlasManager,s=this.batchManager,o=i.getRenderTypeOpts(n);if(this._isVisible(t,o)){if(this.renderTarget.picking&&o.getTexPickingMode){var l=o.getTexPickingMode(t);if(l===An.IGNORE)return;if(l==An.USE_BB){this.drawPickingRectangle(t,a,n);return}}var u=i.getAtlasInfo(t,n),v=kr(u),f;try{for(v.s();!(f=v.n()).done;){var c=f.value,h=c.atlas,d=c.tex1,m=c.tex2;s.canAddToCurrentBatch(h)||this.endBatch();for(var g=s.getAtlasIndexForBatch(h),p=0,y=[[d,!0],[m,!1]];p<y.length;p++){var b=rr(y[p],2),w=b[0],E=b[1];if(w.w!=0){var C=this.instanceCount;this.vertTypeBuffer.getView(C)[0]=Cs;var x=this.indexBuffer.getView(C);Vt(a,x);var T=this.atlasIdBuffer.getView(C);T[0]=g;var S=this.texBuffer.getView(C);S[0]=w.x,S[1]=w.y,S[2]=w.w,S[3]=w.h;var P=this.transformBuffer.getMatrixView(C);this.setTransformMatrix(t,P,o,c,E),this.instanceCount++,E||this.wrappedCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}catch(D){v.e(D)}finally{v.f()}}}},{key:"setTransformMatrix",value:function(t,a,n,i){var s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,o=0;if(n.shapeProps&&n.shapeProps.padding&&(o=t.pstyle(n.shapeProps.padding).pfValue),i){var l=i.bb,u=i.tex1,v=i.tex2,f=u.w/(u.w+v.w);s||(f=1-f);var c=this._getAdjustedBB(l,o,s,f);this._applyTransformMatrix(a,c,n,t)}else{var h=n.getBoundingBox(t),d=this._getAdjustedBB(h,o,!0,1);this._applyTransformMatrix(a,d,n,t)}}},{key:"_applyTransformMatrix",value:function(t,a,n,i){var s,o;Hl(t);var l=n.getRotation?n.getRotation(i):0;if(l!==0){var u=n.getRotationPoint(i),v=u.x,f=u.y;yn(t,t,[v,f]),Wl(t,t,l);var c=n.getRotationOffset(i);s=c.x+(a.xOffset||0),o=c.y+(a.yOffset||0)}else s=a.x1,o=a.y1;yn(t,t,[s,o]),Ws(t,t,[a.w,a.h])}},{key:"_getAdjustedBB",value:function(t,a,n,i){var s=t.x1,o=t.y1,l=t.w,u=t.h,v=t.yOffset;a&&(s-=a,o-=a,l+=2*a,u+=2*a);var f=0,c=l*i;return n&&i<1?l=c:!n&&i<1&&(f=l-c,s+=f,l=c),{x1:s,y1:o,w:l,h:u,xOffset:f,yOffset:v}}},{key:"drawPickingRectangle",value:function(t,a,n){var i=this.atlasManager.getRenderTypeOpts(n),s=this.instanceCount;this.vertTypeBuffer.getView(s)[0]=qt;var o=this.indexBuffer.getView(s);Vt(a,o);var l=this.colorBuffer.getView(s);wt([0,0,0],1,l);var u=this.transformBuffer.getMatrixView(s);this.setTransformMatrix(t,u,i),this.simpleCount++,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}},{key:"drawNode",value:function(t,a,n){var i=this.simpleShapeOptions.get(n);if(this._isVisible(t,i)){var s=i.shapeProps,o=this._getVertTypeForShape(t,s.shape);if(o===void 0||i.isSimple&&!i.isSimple(t)){this.drawTexture(t,a,n);return}var l=this.instanceCount;if(this.vertTypeBuffer.getView(l)[0]=o,o===sn||o===ga){var u=i.getBoundingBox(t),v=this._getCornerRadius(t,s.radius,u),f=this.cornerRadiusBuffer.getView(l);f[0]=v,f[1]=v,f[2]=v,f[3]=v,o===ga&&(f[0]=0,f[2]=0)}var c=this.indexBuffer.getView(l);Vt(a,c);var h=t.pstyle(s.color).value,d=t.pstyle(s.opacity).value,m=this.colorBuffer.getView(l);wt(h,d,m);var g=this.lineWidthBuffer.getView(l);if(g[0]=0,g[1]=0,s.border){var p=t.pstyle("border-width").value;if(p>0){var y=t.pstyle("border-color").value,b=t.pstyle("border-opacity").value,w=this.borderColorBuffer.getView(l);wt(y,b,w);var E=t.pstyle("border-position").value;if(E==="inside")g[0]=0,g[1]=-p;else if(E==="outside")g[0]=p,g[1]=0;else{var C=p/2;g[0]=C,g[1]=-C}}}var x=this.transformBuffer.getMatrixView(l);this.setTransformMatrix(t,x,i),this.simpleCount++,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}},{key:"_getVertTypeForShape",value:function(t,a){var n=t.pstyle(a).value;switch(n){case"rectangle":return qt;case"ellipse":return pa;case"roundrectangle":case"round-rectangle":return sn;case"bottom-round-rectangle":return ga;default:return}}},{key:"_getCornerRadius",value:function(t,a,n){var i=n.w,s=n.h;if(t.pstyle(a).value==="auto")return lt(i,s);var o=t.pstyle(a).pfValue,l=i/2,u=s/2;return Math.min(o,u,l)}},{key:"drawEdgeArrow",value:function(t,a,n){if(t.visible()){var i=t._private.rscratch,s,o,l;if(n==="source"?(s=i.arrowStartX,o=i.arrowStartY,l=i.srcArrowAngle):(s=i.arrowEndX,o=i.arrowEndY,l=i.tgtArrowAngle),!(isNaN(s)||s==null||isNaN(o)||o==null||isNaN(l)||l==null)){var u=t.pstyle(n+"-arrow-shape").value;if(u!=="none"){var v=t.pstyle(n+"-arrow-color").value,f=t.pstyle("opacity").value,c=t.pstyle("line-opacity").value,h=f*c,d=t.pstyle("width").pfValue,m=t.pstyle("arrow-scale").value,g=this.r.getArrowWidth(d,m),p=this.instanceCount,y=this.transformBuffer.getMatrixView(p);Hl(y),yn(y,y,[s,o]),Ws(y,y,[g,g]),Wl(y,y,l),this.vertTypeBuffer.getView(p)[0]=Ts;var b=this.indexBuffer.getView(p);Vt(a,b);var w=this.colorBuffer.getView(p);wt(v,h,w),this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}},{key:"drawEdgeLine",value:function(t,a){if(t.visible()){var n=this._getEdgePoints(t);if(n){var i=t.pstyle("opacity").value,s=t.pstyle("line-opacity").value,o=t.pstyle("width").pfValue,l=t.pstyle("line-color").value,u=i*s;if(n.length/2+this.instanceCount>this.maxInstances&&this.endBatch(),n.length==4){var v=this.instanceCount;this.vertTypeBuffer.getView(v)[0]=$l;var f=this.indexBuffer.getView(v);Vt(a,f);var c=this.colorBuffer.getView(v);wt(l,u,c);var h=this.lineWidthBuffer.getView(v);h[0]=o;var d=this.pointAPointBBuffer.getView(v);d[0]=n[0],d[1]=n[1],d[2]=n[2],d[3]=n[3],this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}else for(var m=0;m<n.length-2;m+=2){var g=this.instanceCount;this.vertTypeBuffer.getView(g)[0]=Ul;var p=this.indexBuffer.getView(g);Vt(a,p);var y=this.colorBuffer.getView(g);wt(l,u,y);var b=this.lineWidthBuffer.getView(g);b[0]=o;var w=n[m-2],E=n[m-1],C=n[m],x=n[m+1],T=n[m+2],S=n[m+3],P=n[m+4],D=n[m+5];m==0&&(w=2*C-T+.001,E=2*x-S+.001),m==n.length-4&&(P=2*T-C+.001,D=2*S-x+.001);var A=this.pointAPointBBuffer.getView(g);A[0]=w,A[1]=E,A[2]=C,A[3]=x;var B=this.pointCPointDBuffer.getView(g);B[0]=T,B[1]=S,B[2]=P,B[3]=D,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}},{key:"_getEdgePoints",value:function(t){var a=t._private.rscratch;if(!(a.badLine||a.allpts==null||isNaN(a.allpts[0]))){var n=a.allpts;if(n.length==4)return n;var i=this._getNumSegments(t);return this._getCurveSegmentPoints(n,i)}}},{key:"_getNumSegments",value:function(t){var a=15;return Math.min(Math.max(a,5),this.maxInstances)}},{key:"_getCurveSegmentPoints",value:function(t,a){if(t.length==4)return t;for(var n=Array((a+1)*2),i=0;i<=a;i++)if(i==0)n[0]=t[0],n[1]=t[1];else if(i==a)n[i*2]=t[t.length-2],n[i*2+1]=t[t.length-1];else{var s=i/a;this._setCurvePoint(t,s,n,i*2)}return n}},{key:"_setCurvePoint",value:function(t,a,n,i){if(t.length<=2)n[i]=t[0],n[i+1]=t[1];else{for(var s=Array(t.length-2),o=0;o<s.length;o+=2){var l=(1-a)*t[o]+a*t[o+2],u=(1-a)*t[o+1]+a*t[o+3];s[o]=l,s[o+1]=u}return this._setCurvePoint(s,a,n,i)}}},{key:"endBatch",value:function(){var t=this.gl,a=this.vao,n=this.vertexCount,i=this.instanceCount;if(i!==0){var s=this.renderTarget.picking?this.pickingProgram:this.program;t.useProgram(s),t.bindVertexArray(a);var o=kr(this.buffers),l;try{for(o.s();!(l=o.n()).done;){var u=l.value;u.bufferSubData(i)}}catch(d){o.e(d)}finally{o.f()}for(var v=this.batchManager.getAtlases(),f=0;f<v.length;f++)v[f].bufferIfNeeded(t);for(var c=0;c<v.length;c++)t.activeTexture(t.TEXTURE0+c),t.bindTexture(t.TEXTURE_2D,v[c].texture),t.uniform1i(s.uTextures[c],c);t.uniform1f(s.uZoom,qy(this.r)),t.uniformMatrix3fv(s.uPanZoomMatrix,!1,this.panZoomMatrix),t.uniform1i(s.uAtlasSize,this.batchManager.getAtlasSize());var h=wt(this.bgColor,1);t.uniform4fv(s.uBGColor,h),t.drawArraysInstanced(t.TRIANGLES,0,n,i),t.bindVertexArray(null),t.bindTexture(t.TEXTURE_2D,null),this.debug&&this.batchDebugInfo.push({count:i,atlasCount:v.length}),this.startBatch()}}},{key:"getDebugInfo",value:function(){var t=this.atlasManager.getDebugInfo(),a=t.reduce(function(s,o){return s+o.atlasCount},0),n=this.batchDebugInfo,i=n.reduce(function(s,o){return s+o.count},0);return{atlasInfo:t,totalAtlases:a,wrappedCount:this.wrappedCount,simpleCount:this.simpleCount,batchCount:n.length,batchInfo:n,totalInstances:i}}}])}(),Pf={};Pf.initWebgl=function(r,e){var t=this,a=t.data.contexts[t.WEBGL];r.bgColor=um(t),r.webglTexSize=Math.min(r.webglTexSize,a.getParameter(a.MAX_TEXTURE_SIZE)),r.webglTexRows=Math.min(r.webglTexRows,54),r.webglTexRowsNodes=Math.min(r.webglTexRowsNodes,54),r.webglBatchSize=Math.min(r.webglBatchSize,16384),r.webglTexPerBatch=Math.min(r.webglTexPerBatch,a.getParameter(a.MAX_TEXTURE_IMAGE_UNITS)),t.webglDebug=r.webglDebug,t.webglDebugShowAtlases=r.webglDebugShowAtlases,t.pickingFrameBuffer=Yy(a),t.pickingFrameBuffer.needsDraw=!0,t.drawing=new om(t,a,r);var n=function(f){return function(c){return t.getTextAngle(c,f)}},i=function(f){return function(c){var h=c.pstyle(f);return h&&h.value}},s=function(f){return function(c){return c.pstyle("".concat(f,"-opacity")).value>0}},o=function(f){var c=f.pstyle("text-events").strValue==="yes";return c?An.USE_BB:An.IGNORE},l=function(f){var c=f.position(),h=c.x,d=c.y,m=f.outerWidth(),g=f.outerHeight();return{w:m,h:g,x1:h-m/2,y1:d-g/2}};t.drawing.addAtlasCollection("node",{texRows:r.webglTexRowsNodes}),t.drawing.addAtlasCollection("label",{texRows:r.webglTexRows}),t.drawing.addTextureAtlasRenderType("node-body",{collection:"node",getKey:e.getStyleKey,getBoundingBox:e.getElementBox,drawElement:e.drawElement}),t.drawing.addSimpleShapeRenderType("node-body",{getBoundingBox:l,isSimple:Gy,shapeProps:{shape:"shape",color:"background-color",opacity:"background-opacity",radius:"corner-radius",border:!0}}),t.drawing.addSimpleShapeRenderType("node-overlay",{getBoundingBox:l,isVisible:s("overlay"),shapeProps:{shape:"overlay-shape",color:"overlay-color",opacity:"overlay-opacity",padding:"overlay-padding",radius:"overlay-corner-radius"}}),t.drawing.addSimpleShapeRenderType("node-underlay",{getBoundingBox:l,isVisible:s("underlay"),shapeProps:{shape:"underlay-shape",color:"underlay-color",opacity:"underlay-opacity",padding:"underlay-padding",radius:"underlay-corner-radius"}}),t.drawing.addTextureAtlasRenderType("label",{collection:"label",getTexPickingMode:o,getKey:Ss(e.getLabelKey,null),getBoundingBox:ks(e.getLabelBox,null),drawClipped:!0,drawElement:e.drawLabel,getRotation:n(null),getRotationPoint:e.getLabelRotationPoint,getRotationOffset:e.getLabelRotationOffset,isVisible:i("label")}),t.drawing.addTextureAtlasRenderType("edge-source-label",{collection:"label",getTexPickingMode:o,getKey:Ss(e.getSourceLabelKey,"source"),getBoundingBox:ks(e.getSourceLabelBox,"source"),drawClipped:!0,drawElement:e.drawSourceLabel,getRotation:n("source"),getRotationPoint:e.getSourceLabelRotationPoint,getRotationOffset:e.getSourceLabelRotationOffset,isVisible:i("source-label")}),t.drawing.addTextureAtlasRenderType("edge-target-label",{collection:"label",getTexPickingMode:o,getKey:Ss(e.getTargetLabelKey,"target"),getBoundingBox:ks(e.getTargetLabelBox,"target"),drawClipped:!0,drawElement:e.drawTargetLabel,getRotation:n("target"),getRotationPoint:e.getTargetLabelRotationPoint,getRotationOffset:e.getTargetLabelRotationOffset,isVisible:i("target-label")});var u=Fa(function(){console.log("garbage collect flag set"),t.data.gc=!0},1e4);t.onUpdateEleCalcs(function(v,f){var c=!1;f&&f.length>0&&(c|=t.drawing.invalidate(f)),c&&u()}),lm(t)};function um(r){var e=r.cy.container(),t=e&&e.style&&e.style.backgroundColor||"white";return av(t)}function Af(r,e){var t=r._private.rscratch;return Tr(t,"labelWrapCachedLines",e)||[]}var Ss=function(e,t){return function(a){var n=e(a),i=Af(a,t);return i.length>1?i.map(function(s,o){return"".concat(n,"_").concat(o)}):n}},ks=function(e,t){return function(a,n){var i=e(a);if(typeof n=="string"){var s=n.indexOf("_");if(s>0){var o=Number(n.substring(s+1)),l=Af(a,t),u=i.h/l.length,v=u*o,f=i.y1+v;return{x1:i.x1,w:i.w,y1:f,h:u,yOffset:v}}}return i}};function lm(r){{var e=r.render;r.render=function(i){i=i||{};var s=r.cy;r.webgl&&(s.zoom()>Ef?(vm(r),e.call(r,i)):(fm(r),Mf(r,i,Ea.SCREEN)))}}{var t=r.matchCanvasSize;r.matchCanvasSize=function(i){t.call(r,i),r.pickingFrameBuffer.setFramebufferAttachmentSizes(r.canvasWidth,r.canvasHeight),r.pickingFrameBuffer.needsDraw=!0}}r.findNearestElements=function(i,s,o,l){return ym(r,i,s)};{var a=r.invalidateCachedZSortedEles;r.invalidateCachedZSortedEles=function(){a.call(r),r.pickingFrameBuffer.needsDraw=!0}}{var n=r.notify;r.notify=function(i,s){n.call(r,i,s),i==="viewport"||i==="bounds"?r.pickingFrameBuffer.needsDraw=!0:i==="background"&&r.drawing.invalidate(s,{type:"node-body"})}}}function vm(r){var e=r.data.contexts[r.WEBGL];e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT)}function fm(r){var e=function(a){a.save(),a.setTransform(1,0,0,1,0,0),a.clearRect(0,0,r.canvasWidth,r.canvasHeight),a.restore()};e(r.data.contexts[r.NODE]),e(r.data.contexts[r.DRAG])}function cm(r){var e=r.canvasWidth,t=r.canvasHeight,a=bo(r),n=a.pan,i=a.zoom,s=Es();yn(s,s,[n.x,n.y]),Ws(s,s,[i,i]);var o=Es();Qy(o,e,t);var l=Es();return Zy(l,o,s),l}function Rf(r,e){var t=r.canvasWidth,a=r.canvasHeight,n=bo(r),i=n.pan,s=n.zoom;e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t,a),e.translate(i.x,i.y),e.scale(s,s)}function dm(r,e){r.drawSelectionRectangle(e,function(t){return Rf(r,t)})}function hm(r){var e=r.data.contexts[r.NODE];e.save(),Rf(r,e),e.strokeStyle="rgba(0, 0, 0, 0.3)",e.beginPath(),e.moveTo(-1e3,0),e.lineTo(1e3,0),e.stroke(),e.beginPath(),e.moveTo(0,-1e3),e.lineTo(0,1e3),e.stroke(),e.restore()}function gm(r){var e=function(n,i,s){for(var o=n.atlasManager.getAtlasCollection(i),l=r.data.contexts[r.NODE],u=o.atlases,v=0;v<u.length;v++){var f=u[v],c=f.canvas;if(c){var h=c.width,d=c.height,m=h*v,g=c.height*s,p=.4;l.save(),l.scale(p,p),l.drawImage(c,m,g),l.strokeStyle="black",l.rect(m,g,h,d),l.stroke(),l.restore()}}},t=0;e(r.drawing,"node",t++),e(r.drawing,"label",t++)}function pm(r,e,t,a,n){var i,s,o,l,u=bo(r),v=u.pan,f=u.zoom;{var c=_y(r,v,f,e,t),h=rr(c,2),d=h[0],m=h[1],g=6;i=d-g/2,s=m-g/2,o=g,l=g}if(o===0||l===0)return[];var p=r.data.contexts[r.WEBGL];p.bindFramebuffer(p.FRAMEBUFFER,r.pickingFrameBuffer),r.pickingFrameBuffer.needsDraw&&(p.viewport(0,0,p.canvas.width,p.canvas.height),Mf(r,null,Ea.PICKING),r.pickingFrameBuffer.needsDraw=!1);var y=o*l,b=new Uint8Array(y*4);p.readPixels(i,s,o,l,p.RGBA,p.UNSIGNED_BYTE,b),p.bindFramebuffer(p.FRAMEBUFFER,null);for(var w=new Set,E=0;E<y;E++){var C=b.slice(E*4,E*4+4),x=Wy(C)-1;x>=0&&w.add(x)}return w}function ym(r,e,t){var a=pm(r,e,t),n=r.getCachedZSortedEles(),i,s,o=kr(a),l;try{for(o.s();!(l=o.n()).done;){var u=l.value,v=n[u];if(!i&&v.isNode()&&(i=v),!s&&v.isEdge()&&(s=v),i&&s)break}}catch(f){o.e(f)}finally{o.f()}return[i,s].filter(Boolean)}function Ds(r,e,t){var a=r.drawing;e+=1,t.isNode()?(a.drawNode(t,e,"node-underlay"),a.drawNode(t,e,"node-body"),a.drawTexture(t,e,"label"),a.drawNode(t,e,"node-overlay")):(a.drawEdgeLine(t,e),a.drawEdgeArrow(t,e,"source"),a.drawEdgeArrow(t,e,"target"),a.drawTexture(t,e,"label"),a.drawTexture(t,e,"edge-source-label"),a.drawTexture(t,e,"edge-target-label"))}function Mf(r,e,t){var a;r.webglDebug&&(a=performance.now());var n=r.drawing,i=0;if(t.screen&&r.data.canvasNeedsRedraw[r.SELECT_BOX]&&dm(r,e),r.data.canvasNeedsRedraw[r.NODE]||t.picking){var s=r.data.contexts[r.WEBGL];t.screen?(s.clearColor(0,0,0,0),s.enable(s.BLEND),s.blendFunc(s.ONE,s.ONE_MINUS_SRC_ALPHA)):s.disable(s.BLEND),s.clear(s.COLOR_BUFFER_BIT|s.DEPTH_BUFFER_BIT),s.viewport(0,0,s.canvas.width,s.canvas.height);var o=cm(r),l=r.getCachedZSortedEles();if(i=l.length,n.startFrame(o,t),t.screen){for(var u=0;u<l.nondrag.length;u++)Ds(r,u,l.nondrag[u]);for(var v=0;v<l.drag.length;v++)Ds(r,v,l.drag[v])}else if(t.picking)for(var f=0;f<l.length;f++)Ds(r,f,l[f]);n.endFrame(),t.screen&&r.webglDebugShowAtlases&&(hm(r),gm(r)),r.data.canvasNeedsRedraw[r.NODE]=!1,r.data.canvasNeedsRedraw[r.DRAG]=!1}if(r.webglDebug){var c=performance.now(),h=!1,d=Math.ceil(c-a),m=n.getDebugInfo(),g=["".concat(i," elements"),"".concat(m.totalInstances," instances"),"".concat(m.batchCount," batches"),"".concat(m.totalAtlases," atlases"),"".concat(m.wrappedCount," wrapped textures"),"".concat(m.simpleCount," simple shapes")].join(", ");if(h)console.log("WebGL (".concat(t.name,") - time ").concat(d,"ms, ").concat(g));else{console.log("WebGL (".concat(t.name,") - frame time ").concat(d,"ms")),console.log("Totals:"),console.log("  ".concat(g)),console.log("Texture Atlases Used:");var p=m.atlasInfo,y=kr(p),b;try{for(y.s();!(b=y.n()).done;){var w=b.value;console.log("  ".concat(w.type,": ").concat(w.keyCount," keys, ").concat(w.atlasCount," atlases"))}}catch(E){y.e(E)}finally{y.f()}console.log("")}}r.data.gc&&(console.log("Garbage Collect!"),r.data.gc=!1,n.gc())}var yt={};yt.drawPolygonPath=function(r,e,t,a,n,i){var s=a/2,o=n/2;r.beginPath&&r.beginPath(),r.moveTo(e+s*i[0],t+o*i[1]);for(var l=1;l<i.length/2;l++)r.lineTo(e+s*i[l*2],t+o*i[l*2+1]);r.closePath()};yt.drawRoundPolygonPath=function(r,e,t,a,n,i,s){s.forEach(function(o){return df(r,o)}),r.closePath()};yt.drawRoundRectanglePath=function(r,e,t,a,n,i){var s=a/2,o=n/2,l=i==="auto"?lt(a,n):Math.min(i,o,s);r.beginPath&&r.beginPath(),r.moveTo(e,t-o),r.arcTo(e+s,t-o,e+s,t,l),r.arcTo(e+s,t+o,e,t+o,l),r.arcTo(e-s,t+o,e-s,t,l),r.arcTo(e-s,t-o,e,t-o,l),r.lineTo(e,t-o),r.closePath()};yt.drawBottomRoundRectanglePath=function(r,e,t,a,n,i){var s=a/2,o=n/2,l=i==="auto"?lt(a,n):i;r.beginPath&&r.beginPath(),r.moveTo(e,t-o),r.lineTo(e+s,t-o),r.lineTo(e+s,t),r.arcTo(e+s,t+o,e,t+o,l),r.arcTo(e-s,t+o,e-s,t,l),r.lineTo(e-s,t-o),r.lineTo(e,t-o),r.closePath()};yt.drawCutRectanglePath=function(r,e,t,a,n,i,s){var o=a/2,l=n/2,u=s==="auto"?no():s;r.beginPath&&r.beginPath(),r.moveTo(e-o+u,t-l),r.lineTo(e+o-u,t-l),r.lineTo(e+o,t-l+u),r.lineTo(e+o,t+l-u),r.lineTo(e+o-u,t+l),r.lineTo(e-o+u,t+l),r.lineTo(e-o,t+l-u),r.lineTo(e-o,t-l+u),r.closePath()};yt.drawBarrelPath=function(r,e,t,a,n){var i=a/2,s=n/2,o=e-i,l=e+i,u=t-s,v=t+s,f=As(a,n),c=f.widthOffset,h=f.heightOffset,d=f.ctrlPtOffsetPct*c;r.beginPath&&r.beginPath(),r.moveTo(o,u+h),r.lineTo(o,v-h),r.quadraticCurveTo(o+d,v,o+c,v),r.lineTo(l-c,v),r.quadraticCurveTo(l-d,v,l,v-h),r.lineTo(l,u+h),r.quadraticCurveTo(l-d,u,l-c,u),r.lineTo(o+c,u),r.quadraticCurveTo(o+d,u,o,u+h),r.closePath()};var Kl=Math.sin(0),Xl=Math.cos(0),$s={},Us={},Lf=Math.PI/40;for(var _t=0*Math.PI;_t<2*Math.PI;_t+=Lf)$s[_t]=Math.sin(_t),Us[_t]=Math.cos(_t);yt.drawEllipsePath=function(r,e,t,a,n){if(r.beginPath&&r.beginPath(),r.ellipse)r.ellipse(e,t,a/2,n/2,0,0,2*Math.PI);else for(var i,s,o=a/2,l=n/2,u=0*Math.PI;u<2*Math.PI;u+=Lf)i=e-o*$s[u]*Kl+o*Us[u]*Xl,s=t+l*Us[u]*Kl+l*$s[u]*Xl,u===0?r.moveTo(i,s):r.lineTo(i,s);r.closePath()};var Wa={};Wa.createBuffer=function(r,e){var t=document.createElement("canvas");return t.width=r,t.height=e,[t,t.getContext("2d")]};Wa.bufferCanvasImage=function(r){var e=this.cy,t=e.mutableElements(),a=t.boundingBox(),n=this.findContainerClientCoords(),i=r.full?Math.ceil(a.w):n[2],s=r.full?Math.ceil(a.h):n[3],o=te(r.maxWidth)||te(r.maxHeight),l=this.getPixelRatio(),u=1;if(r.scale!==void 0)i*=r.scale,s*=r.scale,u=r.scale;else if(o){var v=1/0,f=1/0;te(r.maxWidth)&&(v=u*r.maxWidth/i),te(r.maxHeight)&&(f=u*r.maxHeight/s),u=Math.min(v,f),i*=u,s*=u}o||(i*=l,s*=l,u*=l);var c=document.createElement("canvas");c.width=i,c.height=s,c.style.width=i+"px",c.style.height=s+"px";var h=c.getContext("2d");if(i>0&&s>0){h.clearRect(0,0,i,s),h.globalCompositeOperation="source-over";var d=this.getCachedZSortedEles();if(r.full)h.translate(-a.x1*u,-a.y1*u),h.scale(u,u),this.drawElements(h,d),h.scale(1/u,1/u),h.translate(a.x1*u,a.y1*u);else{var m=e.pan(),g={x:m.x*u,y:m.y*u};u*=e.zoom(),h.translate(g.x,g.y),h.scale(u,u),this.drawElements(h,d),h.scale(1/u,1/u),h.translate(-g.x,-g.y)}r.bg&&(h.globalCompositeOperation="destination-over",h.fillStyle=r.bg,h.rect(0,0,i,s),h.fill())}return c};function mm(r,e){for(var t=atob(r),a=new ArrayBuffer(t.length),n=new Uint8Array(a),i=0;i<t.length;i++)n[i]=t.charCodeAt(i);return new Blob([a],{type:e})}function Yl(r){var e=r.indexOf(",");return r.substr(e+1)}function If(r,e,t){var a=function(){return e.toDataURL(t,r.quality)};switch(r.output){case"blob-promise":return new ta(function(n,i){try{e.toBlob(function(s){s!=null?n(s):i(new Error("`canvas.toBlob()` sent a null value in its callback"))},t,r.quality)}catch(s){i(s)}});case"blob":return mm(Yl(a()),t);case"base64":return Yl(a());case"base64uri":default:return a()}}Wa.png=function(r){return If(r,this.bufferCanvasImage(r),"image/png")};Wa.jpg=function(r){return If(r,this.bufferCanvasImage(r),"image/jpeg")};var Of={};Of.nodeShapeImpl=function(r,e,t,a,n,i,s,o){switch(r){case"ellipse":return this.drawEllipsePath(e,t,a,n,i);case"polygon":return this.drawPolygonPath(e,t,a,n,i,s);case"round-polygon":return this.drawRoundPolygonPath(e,t,a,n,i,s,o);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(e,t,a,n,i,o);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(e,t,a,n,i,s,o);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(e,t,a,n,i,o);case"barrel":return this.drawBarrelPath(e,t,a,n,i)}};var bm=Nf,Ce=Nf.prototype;Ce.CANVAS_LAYERS=3;Ce.SELECT_BOX=0;Ce.DRAG=1;Ce.NODE=2;Ce.WEBGL=3;Ce.CANVAS_TYPES=["2d","2d","2d","webgl2"];Ce.BUFFER_COUNT=3;Ce.TEXTURE_BUFFER=0;Ce.MOTIONBLUR_BUFFER_NODE=1;Ce.MOTIONBLUR_BUFFER_DRAG=2;function Nf(r){var e=this,t=e.cy.window(),a=t.document;r.webgl&&(Ce.CANVAS_LAYERS=e.CANVAS_LAYERS=4,console.log("webgl rendering enabled")),e.data={canvases:new Array(Ce.CANVAS_LAYERS),contexts:new Array(Ce.CANVAS_LAYERS),canvasNeedsRedraw:new Array(Ce.CANVAS_LAYERS),bufferCanvases:new Array(Ce.BUFFER_COUNT),bufferContexts:new Array(Ce.CANVAS_LAYERS)};var n="-webkit-tap-highlight-color",i="rgba(0,0,0,0)";e.data.canvasContainer=a.createElement("div");var s=e.data.canvasContainer.style;e.data.canvasContainer.style[n]=i,s.position="relative",s.zIndex="0",s.overflow="hidden";var o=r.cy.container();o.appendChild(e.data.canvasContainer),o.style[n]=i;var l={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};hc()&&(l["-ms-touch-action"]="none",l["touch-action"]="none");for(var u=0;u<Ce.CANVAS_LAYERS;u++){var v=e.data.canvases[u]=a.createElement("canvas"),f=Ce.CANVAS_TYPES[u];e.data.contexts[u]=v.getContext(f),e.data.contexts[u]||We("Could not create canvas of type "+f),Object.keys(l).forEach(function(Z){v.style[Z]=l[Z]}),v.style.position="absolute",v.setAttribute("data-id","layer"+u),v.style.zIndex=String(Ce.CANVAS_LAYERS-u),e.data.canvasContainer.appendChild(v),e.data.canvasNeedsRedraw[u]=!1}e.data.topCanvas=e.data.canvases[0],e.data.canvases[Ce.NODE].setAttribute("data-id","layer"+Ce.NODE+"-node"),e.data.canvases[Ce.SELECT_BOX].setAttribute("data-id","layer"+Ce.SELECT_BOX+"-selectbox"),e.data.canvases[Ce.DRAG].setAttribute("data-id","layer"+Ce.DRAG+"-drag"),e.data.canvases[Ce.WEBGL]&&e.data.canvases[Ce.WEBGL].setAttribute("data-id","layer"+Ce.WEBGL+"-webgl");for(var u=0;u<Ce.BUFFER_COUNT;u++)e.data.bufferCanvases[u]=a.createElement("canvas"),e.data.bufferContexts[u]=e.data.bufferCanvases[u].getContext("2d"),e.data.bufferCanvases[u].style.position="absolute",e.data.bufferCanvases[u].setAttribute("data-id","buffer"+u),e.data.bufferCanvases[u].style.zIndex=String(-u-1),e.data.bufferCanvases[u].style.visibility="hidden";e.pathsEnabled=!0;var c=wr(),h=function(z){return{x:(z.x1+z.x2)/2,y:(z.y1+z.y2)/2}},d=function(z){return{x:-z.w/2,y:-z.h/2}},m=function(z){var G=z[0]._private,H=G.oldBackgroundTimestamp===G.backgroundTimestamp;return!H},g=function(z){return z[0]._private.nodeKey},p=function(z){return z[0]._private.labelStyleKey},y=function(z){return z[0]._private.sourceLabelStyleKey},b=function(z){return z[0]._private.targetLabelStyleKey},w=function(z,G,H,Q,ne){return e.drawElement(z,G,H,!1,!1,ne)},E=function(z,G,H,Q,ne){return e.drawElementText(z,G,H,Q,"main",ne)},C=function(z,G,H,Q,ne){return e.drawElementText(z,G,H,Q,"source",ne)},x=function(z,G,H,Q,ne){return e.drawElementText(z,G,H,Q,"target",ne)},T=function(z){return z.boundingBox(),z[0]._private.bodyBounds},S=function(z){return z.boundingBox(),z[0]._private.labelBounds.main||c},P=function(z){return z.boundingBox(),z[0]._private.labelBounds.source||c},D=function(z){return z.boundingBox(),z[0]._private.labelBounds.target||c},A=function(z,G){return G},B=function(z){return h(T(z))},R=function(z,G,H){var Q=z?z+"-":"";return{x:G.x+H.pstyle(Q+"text-margin-x").pfValue,y:G.y+H.pstyle(Q+"text-margin-y").pfValue}},M=function(z,G,H){var Q=z[0]._private.rscratch;return{x:Q[G],y:Q[H]}},L=function(z){return R("",M(z,"labelX","labelY"),z)},I=function(z){return R("source",M(z,"sourceLabelX","sourceLabelY"),z)},O=function(z){return R("target",M(z,"targetLabelX","targetLabelY"),z)},F=function(z){return d(T(z))},_=function(z){return d(P(z))},N=function(z){return d(D(z))},q=function(z){var G=S(z),H=d(S(z));if(z.isNode()){switch(z.pstyle("text-halign").value){case"left":H.x=-G.w-(G.leftPad||0);break;case"right":H.x=-(G.rightPad||0);break}switch(z.pstyle("text-valign").value){case"top":H.y=-G.h-(G.topPad||0);break;case"bottom":H.y=-(G.botPad||0);break}}return H},U=e.data.eleTxrCache=new ba(e,{getKey:g,doesEleInvalidateKey:m,drawElement:w,getBoundingBox:T,getRotationPoint:B,getRotationOffset:F,allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),X=e.data.lblTxrCache=new ba(e,{getKey:p,drawElement:E,getBoundingBox:S,getRotationPoint:L,getRotationOffset:q,isVisible:A}),j=e.data.slbTxrCache=new ba(e,{getKey:y,drawElement:C,getBoundingBox:P,getRotationPoint:I,getRotationOffset:_,isVisible:A}),J=e.data.tlbTxrCache=new ba(e,{getKey:b,drawElement:x,getBoundingBox:D,getRotationPoint:O,getRotationOffset:N,isVisible:A}),re=e.data.lyrTxrCache=new Cf(e);e.onUpdateEleCalcs(function(z,G){U.invalidateElements(G),X.invalidateElements(G),j.invalidateElements(G),J.invalidateElements(G),re.invalidateElements(G);for(var H=0;H<G.length;H++){var Q=G[H]._private;Q.oldBackgroundTimestamp=Q.backgroundTimestamp}});var ae=function(z){for(var G=0;G<z.length;G++)re.enqueueElementRefinement(z[G].ele)};U.onDequeue(ae),X.onDequeue(ae),j.onDequeue(ae),J.onDequeue(ae),r.webgl&&e.initWebgl(r,{getStyleKey:g,getLabelKey:p,getSourceLabelKey:y,getTargetLabelKey:b,drawElement:w,drawLabel:E,drawSourceLabel:C,drawTargetLabel:x,getElementBox:T,getLabelBox:S,getSourceLabelBox:P,getTargetLabelBox:D,getElementRotationPoint:B,getElementRotationOffset:F,getLabelRotationPoint:L,getSourceLabelRotationPoint:I,getTargetLabelRotationPoint:O,getLabelRotationOffset:q,getSourceLabelRotationOffset:_,getTargetLabelRotationOffset:N})}Ce.redrawHint=function(r,e){var t=this;switch(r){case"eles":t.data.canvasNeedsRedraw[Ce.NODE]=e;break;case"drag":t.data.canvasNeedsRedraw[Ce.DRAG]=e;break;case"select":t.data.canvasNeedsRedraw[Ce.SELECT_BOX]=e;break;case"gc":t.data.gc=!0;break}};var wm=typeof Path2D<"u";Ce.path2dEnabled=function(r){if(r===void 0)return this.pathsEnabled;this.pathsEnabled=!!r};Ce.usePaths=function(){return wm&&this.pathsEnabled};Ce.setImgSmoothing=function(r,e){r.imageSmoothingEnabled!=null?r.imageSmoothingEnabled=e:(r.webkitImageSmoothingEnabled=e,r.mozImageSmoothingEnabled=e,r.msImageSmoothingEnabled=e)};Ce.getImgSmoothing=function(r){return r.imageSmoothingEnabled!=null?r.imageSmoothingEnabled:r.webkitImageSmoothingEnabled||r.mozImageSmoothingEnabled||r.msImageSmoothingEnabled};Ce.makeOffscreenCanvas=function(r,e){var t;if((typeof OffscreenCanvas>"u"?"undefined":ar(OffscreenCanvas))!=="undefined")t=new OffscreenCanvas(r,e);else{var a=this.cy.window(),n=a.document;t=n.createElement("canvas"),t.width=r,t.height=e}return t};[Tf,Hr,Jr,mo,Mt,pt,xr,Pf,yt,Wa,Of].forEach(function(r){pe(Ce,r)});var xm=[{name:"null",impl:vf},{name:"base",impl:wf},{name:"canvas",impl:bm}],Em=[{type:"layout",extensions:Xp},{type:"renderer",extensions:xm}],zf={},Ff={};function Vf(r,e,t){var a=t,n=function(T){Ie("Can not register `"+e+"` for `"+r+"` since `"+T+"` already exists in the prototype and can not be overridden")};if(r==="core"){if(Ra.prototype[e])return n(e);Ra.prototype[e]=t}else if(r==="collection"){if(fr.prototype[e])return n(e);fr.prototype[e]=t}else if(r==="layout"){for(var i=function(T){this.options=T,t.call(this,T),Re(this._private)||(this._private={}),this._private.cy=T.cy,this._private.listeners=[],this.createEmitter()},s=i.prototype=Object.create(t.prototype),o=[],l=0;l<o.length;l++){var u=o[l];s[u]=s[u]||function(){return this}}s.start&&!s.run?s.run=function(){return this.start(),this}:!s.start&&s.run&&(s.start=function(){return this.run(),this});var v=t.prototype.stop;s.stop=function(){var x=this.options;if(x&&x.animate){var T=this.animations;if(T)for(var S=0;S<T.length;S++)T[S].stop()}return v?v.call(this):this.emit("layoutstop"),this},s.destroy||(s.destroy=function(){return this}),s.cy=function(){return this._private.cy};var f=function(T){return T._private.cy},c={addEventFields:function(T,S){S.layout=T,S.cy=f(T),S.target=T},bubble:function(){return!0},parent:function(T){return f(T)}};pe(s,{createEmitter:function(){return this._private.emitter=new Gn(c,this),this},emitter:function(){return this._private.emitter},on:function(T,S){return this.emitter().on(T,S),this},one:function(T,S){return this.emitter().one(T,S),this},once:function(T,S){return this.emitter().one(T,S),this},removeListener:function(T,S){return this.emitter().removeListener(T,S),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(T,S){return this.emitter().emit(T,S),this}}),Le.eventAliasesOn(s),a=i}else if(r==="renderer"&&e!=="null"&&e!=="base"){var h=qf("renderer","base"),d=h.prototype,m=t,g=t.prototype,p=function(){h.apply(this,arguments),m.apply(this,arguments)},y=p.prototype;for(var b in d){var w=d[b],E=g[b]!=null;if(E)return n(b);y[b]=w}for(var C in g)y[C]=g[C];d.clientFunctions.forEach(function(x){y[x]=y[x]||function(){We("Renderer does not implement `renderer."+x+"()` on its prototype")}}),a=p}else if(r==="__proto__"||r==="constructor"||r==="prototype")return We(r+" is an illegal type to be registered, possibly lead to prototype pollutions");return nv({map:zf,keys:[r,e],value:a})}function qf(r,e){return iv({map:zf,keys:[r,e]})}function Cm(r,e,t,a,n){return nv({map:Ff,keys:[r,e,t,a],value:n})}function Tm(r,e,t,a){return iv({map:Ff,keys:[r,e,t,a]})}var Ks=function(){if(arguments.length===2)return qf.apply(null,arguments);if(arguments.length===3)return Vf.apply(null,arguments);if(arguments.length===4)return Tm.apply(null,arguments);if(arguments.length===5)return Cm.apply(null,arguments);We("Invalid extension access syntax")};Ra.prototype.extension=Ks;Em.forEach(function(r){r.extensions.forEach(function(e){Vf(r.type,e.name,e.impl)})});var Rn=function(){if(!(this instanceof Rn))return new Rn;this.length=0},At=Rn.prototype;At.instanceString=function(){return"stylesheet"};At.selector=function(r){var e=this.length++;return this[e]={selector:r,properties:[]},this};At.css=function(r,e){var t=this.length-1;if(de(r))this[t].properties.push({name:r,value:e});else if(Re(r))for(var a=r,n=Object.keys(a),i=0;i<n.length;i++){var s=n[i],o=a[s];if(o!=null){var l=or.properties[s]||or.properties[Mn(s)];if(l!=null){var u=l.name,v=o;this[t].properties.push({name:u,value:v})}}}return this};At.style=At.css;At.generateStyle=function(r){var e=new or(r);return this.appendToStyle(e)};At.appendToStyle=function(r){for(var e=0;e<this.length;e++){var t=this[e],a=t.selector,n=t.properties;r.selector(a);for(var i=0;i<n.length;i++){var s=n[i];r.css(s.name,s.value)}}return r};var Sm="3.32.1",ea=function(e){if(e===void 0&&(e={}),Re(e))return new Ra(e);if(de(e))return Ks.apply(Ks,arguments)};ea.use=function(r){var e=Array.prototype.slice.call(arguments,1);return e.unshift(ea),r.apply(null,e),this};ea.warnings=function(r){return cv(r)};ea.version=Sm;ea.stylesheet=ea.Stylesheet=Rn;export{ea as c};
