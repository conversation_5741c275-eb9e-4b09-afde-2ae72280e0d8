import{s as r,b as e,a,S as i}from"./chunk-OW32GOEJ-BJOGnmEj.js";import{_ as s}from"./index-DTzYmM8W.js";import"./chunk-BFAMUDN2-DFWQ9R70.js";import"./chunk-SKB7J2MH-B3bZuGXG.js";import"./semi-ui-Csx8wKaA.js";import"./react-core-DskXcPn0.js";import"./tools-C3llIrvJ.js";import"./react-components-C55tCU1e.js";import"./i18n-bne0o_C4.js";var f={parser:a,get db(){return new i(2)},renderer:e,styles:r,init:s(t=>{t.state||(t.state={}),t.state.arrowMarkerAbsolute=t.arrowMarkerAbsolute},"init")};export{f as diagram};
