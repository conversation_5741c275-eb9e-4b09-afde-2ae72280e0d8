import{r as R}from"./react-core-DskXcPn0.js";import{a as $e,b as Ie}from"./semi-ui-Csx8wKaA.js";function Ee(){if(console&&console.warn){for(var i=arguments.length,e=new Array(i),t=0;t<i;t++)e[t]=arguments[t];typeof e[0]=="string"&&(e[0]=`react-i18next:: ${e[0]}`),console.warn(...e)}}const se={};function Z(){for(var i=arguments.length,e=new Array(i),t=0;t<i;t++)e[t]=arguments[t];typeof e[0]=="string"&&se[e[0]]||(typeof e[0]=="string"&&(se[e[0]]=new Date),Ee(...e))}const ve=(i,e)=>()=>{if(i.isInitialized)e();else{const t=()=>{setTimeout(()=>{i.off("initialized",t)},0),e()};i.on("initialized",t)}};function ie(i,e,t){i.loadNamespaces(e,ve(i,t))}function re(i,e,t,n){typeof t=="string"&&(t=[t]),t.forEach(s=>{i.options.ns.indexOf(s)<0&&i.options.ns.push(s)}),i.loadLanguages(e,ve(i,n))}function Fe(i,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const n=e.languages[0],s=e.options?e.options.fallbackLng:!1,r=e.languages[e.languages.length-1];if(n.toLowerCase()==="cimode")return!0;const a=(o,l)=>{const u=e.services.backendConnector.state[`${o}|${l}`];return u===-1||u===2};return t.bindI18n&&t.bindI18n.indexOf("languageChanging")>-1&&e.services.backendConnector.backend&&e.isLanguageChangingTo&&!a(e.isLanguageChangingTo,i)?!1:!!(e.hasResourceBundle(n,i)||!e.services.backendConnector.backend||e.options.resources&&!e.options.partialBundledLanguages||a(n,i)&&(!s||a(r,i)))}function Ae(i,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return!e.languages||!e.languages.length?(Z("i18n.languages were undefined or empty",e.languages),!0):e.options.ignoreJSONStructure!==void 0?e.hasLoadedNamespace(i,{lng:t.lng,precheck:(s,r)=>{if(t.bindI18n&&t.bindI18n.indexOf("languageChanging")>-1&&s.services.backendConnector.backend&&s.isLanguageChangingTo&&!r(s.isLanguageChangingTo,i))return!1}}):Fe(i,e,t)}const Te=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,De={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},je=i=>De[i],Ue=i=>i.replace(Te,je);let X={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:Ue};function Ve(){let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};X={...X,...i}}function Ke(){return X}let we;function Me(i){we=i}function ze(){return we}const Et={type:"3rdParty",init(i){Ve(i.options.react),Me(i)}},He=R.createContext();class Be{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(t=>{this.usedNamespaces[t]||(this.usedNamespaces[t]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const Je=(i,e)=>{const t=R.useRef();return R.useEffect(()=>{t.current=i},[i,e]),t.current};function Ft(i){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{i18n:t}=e,{i18n:n,defaultNS:s}=R.useContext(He)||{},r=t||n||ze();if(r&&!r.reportNamespaces&&(r.reportNamespaces=new Be),!r){Z("You will need to pass in an i18next instance by using initReactI18next");const w=(y,S)=>typeof S=="string"?S:S&&typeof S=="object"&&typeof S.defaultValue=="string"?S.defaultValue:Array.isArray(y)?y[y.length-1]:y,x=[w,{},!1];return x.t=w,x.i18n={},x.ready=!1,x}r.options.react&&r.options.react.wait!==void 0&&Z("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const a={...Ke(),...r.options.react,...e},{useSuspense:o,keyPrefix:l}=a;let u=s||r.options&&r.options.defaultNS;u=typeof u=="string"?[u]:u||["translation"],r.reportNamespaces.addUsedNamespaces&&r.reportNamespaces.addUsedNamespaces(u);const c=(r.isInitialized||r.initializedStoreOnce)&&u.every(w=>Ae(w,r,a));function g(){return r.getFixedT(e.lng||null,a.nsMode==="fallback"?u:u[0],l)}const[f,d]=R.useState(g);let p=u.join();e.lng&&(p=`${e.lng}${p}`);const b=Je(p),m=R.useRef(!0);R.useEffect(()=>{const{bindI18n:w,bindI18nStore:x}=a;m.current=!0,!c&&!o&&(e.lng?re(r,e.lng,u,()=>{m.current&&d(g)}):ie(r,u,()=>{m.current&&d(g)})),c&&b&&b!==p&&m.current&&d(g);function y(){m.current&&d(g)}return w&&r&&r.on(w,y),x&&r&&r.store.on(x,y),()=>{m.current=!1,w&&r&&w.split(" ").forEach(S=>r.off(S,y)),x&&r&&x.split(" ").forEach(S=>r.store.off(S,y))}},[r,p]);const L=R.useRef(!0);R.useEffect(()=>{m.current&&!L.current&&d(g),L.current=!1},[r,l]);const v=[f,r,c];if(v.t=f,v.i18n=r,v.ready=c,c||!c&&!o)return v;throw new Promise(w=>{e.lng?re(r,e.lng,u,()=>w()):ie(r,u,()=>w())})}const h=i=>typeof i=="string",D=()=>{let i,e;const t=new Promise((n,s)=>{i=n,e=s});return t.resolve=i,t.reject=e,t},ae=i=>i==null?"":""+i,We=(i,e,t)=>{i.forEach(n=>{e[n]&&(t[n]=e[n])})},Qe=/###/g,oe=i=>i&&i.indexOf("###")>-1?i.replace(Qe,"."):i,le=i=>!i||h(i),V=(i,e,t)=>{const n=h(e)?e.split("."):e;let s=0;for(;s<n.length-1;){if(le(i))return{};const r=oe(n[s]);!i[r]&&t&&(i[r]=new t),Object.prototype.hasOwnProperty.call(i,r)?i=i[r]:i={},++s}return le(i)?{}:{obj:i,k:oe(n[s])}},ue=(i,e,t)=>{const{obj:n,k:s}=V(i,e,Object);if(n!==void 0||e.length===1){n[s]=t;return}let r=e[e.length-1],a=e.slice(0,e.length-1),o=V(i,a,Object);for(;o.obj===void 0&&a.length;)r=`${a[a.length-1]}.${r}`,a=a.slice(0,a.length-1),o=V(i,a,Object),o&&o.obj&&typeof o.obj[`${o.k}.${r}`]<"u"&&(o.obj=void 0);o.obj[`${o.k}.${r}`]=t},Ye=(i,e,t,n)=>{const{obj:s,k:r}=V(i,e,Object);s[r]=s[r]||[],s[r].push(t)},B=(i,e)=>{const{obj:t,k:n}=V(i,e);if(t)return t[n]},qe=(i,e,t)=>{const n=B(i,t);return n!==void 0?n:B(e,t)},Le=(i,e,t)=>{for(const n in e)n!=="__proto__"&&n!=="constructor"&&(n in i?h(i[n])||i[n]instanceof String||h(e[n])||e[n]instanceof String?t&&(i[n]=e[n]):Le(i[n],e[n],t):i[n]=e[n]);return i},$=i=>i.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Ge={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Ze=i=>h(i)?i.replace(/[&<>"'\/]/g,e=>Ge[e]):i;class Xe{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(t!==void 0)return t;const n=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,n),this.regExpQueue.push(e),n}}const _e=[" ",",","?","!",";"],et=new Xe(20),tt=(i,e,t)=>{e=e||"",t=t||"";const n=_e.filter(a=>e.indexOf(a)<0&&t.indexOf(a)<0);if(n.length===0)return!0;const s=et.getRegExp(`(${n.map(a=>a==="?"?"\\?":a).join("|")})`);let r=!s.test(i);if(!r){const a=i.indexOf(t);a>0&&!s.test(i.substring(0,a))&&(r=!0)}return r},_=function(i,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(!i)return;if(i[e])return i[e];const n=e.split(t);let s=i;for(let r=0;r<n.length;){if(!s||typeof s!="object")return;let a,o="";for(let l=r;l<n.length;++l)if(l!==r&&(o+=t),o+=n[l],a=s[o],a!==void 0){if(["string","number","boolean"].indexOf(typeof a)>-1&&l<n.length-1)continue;r+=l-r+1;break}s=a}return s},J=i=>i&&i.replace("_","-"),nt={type:"logger",log(i){this.output("log",i)},warn(i){this.output("warn",i)},error(i){this.output("error",i)},output(i,e){console&&console[i]&&console[i].apply(console,e)}};class W{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||nt,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,n,s){return s&&!this.debug?null:(h(e[0])&&(e[0]=`${n}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new W(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new W(this.logger,e)}}var N=new W;class Y{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(n=>{this.observers[n]||(this.observers[n]=new Map);const s=this.observers[n].get(t)||0;this.observers[n].set(t,s+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(a=>{let[o,l]=a;for(let u=0;u<l;u++)o(...n)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(a=>{let[o,l]=a;for(let u=0;u<l;u++)o.apply(o,[e,...n])})}}class fe extends Y{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const r=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,a=s.ignoreJSONStructure!==void 0?s.ignoreJSONStructure:this.options.ignoreJSONStructure;let o;e.indexOf(".")>-1?o=e.split("."):(o=[e,t],n&&(Array.isArray(n)?o.push(...n):h(n)&&r?o.push(...n.split(r)):o.push(n)));const l=B(this.data,o);return!l&&!t&&!n&&e.indexOf(".")>-1&&(e=o[0],t=o[1],n=o.slice(2).join(".")),l||!a||!h(n)?l:_(this.data&&this.data[e]&&this.data[e][t],n,r)}addResource(e,t,n,s){let r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1};const a=r.keySeparator!==void 0?r.keySeparator:this.options.keySeparator;let o=[e,t];n&&(o=o.concat(a?n.split(a):n)),e.indexOf(".")>-1&&(o=e.split("."),s=t,t=o[1]),this.addNamespaces(t),ue(this.data,o,s),r.silent||this.emit("added",e,t,n,s)}addResources(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(const r in n)(h(n[r])||Array.isArray(n[r]))&&this.addResource(e,t,r,n[r],{silent:!0});s.silent||this.emit("added",e,t,n)}addResourceBundle(e,t,n,s,r){let a=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1,skipCopy:!1},o=[e,t];e.indexOf(".")>-1&&(o=e.split("."),s=n,n=t,t=o[1]),this.addNamespaces(t);let l=B(this.data,o)||{};a.skipCopy||(n=JSON.parse(JSON.stringify(n))),s?Le(l,n,r):l={...l,...n},ue(this.data,o,l),a.silent||this.emit("added",e,t,n)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.options.compatibilityAPI==="v1"?{...this.getResource(e,t)}:this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(s=>t[s]&&Object.keys(t[s]).length>0)}toJSON(){return this.data}}var Oe={processors:{},addPostProcessor(i){this.processors[i.name]=i},handle(i,e,t,n,s){return i.forEach(r=>{this.processors[r]&&(e=this.processors[r].process(e,t,n,s))}),e}};const ce={};class Q extends Y{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),We(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=N.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(e==null)return!1;const n=this.resolve(e,t);return n&&n.res!==void 0}extractFromKey(e,t){let n=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;n===void 0&&(n=":");const s=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator;let r=t.ns||this.options.defaultNS||[];const a=n&&e.indexOf(n)>-1,o=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!tt(e,n,s);if(a&&!o){const l=e.match(this.interpolator.nestingRegexp);if(l&&l.length>0)return{key:e,namespaces:h(r)?[r]:r};const u=e.split(n);(n!==s||n===s&&this.options.ns.indexOf(u[0])>-1)&&(r=u.shift()),e=u.join(s)}return{key:e,namespaces:h(r)?[r]:r}}translate(e,t,n){if(typeof t!="object"&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),typeof t=="object"&&(t={...t}),t||(t={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const s=t.returnDetails!==void 0?t.returnDetails:this.options.returnDetails,r=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator,{key:a,namespaces:o}=this.extractFromKey(e[e.length-1],t),l=o[o.length-1],u=t.lng||this.language,c=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(u&&u.toLowerCase()==="cimode"){if(c){const x=t.nsSeparator||this.options.nsSeparator;return s?{res:`${l}${x}${a}`,usedKey:a,exactUsedKey:a,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:`${l}${x}${a}`}return s?{res:a,usedKey:a,exactUsedKey:a,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:a}const g=this.resolve(e,t);let f=g&&g.res;const d=g&&g.usedKey||a,p=g&&g.exactUsedKey||a,b=Object.prototype.toString.apply(f),m=["[object Number]","[object Function]","[object RegExp]"],L=t.joinArrays!==void 0?t.joinArrays:this.options.joinArrays,v=!this.i18nFormat||this.i18nFormat.handleAsObject,w=!h(f)&&typeof f!="boolean"&&typeof f!="number";if(v&&f&&w&&m.indexOf(b)<0&&!(h(L)&&Array.isArray(f))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const x=this.options.returnedObjectHandler?this.options.returnedObjectHandler(d,f,{...t,ns:o}):`key '${a} (${this.language})' returned an object instead of string.`;return s?(g.res=x,g.usedParams=this.getUsedParamsDetails(t),g):x}if(r){const x=Array.isArray(f),y=x?[]:{},S=x?p:d;for(const O in f)if(Object.prototype.hasOwnProperty.call(f,O)){const M=`${S}${r}${O}`;y[O]=this.translate(M,{...t,joinArrays:!1,ns:o}),y[O]===M&&(y[O]=f[O])}f=y}}else if(v&&h(L)&&Array.isArray(f))f=f.join(L),f&&(f=this.extendTranslation(f,e,t,n));else{let x=!1,y=!1;const S=t.count!==void 0&&!h(t.count),O=Q.hasDefaultValue(t),M=S?this.pluralResolver.getSuffix(u,t.count,t):"",Re=t.ordinal&&S?this.pluralResolver.getSuffix(u,t.count,{ordinal:!1}):"",ee=S&&!t.ordinal&&t.count===0&&this.pluralResolver.shouldUseIntlApi(),E=ee&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${M}`]||t[`defaultValue${Re}`]||t.defaultValue;!this.isValidLookup(f)&&O&&(x=!0,f=E),this.isValidLookup(f)||(y=!0,f=a);const Pe=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&y?void 0:f,F=O&&E!==f&&this.options.updateMissing;if(y||x||F){if(this.logger.log(F?"updateKey":"missingKey",u,l,a,F?E:f),r){const k=this.resolve(a,{...t,keySeparator:!1});k&&k.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let A=[];const z=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if(this.options.saveMissingTo==="fallback"&&z&&z[0])for(let k=0;k<z.length;k++)A.push(z[k]);else this.options.saveMissingTo==="all"?A=this.languageUtils.toResolveHierarchy(t.lng||this.language):A.push(t.lng||this.language);const te=(k,P,T)=>{const ne=O&&T!==f?T:Pe;this.options.missingKeyHandler?this.options.missingKeyHandler(k,l,P,ne,F,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(k,l,P,ne,F,t),this.emit("missingKey",k,l,P,f)};this.options.saveMissing&&(this.options.saveMissingPlurals&&S?A.forEach(k=>{const P=this.pluralResolver.getSuffixes(k,t);ee&&t[`defaultValue${this.options.pluralSeparator}zero`]&&P.indexOf(`${this.options.pluralSeparator}zero`)<0&&P.push(`${this.options.pluralSeparator}zero`),P.forEach(T=>{te([k],a+T,t[`defaultValue${T}`]||E)})}):te(A,a,E))}f=this.extendTranslation(f,e,t,g,n),y&&f===a&&this.options.appendNamespaceToMissingKey&&(f=`${l}:${a}`),(y||x)&&this.options.parseMissingKeyHandler&&(this.options.compatibilityAPI!=="v1"?f=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}:${a}`:a,x?f:void 0):f=this.options.parseMissingKeyHandler(f))}return s?(g.res=f,g.usedParams=this.getUsedParamsDetails(t),g):f}extendTranslation(e,t,n,s,r){var a=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...n},n.lng||this.language||s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init({...n,interpolation:{...this.options.interpolation,...n.interpolation}});const u=h(e)&&(n&&n.interpolation&&n.interpolation.skipOnVariables!==void 0?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let c;if(u){const f=e.match(this.interpolator.nestingRegexp);c=f&&f.length}let g=n.replace&&!h(n.replace)?n.replace:n;if(this.options.interpolation.defaultVariables&&(g={...this.options.interpolation.defaultVariables,...g}),e=this.interpolator.interpolate(e,g,n.lng||this.language||s.usedLng,n),u){const f=e.match(this.interpolator.nestingRegexp),d=f&&f.length;c<d&&(n.nest=!1)}!n.lng&&this.options.compatibilityAPI!=="v1"&&s&&s.res&&(n.lng=this.language||s.usedLng),n.nest!==!1&&(e=this.interpolator.nest(e,function(){for(var f=arguments.length,d=new Array(f),p=0;p<f;p++)d[p]=arguments[p];return r&&r[0]===d[0]&&!n.context?(a.logger.warn(`It seems you are nesting recursively key: ${d[0]} in key: ${t[0]}`),null):a.translate(...d,t)},n)),n.interpolation&&this.interpolator.reset()}const o=n.postProcess||this.options.postProcess,l=h(o)?[o]:o;return e!=null&&l&&l.length&&n.applyPostProcessor!==!1&&(e=Oe.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...s,usedParams:this.getUsedParamsDetails(n)},...n}:n,this)),e}resolve(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n,s,r,a,o;return h(e)&&(e=[e]),e.forEach(l=>{if(this.isValidLookup(n))return;const u=this.extractFromKey(l,t),c=u.key;s=c;let g=u.namespaces;this.options.fallbackNS&&(g=g.concat(this.options.fallbackNS));const f=t.count!==void 0&&!h(t.count),d=f&&!t.ordinal&&t.count===0&&this.pluralResolver.shouldUseIntlApi(),p=t.context!==void 0&&(h(t.context)||typeof t.context=="number")&&t.context!=="",b=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);g.forEach(m=>{this.isValidLookup(n)||(o=m,!ce[`${b[0]}-${m}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(o)&&(ce[`${b[0]}-${m}`]=!0,this.logger.warn(`key "${s}" for languages "${b.join(", ")}" won't get resolved as namespace "${o}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),b.forEach(L=>{if(this.isValidLookup(n))return;a=L;const v=[c];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(v,c,L,m,t);else{let x;f&&(x=this.pluralResolver.getSuffix(L,t.count,t));const y=`${this.options.pluralSeparator}zero`,S=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(f&&(v.push(c+x),t.ordinal&&x.indexOf(S)===0&&v.push(c+x.replace(S,this.options.pluralSeparator)),d&&v.push(c+y)),p){const O=`${c}${this.options.contextSeparator}${t.context}`;v.push(O),f&&(v.push(O+x),t.ordinal&&x.indexOf(S)===0&&v.push(O+x.replace(S,this.options.pluralSeparator)),d&&v.push(O+y))}}let w;for(;w=v.pop();)this.isValidLookup(n)||(r=w,n=this.getResource(L,m,w,t))}))})}),{res:n,usedKey:s,exactUsedKey:r,usedLng:a,usedNS:o}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,n,s):this.resourceStore.getResource(e,t,n,s)}getUsedParamsDetails(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],n=e.replace&&!h(e.replace);let s=n?e.replace:e;if(n&&typeof e.count<"u"&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!n){s={...s};for(const r of t)delete s[r]}return s}static hasDefaultValue(e){const t="defaultValue";for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,t.length)&&e[n]!==void 0)return!0;return!1}}const q=i=>i.charAt(0).toUpperCase()+i.slice(1);class ge{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=N.create("languageUtils")}getScriptPartFromCode(e){if(e=J(e),!e||e.indexOf("-")<0)return null;const t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=J(e),!e||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(h(e)&&e.indexOf("-")>-1){if(typeof Intl<"u"&&typeof Intl.getCanonicalLocales<"u")try{let s=Intl.getCanonicalLocales(e)[0];if(s&&this.options.lowerCaseLng&&(s=s.toLowerCase()),s)return s}catch{}const t=["hans","hant","latn","cyrl","cans","mong","arab"];let n=e.split("-");return this.options.lowerCaseLng?n=n.map(s=>s.toLowerCase()):n.length===2?(n[0]=n[0].toLowerCase(),n[1]=n[1].toUpperCase(),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=q(n[1].toLowerCase()))):n.length===3&&(n[0]=n[0].toLowerCase(),n[1].length===2&&(n[1]=n[1].toUpperCase()),n[0]!=="sgn"&&n[2].length===2&&(n[2]=n[2].toUpperCase()),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=q(n[1].toLowerCase())),t.indexOf(n[2].toLowerCase())>-1&&(n[2]=q(n[2].toLowerCase()))),n.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(n=>{if(t)return;const s=this.formatLanguageCode(n);(!this.options.supportedLngs||this.isSupportedCode(s))&&(t=s)}),!t&&this.options.supportedLngs&&e.forEach(n=>{if(t)return;const s=this.getLanguagePartFromCode(n);if(this.isSupportedCode(s))return t=s;t=this.options.supportedLngs.find(r=>{if(r===s)return r;if(!(r.indexOf("-")<0&&s.indexOf("-")<0)&&(r.indexOf("-")>0&&s.indexOf("-")<0&&r.substring(0,r.indexOf("-"))===s||r.indexOf(s)===0&&s.length>1))return r})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),h(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}toResolveHierarchy(e,t){const n=this.getFallbackCodes(t||this.options.fallbackLng||[],e),s=[],r=a=>{a&&(this.isSupportedCode(a)?s.push(a):this.logger.warn(`rejecting language code not found in supportedLngs: ${a}`))};return h(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&r(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&r(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&r(this.getLanguagePartFromCode(e))):h(e)&&r(this.formatLanguageCode(e)),n.forEach(a=>{s.indexOf(a)<0&&r(this.formatLanguageCode(a))}),s}}let st=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],it={1:i=>+(i>1),2:i=>+(i!=1),3:i=>0,4:i=>i%10==1&&i%100!=11?0:i%10>=2&&i%10<=4&&(i%100<10||i%100>=20)?1:2,5:i=>i==0?0:i==1?1:i==2?2:i%100>=3&&i%100<=10?3:i%100>=11?4:5,6:i=>i==1?0:i>=2&&i<=4?1:2,7:i=>i==1?0:i%10>=2&&i%10<=4&&(i%100<10||i%100>=20)?1:2,8:i=>i==1?0:i==2?1:i!=8&&i!=11?2:3,9:i=>+(i>=2),10:i=>i==1?0:i==2?1:i<7?2:i<11?3:4,11:i=>i==1||i==11?0:i==2||i==12?1:i>2&&i<20?2:3,12:i=>+(i%10!=1||i%100==11),13:i=>+(i!==0),14:i=>i==1?0:i==2?1:i==3?2:3,15:i=>i%10==1&&i%100!=11?0:i%10>=2&&(i%100<10||i%100>=20)?1:2,16:i=>i%10==1&&i%100!=11?0:i!==0?1:2,17:i=>i==1||i%10==1&&i%100!=11?0:1,18:i=>i==0?0:i==1?1:2,19:i=>i==1?0:i==0||i%100>1&&i%100<11?1:i%100>10&&i%100<20?2:3,20:i=>i==1?0:i==0||i%100>0&&i%100<20?1:2,21:i=>i%100==1?1:i%100==2?2:i%100==3||i%100==4?3:0,22:i=>i==1?0:i==2?1:(i<0||i>10)&&i%10==0?2:3};const rt=["v1","v2","v3"],at=["v4"],de={zero:0,one:1,two:2,few:3,many:4,other:5},ot=()=>{const i={};return st.forEach(e=>{e.lngs.forEach(t=>{i[t]={numbers:e.nr,plurals:it[e.fc]}})}),i};class lt{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=N.create("pluralResolver"),(!this.options.compatibilityJSON||at.includes(this.options.compatibilityJSON))&&(typeof Intl>"u"||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=ot(),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.shouldUseIntlApi()){const n=J(e==="dev"?"en":e),s=t.ordinal?"ordinal":"cardinal",r=JSON.stringify({cleanedCode:n,type:s});if(r in this.pluralRulesCache)return this.pluralRulesCache[r];let a;try{a=new Intl.PluralRules(n,{type:s})}catch{if(!e.match(/-|_/))return;const l=this.languageUtils.getLanguagePartFromCode(e);a=this.getRule(l,t)}return this.pluralRulesCache[r]=a,a}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const n=this.getRule(e,t);return this.shouldUseIntlApi()?n&&n.resolvedOptions().pluralCategories.length>1:n&&n.numbers.length>1}getPluralFormsOfKey(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(e,n).map(s=>`${t}${s}`)}getSuffixes(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const n=this.getRule(e,t);return n?this.shouldUseIntlApi()?n.resolvedOptions().pluralCategories.sort((s,r)=>de[s]-de[r]).map(s=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${s}`):n.numbers.map(s=>this.getSuffix(e,s,t)):[]}getSuffix(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const s=this.getRule(e,n);return s?this.shouldUseIntlApi()?`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${s.select(t)}`:this.getSuffixRetroCompatible(s,t):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,t){const n=e.noAbs?e.plurals(t):e.plurals(Math.abs(t));let s=e.numbers[n];this.options.simplifyPluralSuffix&&e.numbers.length===2&&e.numbers[0]===1&&(s===2?s="plural":s===1&&(s=""));const r=()=>this.options.prepend&&s.toString()?this.options.prepend+s.toString():s.toString();return this.options.compatibilityJSON==="v1"?s===1?"":typeof s=="number"?`_plural_${s.toString()}`:r():this.options.compatibilityJSON==="v2"||this.options.simplifyPluralSuffix&&e.numbers.length===2&&e.numbers[0]===1?r():this.options.prepend&&n.toString()?this.options.prepend+n.toString():n.toString()}shouldUseIntlApi(){return!rt.includes(this.options.compatibilityJSON)}}const he=function(i,e,t){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:".",s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,r=qe(i,e,t);return!r&&s&&h(t)&&(r=_(i,t,n),r===void 0&&(r=_(e,t,n))),r},G=i=>i.replace(/\$/g,"$$$$");class ut{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=N.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(t=>t),this.init(e)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:n,useRawValueToEscape:s,prefix:r,prefixEscaped:a,suffix:o,suffixEscaped:l,formatSeparator:u,unescapeSuffix:c,unescapePrefix:g,nestingPrefix:f,nestingPrefixEscaped:d,nestingSuffix:p,nestingSuffixEscaped:b,nestingOptionsSeparator:m,maxReplaces:L,alwaysFormat:v}=e.interpolation;this.escape=t!==void 0?t:Ze,this.escapeValue=n!==void 0?n:!0,this.useRawValueToEscape=s!==void 0?s:!1,this.prefix=r?$(r):a||"{{",this.suffix=o?$(o):l||"}}",this.formatSeparator=u||",",this.unescapePrefix=c?"":g||"-",this.unescapeSuffix=this.unescapePrefix?"":c||"",this.nestingPrefix=f?$(f):d||$("$t("),this.nestingSuffix=p?$(p):b||$(")"),this.nestingOptionsSeparator=m||",",this.maxReplaces=L||1e3,this.alwaysFormat=v!==void 0?v:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(t,n)=>t&&t.source===n?(t.lastIndex=0,t):new RegExp(n,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,n,s){let r,a,o;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=d=>{if(d.indexOf(this.formatSeparator)<0){const L=he(t,l,d,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(L,void 0,n,{...s,...t,interpolationkey:d}):L}const p=d.split(this.formatSeparator),b=p.shift().trim(),m=p.join(this.formatSeparator).trim();return this.format(he(t,l,b,this.options.keySeparator,this.options.ignoreJSONStructure),m,n,{...s,...t,interpolationkey:b})};this.resetRegExp();const c=s&&s.missingInterpolationHandler||this.options.missingInterpolationHandler,g=s&&s.interpolation&&s.interpolation.skipOnVariables!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:d=>G(d)},{regex:this.regexp,safeValue:d=>this.escapeValue?G(this.escape(d)):G(d)}].forEach(d=>{for(o=0;r=d.regex.exec(e);){const p=r[1].trim();if(a=u(p),a===void 0)if(typeof c=="function"){const m=c(e,r,s);a=h(m)?m:""}else if(s&&Object.prototype.hasOwnProperty.call(s,p))a="";else if(g){a=r[0];continue}else this.logger.warn(`missed to pass in variable ${p} for interpolating ${e}`),a="";else!h(a)&&!this.useRawValueToEscape&&(a=ae(a));const b=d.safeValue(a);if(e=e.replace(r[0],b),g?(d.regex.lastIndex+=a.length,d.regex.lastIndex-=r[0].length):d.regex.lastIndex=0,o++,o>=this.maxReplaces)break}}),e}nest(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s,r,a;const o=(l,u)=>{const c=this.nestingOptionsSeparator;if(l.indexOf(c)<0)return l;const g=l.split(new RegExp(`${c}[ ]*{`));let f=`{${g[1]}`;l=g[0],f=this.interpolate(f,a);const d=f.match(/'/g),p=f.match(/"/g);(d&&d.length%2===0&&!p||p.length%2!==0)&&(f=f.replace(/'/g,'"'));try{a=JSON.parse(f),u&&(a={...u,...a})}catch(b){return this.logger.warn(`failed parsing options string in nesting for key ${l}`,b),`${l}${c}${f}`}return a.defaultValue&&a.defaultValue.indexOf(this.prefix)>-1&&delete a.defaultValue,l};for(;s=this.nestingRegexp.exec(e);){let l=[];a={...n},a=a.replace&&!h(a.replace)?a.replace:a,a.applyPostProcessor=!1,delete a.defaultValue;let u=!1;if(s[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(s[1])){const c=s[1].split(this.formatSeparator).map(g=>g.trim());s[1]=c.shift(),l=c,u=!0}if(r=t(o.call(this,s[1].trim(),a),a),r&&s[0]===e&&!h(r))return r;h(r)||(r=ae(r)),r||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${e}`),r=""),u&&(r=l.reduce((c,g)=>this.format(c,g,n.lng,{...n,interpolationkey:s[1].trim()}),r.trim())),e=e.replace(s[0],r),this.regexp.lastIndex=0}return e}}const ft=i=>{let e=i.toLowerCase().trim();const t={};if(i.indexOf("(")>-1){const n=i.split("(");e=n[0].toLowerCase().trim();const s=n[1].substring(0,n[1].length-1);e==="currency"&&s.indexOf(":")<0?t.currency||(t.currency=s.trim()):e==="relativetime"&&s.indexOf(":")<0?t.range||(t.range=s.trim()):s.split(";").forEach(a=>{if(a){const[o,...l]=a.split(":"),u=l.join(":").trim().replace(/^'+|'+$/g,""),c=o.trim();t[c]||(t[c]=u),u==="false"&&(t[c]=!1),u==="true"&&(t[c]=!0),isNaN(u)||(t[c]=parseInt(u,10))}})}return{formatName:e,formatOptions:t}},I=i=>{const e={};return(t,n,s)=>{let r=s;s&&s.interpolationkey&&s.formatParams&&s.formatParams[s.interpolationkey]&&s[s.interpolationkey]&&(r={...r,[s.interpolationkey]:void 0});const a=n+JSON.stringify(r);let o=e[a];return o||(o=i(J(n),s),e[a]=o),o(t)}};class ct{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=N.create("formatter"),this.options=e,this.formats={number:I((t,n)=>{const s=new Intl.NumberFormat(t,{...n});return r=>s.format(r)}),currency:I((t,n)=>{const s=new Intl.NumberFormat(t,{...n,style:"currency"});return r=>s.format(r)}),datetime:I((t,n)=>{const s=new Intl.DateTimeFormat(t,{...n});return r=>s.format(r)}),relativetime:I((t,n)=>{const s=new Intl.RelativeTimeFormat(t,{...n});return r=>s.format(r,n.range||"day")}),list:I((t,n)=>{const s=new Intl.ListFormat(t,{...n});return r=>s.format(r)})},this.init(e)}init(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=I(t)}format(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const r=t.split(this.formatSeparator);if(r.length>1&&r[0].indexOf("(")>1&&r[0].indexOf(")")<0&&r.find(o=>o.indexOf(")")>-1)){const o=r.findIndex(l=>l.indexOf(")")>-1);r[0]=[r[0],...r.splice(1,o)].join(this.formatSeparator)}return r.reduce((o,l)=>{const{formatName:u,formatOptions:c}=ft(l);if(this.formats[u]){let g=o;try{const f=s&&s.formatParams&&s.formatParams[s.interpolationkey]||{},d=f.locale||f.lng||s.locale||s.lng||n;g=this.formats[u](o,d,{...c,...s,...f})}catch(f){this.logger.warn(f)}return g}else this.logger.warn(`there was no format function for ${u}`);return o},e)}}const gt=(i,e)=>{i.pending[e]!==void 0&&(delete i.pending[e],i.pendingCount--)};class dt extends Y{constructor(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=n,this.languageUtils=n.languageUtils,this.options=s,this.logger=N.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=s.maxParallelReads||10,this.readingCalls=0,this.maxRetries=s.maxRetries>=0?s.maxRetries:5,this.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(n,s.backend,s)}queueLoad(e,t,n,s){const r={},a={},o={},l={};return e.forEach(u=>{let c=!0;t.forEach(g=>{const f=`${u}|${g}`;!n.reload&&this.store.hasResourceBundle(u,g)?this.state[f]=2:this.state[f]<0||(this.state[f]===1?a[f]===void 0&&(a[f]=!0):(this.state[f]=1,c=!1,a[f]===void 0&&(a[f]=!0),r[f]===void 0&&(r[f]=!0),l[g]===void 0&&(l[g]=!0)))}),c||(o[u]=!0)}),(Object.keys(r).length||Object.keys(a).length)&&this.queue.push({pending:a,pendingCount:Object.keys(a).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(r),pending:Object.keys(a),toLoadLanguages:Object.keys(o),toLoadNamespaces:Object.keys(l)}}loaded(e,t,n){const s=e.split("|"),r=s[0],a=s[1];t&&this.emit("failedLoading",r,a,t),!t&&n&&this.store.addResourceBundle(r,a,n,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&n&&(this.state[e]=0);const o={};this.queue.forEach(l=>{Ye(l.loaded,[r],a),gt(l,e),t&&l.errors.push(t),l.pendingCount===0&&!l.done&&(Object.keys(l.loaded).forEach(u=>{o[u]||(o[u]={});const c=l.loaded[u];c.length&&c.forEach(g=>{o[u][g]===void 0&&(o[u][g]=!0)})}),l.done=!0,l.errors.length?l.callback(l.errors):l.callback())}),this.emit("loaded",o),this.queue=this.queue.filter(l=>!l.done)}read(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,a=arguments.length>5?arguments[5]:void 0;if(!e.length)return a(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:n,tried:s,wait:r,callback:a});return}this.readingCalls++;const o=(u,c)=>{if(this.readingCalls--,this.waitingReads.length>0){const g=this.waitingReads.shift();this.read(g.lng,g.ns,g.fcName,g.tried,g.wait,g.callback)}if(u&&c&&s<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,n,s+1,r*2,a)},r);return}a(u,c)},l=this.backend[n].bind(this.backend);if(l.length===2){try{const u=l(e,t);u&&typeof u.then=="function"?u.then(c=>o(null,c)).catch(o):o(null,u)}catch(u){o(u)}return}return l(e,t,o)}prepareLoading(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),s&&s();h(e)&&(e=this.languageUtils.toResolveHierarchy(e)),h(t)&&(t=[t]);const r=this.queueLoad(e,t,n,s);if(!r.toLoad.length)return r.pending.length||s(),null;r.toLoad.forEach(a=>{this.loadOne(a)})}load(e,t,n){this.prepareLoading(e,t,{},n)}reload(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}loadOne(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const n=e.split("|"),s=n[0],r=n[1];this.read(s,r,"read",void 0,void 0,(a,o)=>{a&&this.logger.warn(`${t}loading namespace ${r} for language ${s} failed`,a),!a&&o&&this.logger.log(`${t}loaded namespace ${r} for language ${s}`,o),this.loaded(e,a,o)})}saveMissing(e,t,n,s,r){let a=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},o=arguments.length>6&&arguments[6]!==void 0?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t)){this.logger.warn(`did not save key "${n}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(n==null||n==="")){if(this.backend&&this.backend.create){const l={...a,isUpdate:r},u=this.backend.create.bind(this.backend);if(u.length<6)try{let c;u.length===5?c=u(e,t,n,s,l):c=u(e,t,n,s),c&&typeof c.then=="function"?c.then(g=>o(null,g)).catch(o):o(null,c)}catch(c){o(c)}else u(e,t,n,s,o,l)}!e||!e[0]||this.store.addResource(e[0],t,n,s)}}}const pe=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:i=>{let e={};if(typeof i[1]=="object"&&(e=i[1]),h(i[1])&&(e.defaultValue=i[1]),h(i[2])&&(e.tDescription=i[2]),typeof i[2]=="object"||typeof i[3]=="object"){const t=i[3]||i[2];Object.keys(t).forEach(n=>{e[n]=t[n]})}return e},interpolation:{escapeValue:!0,format:i=>i,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),me=i=>(h(i.ns)&&(i.ns=[i.ns]),h(i.fallbackLng)&&(i.fallbackLng=[i.fallbackLng]),h(i.fallbackNS)&&(i.fallbackNS=[i.fallbackNS]),i.supportedLngs&&i.supportedLngs.indexOf("cimode")<0&&(i.supportedLngs=i.supportedLngs.concat(["cimode"])),i),H=()=>{},ht=i=>{Object.getOwnPropertyNames(Object.getPrototypeOf(i)).forEach(t=>{typeof i[t]=="function"&&(i[t]=i[t].bind(i))})};class K extends Y{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=me(e),this.services={},this.logger=N,this.modules={external:[]},ht(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,typeof t=="function"&&(n=t,t={}),!t.defaultNS&&t.defaultNS!==!1&&t.ns&&(h(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));const s=pe();this.options={...s,...this.options,...me(t)},this.options.compatibilityAPI!=="v1"&&(this.options.interpolation={...s.interpolation,...this.options.interpolation}),t.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=t.keySeparator),t.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=t.nsSeparator);const r=c=>c?typeof c=="function"?new c:c:null;if(!this.options.isClone){this.modules.logger?N.init(r(this.modules.logger),this.options):N.init(null,this.options);let c;this.modules.formatter?c=this.modules.formatter:typeof Intl<"u"&&(c=ct);const g=new ge(this.options);this.store=new fe(this.options.resources,this.options);const f=this.services;f.logger=N,f.resourceStore=this.store,f.languageUtils=g,f.pluralResolver=new lt(g,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),c&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(f.formatter=r(c),f.formatter.init(f,this.options),this.options.interpolation.format=f.formatter.format.bind(f.formatter)),f.interpolator=new ut(this.options),f.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},f.backendConnector=new dt(r(this.modules.backend),f.resourceStore,f,this.options),f.backendConnector.on("*",function(d){for(var p=arguments.length,b=new Array(p>1?p-1:0),m=1;m<p;m++)b[m-1]=arguments[m];e.emit(d,...b)}),this.modules.languageDetector&&(f.languageDetector=r(this.modules.languageDetector),f.languageDetector.init&&f.languageDetector.init(f,this.options.detection,this.options)),this.modules.i18nFormat&&(f.i18nFormat=r(this.modules.i18nFormat),f.i18nFormat.init&&f.i18nFormat.init(this)),this.translator=new Q(this.services,this.options),this.translator.on("*",function(d){for(var p=arguments.length,b=new Array(p>1?p-1:0),m=1;m<p;m++)b[m-1]=arguments[m];e.emit(d,...b)}),this.modules.external.forEach(d=>{d.init&&d.init(this)})}if(this.format=this.options.interpolation.format,n||(n=H),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const c=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);c.length>0&&c[0]!=="dev"&&(this.options.lng=c[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(c=>{this[c]=function(){return e.store[c](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(c=>{this[c]=function(){return e.store[c](...arguments),e}});const l=D(),u=()=>{const c=(g,f)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),l.resolve(f),n(g,f)};if(this.languages&&this.options.compatibilityAPI!=="v1"&&!this.isInitialized)return c(null,this.t.bind(this));this.changeLanguage(this.options.lng,c)};return this.options.resources||!this.options.initImmediate?u():setTimeout(u,0),l}loadResources(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:H;const s=h(e)?e:this.language;if(typeof e=="function"&&(n=e),!this.options.resources||this.options.partialBundledLanguages){if(s&&s.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return n();const r=[],a=o=>{if(!o||o==="cimode")return;this.services.languageUtils.toResolveHierarchy(o).forEach(u=>{u!=="cimode"&&r.indexOf(u)<0&&r.push(u)})};s?a(s):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(l=>a(l)),this.options.preload&&this.options.preload.forEach(o=>a(o)),this.services.backendConnector.load(r,this.options.ns,o=>{!o&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),n(o)})}else n(null)}reloadResources(e,t,n){const s=D();return typeof e=="function"&&(n=e,e=void 0),typeof t=="function"&&(n=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),n||(n=H),this.services.backendConnector.reload(e,t,r=>{s.resolve(),n(r)}),s}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&Oe.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1))for(let t=0;t<this.languages.length;t++){const n=this.languages[t];if(!(["cimode","dev"].indexOf(n)>-1)&&this.store.hasLanguageSomeTranslations(n)){this.resolvedLanguage=n;break}}}changeLanguage(e,t){var n=this;this.isLanguageChangingTo=e;const s=D();this.emit("languageChanging",e);const r=l=>{this.language=l,this.languages=this.services.languageUtils.toResolveHierarchy(l),this.resolvedLanguage=void 0,this.setResolvedLanguage(l)},a=(l,u)=>{u?(r(u),this.translator.changeLanguage(u),this.isLanguageChangingTo=void 0,this.emit("languageChanged",u),this.logger.log("languageChanged",u)):this.isLanguageChangingTo=void 0,s.resolve(function(){return n.t(...arguments)}),t&&t(l,function(){return n.t(...arguments)})},o=l=>{!e&&!l&&this.services.languageDetector&&(l=[]);const u=h(l)?l:this.services.languageUtils.getBestMatchFromCodes(l);u&&(this.language||r(u),this.translator.language||this.translator.changeLanguage(u),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(u)),this.loadResources(u,c=>{a(c,u)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?o(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(o):this.services.languageDetector.detect(o):o(e),s}getFixedT(e,t,n){var s=this;const r=function(a,o){let l;if(typeof o!="object"){for(var u=arguments.length,c=new Array(u>2?u-2:0),g=2;g<u;g++)c[g-2]=arguments[g];l=s.options.overloadTranslationOptionHandler([a,o].concat(c))}else l={...o};l.lng=l.lng||r.lng,l.lngs=l.lngs||r.lngs,l.ns=l.ns||r.ns,l.keyPrefix!==""&&(l.keyPrefix=l.keyPrefix||n||r.keyPrefix);const f=s.options.keySeparator||".";let d;return l.keyPrefix&&Array.isArray(a)?d=a.map(p=>`${l.keyPrefix}${f}${p}`):d=l.keyPrefix?`${l.keyPrefix}${f}${a}`:a,s.t(d,l)};return h(e)?r.lng=e:r.lngs=e,r.ns=t,r.keyPrefix=n,r}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const n=t.lng||this.resolvedLanguage||this.languages[0],s=this.options?this.options.fallbackLng:!1,r=this.languages[this.languages.length-1];if(n.toLowerCase()==="cimode")return!0;const a=(o,l)=>{const u=this.services.backendConnector.state[`${o}|${l}`];return u===-1||u===0||u===2};if(t.precheck){const o=t.precheck(this,a);if(o!==void 0)return o}return!!(this.hasResourceBundle(n,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||a(n,e)&&(!s||a(r,e)))}loadNamespaces(e,t){const n=D();return this.options.ns?(h(e)&&(e=[e]),e.forEach(s=>{this.options.ns.indexOf(s)<0&&this.options.ns.push(s)}),this.loadResources(s=>{n.resolve(),t&&t(s)}),n):(t&&t(),Promise.resolve())}loadLanguages(e,t){const n=D();h(e)&&(e=[e]);const s=this.options.preload||[],r=e.filter(a=>s.indexOf(a)<0&&this.services.languageUtils.isSupportedCode(a));return r.length?(this.options.preload=s.concat(r),this.loadResources(a=>{n.resolve(),t&&t(a)}),n):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";const t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],n=this.services&&this.services.languageUtils||new ge(pe());return t.indexOf(n.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new K(e,t)}cloneInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:H;const n=e.forkResourceStore;n&&delete e.forkResourceStore;const s={...this.options,...e,isClone:!0},r=new K(s);return(e.debug!==void 0||e.prefix!==void 0)&&(r.logger=r.logger.clone(e)),["store","services","language"].forEach(o=>{r[o]=this[o]}),r.services={...this.services},r.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},n&&(r.store=new fe(this.store.data,s),r.services.resourceStore=r.store),r.translator=new Q(r.services,s),r.translator.on("*",function(o){for(var l=arguments.length,u=new Array(l>1?l-1:0),c=1;c<l;c++)u[c-1]=arguments[c];r.emit(o,...u)}),r.init(s,t),r.translator.options=s,r.translator.backendConnector.services.utils={hasLoadedNamespace:r.hasLoadedNamespace.bind(r)},r}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const C=K.createInstance();C.createInstance=K.createInstance;C.createInstance;C.dir;C.init;C.loadResources;C.reloadResources;C.use;C.changeLanguage;C.getFixedT;C.t;C.exists;C.setDefaultNamespace;C.hasLoadedNamespace;C.loadNamespaces;C.loadLanguages;var Ce=[],pt=Ce.forEach,mt=Ce.slice;function xt(i){return pt.call(mt.call(arguments,1),function(e){if(e)for(var t in e)i[t]===void 0&&(i[t]=e[t])}),i}var xe=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,bt=function(e,t,n){var s=n||{};s.path=s.path||"/";var r=encodeURIComponent(t),a="".concat(e,"=").concat(r);if(s.maxAge>0){var o=s.maxAge-0;if(Number.isNaN(o))throw new Error("maxAge should be a Number");a+="; Max-Age=".concat(Math.floor(o))}if(s.domain){if(!xe.test(s.domain))throw new TypeError("option domain is invalid");a+="; Domain=".concat(s.domain)}if(s.path){if(!xe.test(s.path))throw new TypeError("option path is invalid");a+="; Path=".concat(s.path)}if(s.expires){if(typeof s.expires.toUTCString!="function")throw new TypeError("option expires is invalid");a+="; Expires=".concat(s.expires.toUTCString())}if(s.httpOnly&&(a+="; HttpOnly"),s.secure&&(a+="; Secure"),s.sameSite){var l=typeof s.sameSite=="string"?s.sameSite.toLowerCase():s.sameSite;switch(l){case!0:a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"strict":a+="; SameSite=Strict";break;case"none":a+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return a},be={create:function(e,t,n,s){var r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};n&&(r.expires=new Date,r.expires.setTime(r.expires.getTime()+n*60*1e3)),s&&(r.domain=s),document.cookie=bt(e,encodeURIComponent(t),r)},read:function(e){for(var t="".concat(e,"="),n=document.cookie.split(";"),s=0;s<n.length;s++){for(var r=n[s];r.charAt(0)===" ";)r=r.substring(1,r.length);if(r.indexOf(t)===0)return r.substring(t.length,r.length)}return null},remove:function(e){this.create(e,"",-1)}},yt={name:"cookie",lookup:function(e){var t;if(e.lookupCookie&&typeof document<"u"){var n=be.read(e.lookupCookie);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupCookie&&typeof document<"u"&&be.create(t.lookupCookie,e,t.cookieMinutes,t.cookieDomain,t.cookieOptions)}},St={name:"querystring",lookup:function(e){var t;if(typeof window<"u"){var n=window.location.search;!window.location.search&&window.location.hash&&window.location.hash.indexOf("?")>-1&&(n=window.location.hash.substring(window.location.hash.indexOf("?")));for(var s=n.substring(1),r=s.split("&"),a=0;a<r.length;a++){var o=r[a].indexOf("=");if(o>0){var l=r[a].substring(0,o);l===e.lookupQuerystring&&(t=r[a].substring(o+1))}}}return t}},j=null,ye=function(){if(j!==null)return j;try{j=window!=="undefined"&&window.localStorage!==null;var e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch{j=!1}return j},vt={name:"localStorage",lookup:function(e){var t;if(e.lookupLocalStorage&&ye()){var n=window.localStorage.getItem(e.lookupLocalStorage);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupLocalStorage&&ye()&&window.localStorage.setItem(t.lookupLocalStorage,e)}},U=null,Se=function(){if(U!==null)return U;try{U=window!=="undefined"&&window.sessionStorage!==null;var e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch{U=!1}return U},wt={name:"sessionStorage",lookup:function(e){var t;if(e.lookupSessionStorage&&Se()){var n=window.sessionStorage.getItem(e.lookupSessionStorage);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupSessionStorage&&Se()&&window.sessionStorage.setItem(t.lookupSessionStorage,e)}},Lt={name:"navigator",lookup:function(e){var t=[];if(typeof navigator<"u"){if(navigator.languages)for(var n=0;n<navigator.languages.length;n++)t.push(navigator.languages[n]);navigator.userLanguage&&t.push(navigator.userLanguage),navigator.language&&t.push(navigator.language)}return t.length>0?t:void 0}},Ot={name:"htmlTag",lookup:function(e){var t,n=e.htmlTag||(typeof document<"u"?document.documentElement:null);return n&&typeof n.getAttribute=="function"&&(t=n.getAttribute("lang")),t}},Ct={name:"path",lookup:function(e){var t;if(typeof window<"u"){var n=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(n instanceof Array)if(typeof e.lookupFromPathIndex=="number"){if(typeof n[e.lookupFromPathIndex]!="string")return;t=n[e.lookupFromPathIndex].replace("/","")}else t=n[0].replace("/","")}return t}},kt={name:"subdomain",lookup:function(e){var t=typeof e.lookupFromSubdomainIndex=="number"?e.lookupFromSubdomainIndex+1:1,n=typeof window<"u"&&window.location&&window.location.hostname&&window.location.hostname.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(n)return n[t]}},ke=!1;try{document.cookie,ke=!0}catch{}var Ne=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];ke||Ne.splice(1,1);function Nt(){return{order:Ne,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:function(e){return e}}}var Rt=function(){function i(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};Ie(this,i),this.type="languageDetector",this.detectors={},this.init(e,t)}return $e(i,[{key:"init",value:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=t||{languageUtils:{}},this.options=xt(n,this.options||{},Nt()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=function(r){return r.replace("-","_")}),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=s,this.addDetector(yt),this.addDetector(St),this.addDetector(vt),this.addDetector(wt),this.addDetector(Lt),this.addDetector(Ot),this.addDetector(Ct),this.addDetector(kt)}},{key:"addDetector",value:function(t){return this.detectors[t.name]=t,this}},{key:"detect",value:function(t){var n=this;t||(t=this.options.order);var s=[];return t.forEach(function(r){if(n.detectors[r]){var a=n.detectors[r].lookup(n.options);a&&typeof a=="string"&&(a=[a]),a&&(s=s.concat(a))}}),s=s.map(function(r){return n.options.convertDetectedLanguage(r)}),this.services.languageUtils.getBestMatchFromCodes?s:s.length>0?s[0]:null}},{key:"cacheUserLanguage",value:function(t,n){var s=this;n||(n=this.options.caches),n&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(t)>-1||n.forEach(function(r){s.detectors[r]&&s.detectors[r].cacheUserLanguage(t,s.options)}))}}])}();Rt.type="languageDetector";export{Rt as B,Et as a,C as i,Ft as u};
