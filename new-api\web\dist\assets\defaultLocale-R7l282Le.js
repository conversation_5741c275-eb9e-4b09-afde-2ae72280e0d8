function q(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)}function A(t,n){if((o=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var o,i=t.slice(0,o);return[i.length>1?i[0]+i.slice(2):i,+t.slice(o+1)]}function H(t){return t=A(Math.abs(t)),t?t[1]:NaN}function J(t,n){return function(o,i){for(var a=o.length,h=[],u=0,d=t[0],p=0;a>0&&d>0&&(p+d+1>i&&(d=Math.max(1,i-p)),h.push(o.substring(a-=d,a+d)),!((p+=d+1)>i));)d=t[u=(u+1)%t.length];return h.reverse().join(n)}}function K(t){return function(n){return n.replace(/[0-9]/g,function(o){return t[+o]})}}var Q=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function E(t){if(!(n=Q.exec(t)))throw new Error("invalid format: "+t);var n;return new N({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}E.prototype=N.prototype;function N(t){this.fill=t.fill===void 0?" ":t.fill+"",this.align=t.align===void 0?">":t.align+"",this.sign=t.sign===void 0?"-":t.sign+"",this.symbol=t.symbol===void 0?"":t.symbol+"",this.zero=!!t.zero,this.width=t.width===void 0?void 0:+t.width,this.comma=!!t.comma,this.precision=t.precision===void 0?void 0:+t.precision,this.trim=!!t.trim,this.type=t.type===void 0?"":t.type+""}N.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function V(t){t:for(var n=t.length,o=1,i=-1,a;o<n;++o)switch(t[o]){case".":i=a=o;break;case"0":i===0&&(i=o),a=o;break;default:if(!+t[o])break t;i>0&&(i=0);break}return i>0?t.slice(0,i)+t.slice(a+1):t}var O;function W(t,n){var o=A(t,n);if(!o)return t+"";var i=o[0],a=o[1],h=a-(O=Math.max(-8,Math.min(8,Math.floor(a/3)))*3)+1,u=i.length;return h===u?i:h>u?i+new Array(h-u+1).join("0"):h>0?i.slice(0,h)+"."+i.slice(h):"0."+new Array(1-h).join("0")+A(t,Math.max(0,n+h-1))[0]}function D(t,n){var o=A(t,n);if(!o)return t+"";var i=o[0],a=o[1];return a<0?"0."+new Array(-a).join("0")+i:i.length>a+1?i.slice(0,a+1)+"."+i.slice(a+1):i+new Array(a-i.length+2).join("0")}const G={"%":(t,n)=>(t*100).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:q,e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>D(t*100,n),r:D,s:W,X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function I(t){return t}var X=Array.prototype.map,B=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function _(t){var n=t.grouping===void 0||t.thousands===void 0?I:J(X.call(t.grouping,Number),t.thousands+""),o=t.currency===void 0?"":t.currency[0]+"",i=t.currency===void 0?"":t.currency[1]+"",a=t.decimal===void 0?".":t.decimal+"",h=t.numerals===void 0?I:K(X.call(t.numerals,String)),u=t.percent===void 0?"%":t.percent+"",d=t.minus===void 0?"−":t.minus+"",p=t.nan===void 0?"NaN":t.nan+"";function $(e){e=E(e);var x=e.fill,M=e.align,s=e.sign,w=e.symbol,l=e.zero,S=e.width,j=e.comma,g=e.precision,F=e.trim,f=e.type;f==="n"?(j=!0,f="g"):G[f]||(g===void 0&&(g=12),F=!0,f="g"),(l||x==="0"&&M==="=")&&(l=!0,x="0",M="=");var U=w==="$"?o:w==="#"&&/[boxX]/.test(f)?"0"+f.toLowerCase():"",Y=w==="$"?i:/[%p]/.test(f)?u:"",L=G[f],Z=/[defgprs%]/.test(f);g=g===void 0?6:/[gprs]/.test(f)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g));function T(r){var y=U,m=Y,b,C,k;if(f==="c")m=L(r)+m,r="";else{r=+r;var v=r<0||1/r<0;if(r=isNaN(r)?p:L(Math.abs(r),g),F&&(r=V(r)),v&&+r==0&&s!=="+"&&(v=!1),y=(v?s==="("?s:d:s==="-"||s==="("?"":s)+y,m=(f==="s"?B[8+O/3]:"")+m+(v&&s==="("?")":""),Z){for(b=-1,C=r.length;++b<C;)if(k=r.charCodeAt(b),48>k||k>57){m=(k===46?a+r.slice(b+1):r.slice(b))+m,r=r.slice(0,b);break}}}j&&!l&&(r=n(r,1/0));var P=y.length+r.length+m.length,c=P<S?new Array(S-P+1).join(x):"";switch(j&&l&&(r=n(c+r,c.length?S-m.length:1/0),c=""),M){case"<":r=y+r+m+c;break;case"=":r=y+c+r+m;break;case"^":r=c.slice(0,P=c.length>>1)+y+r+m+c.slice(P);break;default:r=c+y+r+m;break}return h(r)}return T.toString=function(){return e+""},T}function R(e,x){var M=$((e=E(e),e.type="f",e)),s=Math.max(-8,Math.min(8,Math.floor(H(x)/3)))*3,w=Math.pow(10,-s),l=B[8+s/3];return function(S){return M(w*S)+l}}return{format:$,formatPrefix:R}}var z,tt,nt;rt({thousands:",",grouping:[3],currency:["$",""]});function rt(t){return z=_(t),tt=z.format,nt=z.formatPrefix,z}export{E as a,nt as b,H as e,tt as f};
