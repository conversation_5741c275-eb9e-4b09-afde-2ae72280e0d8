import{j as t,I as $a,c as Ae,d as Ra,e as Tt,f as qa,g as La,h as Pa,i as Ha,E as G,D as Et,B as Ie,k as Ua,l as Mt,M as Qa,F as Z,C as H,A as St,S as _t,T as Bt,m as Y,n as za,o as pe,p as At,q as It,r as Oa,s as Va,t as Wa,P as Ka}from"./semi-ui-Csx8wKaA.js";import{r as l,u as Ga,R as Za}from"./react-core-DskXcPn0.js";import{l as $t,V as J,b as X}from"./visactor-Csuqn0ps.js";import{m as $e}from"./tools-C3llIrvJ.js";import{I as ee,a as te}from"./IllustrationConstruction-BxqVZ4HA.js";import{aO as be,aP as Ya,aQ as Ja,aR as Xa,aS as es,aT as ge,aU as ae,aV as E,aW as N,aX as ts,aY as as,aZ as xe,a_ as ss,a$ as os,A as Re,s as qe,b0 as Le,b1 as ls,b2 as rs,b3 as ns,b4 as is,b5 as cs}from"./index-DTzYmM8W.js";import{u as us}from"./i18n-bne0o_C4.js";import"./react-components-C55tCU1e.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ds=[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]],ms=be("chart-pie",ds);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hs=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]],ps=be("external-link",hs);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gs=[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]],xs=be("server",gs);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bs=[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]],fs=be("wallet",bs),Rt="Inter,-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif",He="theme-mode",qt=(c,i,m)=>{const n=c&&getComputedStyle(document.body).getPropertyValue(c)||i;return n&&!isNaN(n[0])?`rgba(${n})`:n},Cs=(c,i,m)=>{new MutationObserver(n=>{n.forEach(h=>{h.attributeName===i&&m(h)})}).observe(c,{attributes:!0})},ys=(c,i,m,n)=>i.map((h,o)=>{const{scheme:f}=m[o];return Object.assign(Object.assign({},h),{scheme:h.scheme.map((w,M)=>qt(typeof w=="object"?w[c]:w,f==null?void 0:f[M]))})}),vs=(c,i,m,n)=>{const h={};return Object.keys(i).forEach(o=>{const f=typeof i[o]=="object"?i[o][c]:i[o];h[o]=qt(f,m[o])}),h},Lt=()=>document.body.hasAttribute(He)&&document.body.getAttribute(He)==="dark"?"dark":"light",js=c=>`semiDesign${c[0].toUpperCase()}${c.slice(1)}`,ks=c=>{new MutationObserver(i=>{i.forEach(m=>{var n;if(m.addedNodes.length===1){const h=m.addedNodes[0];h.tagName==="LINK"&&((n=h.getAttribute)===null||n===void 0?void 0:n.call(h,"semi-theme-switcher"))==="true"&&c(m,h)}})}).observe(document.body,{childList:!0})},Fs={default:{dataScheme:$t.colorScheme.default.dataScheme,palette:{backgroundColor:"#16161a",borderColor:"rgba(255,255,255,0.08)",shadowColor:"rgba(0,0,0,0.25)",hoverBackgroundColor:"rgba(255,255,255,0.12)",sliderRailColor:"rgba(255,255,255,0.12)",sliderHandleColor:"#e4e7f5",sliderTrackColor:"rgba(84,169,255,1)",popupBackgroundColor:"#43444a",primaryFontColor:"rgba(249,249,249,1)",secondaryFontColor:"rgba(249,249,249,0.8)",tertiaryFontColor:"rgba(249,249,249,0.6)",axisLabelFontColor:"rgba(249,249,249,0.6)",disableFontColor:"rgba(249,249,249,0.35)",axisMarkerFontColor:"#16161a",axisGridColor:"rgba(255,255,255,0.08)",axisDomainColor:"rgba(255,255,255,0.08)",dataZoomHandleStrokeColor:"rgba(46,50,56,0.13)",dataZoomChartColor:"rgba(255,255,255,0.16)",playerControllerColor:"rgba(84,169,255,1)",axisMarkerBackgroundColor:"rgba(249,249,249,1)",markLabelBackgroundColor:"rgba(255,255,255,0.08)",markLineStrokeColor:"rgba(249,249,249,0.8)",dangerColor:"rgba(252,114,90,1)",warningColor:"rgba(255,174,67,1)",successColor:"rgba(93,194,100,1)",infoColor:"rgba(84,169,255,1)"}}},Ds={name:"semiDesignDark",description:"Semi Design - dark",type:"dark",fontFamily:Rt,colorScheme:Fs},Ns={default:{dataScheme:$t.colorScheme.default.dataScheme,palette:{backgroundColor:"rgba(255,255,255,1)",borderColor:"rgba(28,31,35,0.08)",shadowColor:"rgba(0,0,0,0.1)",hoverBackgroundColor:"rgba(46,50,56,0.05)",sliderRailColor:"rgba(46,50,56,0.05)",sliderHandleColor:"rgba(255,255,255,1)",sliderTrackColor:"rgba(0,100,250,1)",popupBackgroundColor:"rgba(255,255,255,1)",primaryFontColor:"rgba(28,31,35,1)",secondaryFontColor:"rgba(28,31,35,0.8)",tertiaryFontColor:"rgba(28,31,35,0.62)",axisLabelFontColor:"rgba(28,31,35,0.62)",disableFontColor:"rgba(28,31,35,0.35)",axisMarkerFontColor:"rgba(255,255,255,1)",axisGridColor:"rgba(28,31,35,0.08)",axisDomainColor:"rgba(28,31,35,0.15)",dataZoomHandleStrokeColor:"rgba(46,50,56,0.13)",dataZoomChartColor:"rgba(46,50,56,0.09)",playerControllerColor:"rgba(0,100,250,1)",axisMarkerBackgroundColor:"rgba(28,31,35,1)",markLabelBackgroundColor:"rgba(28,31,35,0.08)",markLineStrokeColor:"rgba(28,31,35,0.8)",dangerColor:"rgba(249,57,32,1)",warningColor:"rgba(252,136,0,1)",successColor:"rgba(59,179,70,1)",infoColor:"rgba(0,100,250,1)"}}},ws={name:"semiDesignLight",description:"Semi Design - light",type:"light",fontFamily:Rt,colorScheme:Ns},Ts={backgroundColor:"--semi-color-bg-0",borderColor:"--semi-color-border",hoverBackgroundColor:"--semi-color-fill-0",sliderRailColor:"--semi-color-fill-0",sliderHandleColor:"--semi-white",sliderTrackColor:"--semi-color-primary",popupBackgroundColor:"--semi-color-bg-3",primaryFontColor:"--semi-color-text-0",secondaryFontColor:"--semi-color-text-1",tertiaryFontColor:"--semi-color-text-2",axisLabelFontColor:"--semi-color-text-0",disableFontColor:"--semi-color-disabled-text",axisMarkerFontColor:"--semi-color-bg-0",axisGridColor:"--semi-color-border",axisDomainColor:{light:"--semi-grey-9",dark:"--semi-color-border"},dataZoomHandleStrokeColor:{light:"--semi-color-fill-2"},dataZoomChartColor:"--semi-color-fill-1",playerControllerColor:"--semi-color-primary",axisMarkerBackgroundColor:"--semi-color-text-0",markLabelBackgroundColor:"--semi-color-border",markLineStrokeColor:"--semi-color-text-1",dangerColor:"--semi-color-danger",warningColor:"--semi-color-warning",successColor:"--semi-color-success",infoColor:"--semi-color-info"},Es=[{maxDomainLength:10,scheme:["--semi-color-data-0","--semi-color-data-2","--semi-color-data-4","--semi-color-data-6","--semi-color-data-8","--semi-color-data-10","--semi-color-data-12","--semi-color-data-14","--semi-color-data-16","--semi-color-data-18"]},{scheme:["--semi-color-data-0","--semi-color-data-1","--semi-color-data-2","--semi-color-data-3","--semi-color-data-4","--semi-color-data-5","--semi-color-data-6","--semi-color-data-7","--semi-color-data-8","--semi-color-data-9","--semi-color-data-10","--semi-color-data-11","--semi-color-data-12","--semi-color-data-13","--semi-color-data-14","--semi-color-data-15","--semi-color-data-16","--semi-color-data-17","--semi-color-data-18","--semi-color-data-19"]}],Ms={light:ws,dark:Ds},Ue=(c,i)=>{const m=Ms[c],{dataScheme:n,palette:h}=m.colorScheme.default,o={default:{dataScheme:ys(c,Es,n),palette:vs(c,Ts,h)}};return Object.assign(Object.assign({},m),{colorScheme:o})},Ss=c=>{const{defaultMode:i,isWatchingMode:m=!0,isWatchingThemeSwitch:n=!1}=c??{};Pe(!1,i),m&&Cs(document.body,He,()=>Pe()),n&&ks(()=>{const h=Lt(),o=JSON.stringify(Ue(h).colorScheme);let f=0;const w=setInterval(()=>{const M=Ue(h);(f>50||o!==JSON.stringify(M.colorScheme))&&(Pe(!0,h,M),clearInterval(w)),f++},100)})},Pe=(c,i,m)=>{i||(i=Lt());const n=js(i);(c||J.ThemeManager.getCurrentTheme()!==n)&&(c&&J.ThemeManager.removeTheme(n),J.ThemeManager.themeExist(n)||J.ThemeManager.registerTheme(n,m??Ue(i)),J.ThemeManager.setCurrentTheme(n))},Qs=c=>{var gt,xt,bt,ft,Ct,yt,vt,jt,kt,Ft,Dt;const[i,m]=l.useContext(Ya),[n,h]=l.useContext(Ja),{t:o}=us(),f=Ga(),w=Xa(),M=l.useRef(),Qe=l.useRef(!1),fe=l.useRef(null),U={mode:"desktop-browser"},S={shadows:"always",bordered:!1,headerLine:!0},Pt={className:"w-full mb-2 !rounded-lg",size:"large"},ze="text-white hover:bg-opacity-80 !rounded-full",se="flex items-center gap-2",C={width:96,height:96};let Oe=new Date;const Ce=es(),Ht=((gt=n==null?void 0:n.status)==null?void 0:gt.api_info_enabled)??!0,Ve=((xt=n==null?void 0:n.status)==null?void 0:xt.announcements_enabled)??!0,We=((bt=n==null?void 0:n.status)==null?void 0:bt.faq_enabled)??!0,Ke=((ft=n==null?void 0:n.status)==null?void 0:ft.uptime_kuma_enabled)??!0,ye=Ht,Ut=Ve||We||Ke,ve=l.useCallback(()=>localStorage.getItem("data_export_default_time")||"hour",[]),oe=l.useCallback((e,a=!1)=>{const s={hour:a?3600:60,day:a?86400:1440,week:a?604800:10080};return s[e]||s.hour},[]),Qt=l.useCallback(()=>{const e=ve(),a=new Date().getTime()/1e3;switch(e){case"hour":return ge(a-86400);case"week":return ge(a-86400*30);default:return ge(a-86400*7)}},[ve]),_=l.useCallback((e,a,s)=>{e.has(a)||e.set(a,0),e.set(a,e.get(a)+s)},[]),Ge=l.useCallback((e,...a)=>{a.forEach(s=>{s.has(e)||s.set(e,0)})},[]),Q=l.useCallback((e,a,s,r,u)=>{e(g=>({...g,data:[{id:u,values:a}],title:{...g.title,subtext:s},color:{specified:r}}))},[]),z=l.useCallback((e,a)=>t.jsxs("div",{className:se,children:[t.jsx(e,{size:16}),a]}),[]),le=l.useCallback((e,a)=>t.jsx(e,{...Pt,...a}),[]),zt=l.useMemo(()=>[{label:o("小时"),value:"hour"},{label:o("天"),value:"day"},{label:o("周"),value:"week"}],[o]),[Ot,Vt]=l.useState({username:"",token_name:"",model_name:"",start_timestamp:Qt(),end_timestamp:ge(Oe.getTime()/1e3+3600),channel:"",data_export_default_time:""}),[x,Wt]=l.useState(ve()),[je,Ze]=l.useState(!1),[Kt,Gt]=l.useState(!1),[_s,Zt]=l.useState([]),[ke,Yt]=l.useState(0),[B,Jt]=l.useState(0),[A,Xt]=l.useState(0),[ea,ta]=l.useState([{type:"null",value:"0"}]),[aa,sa]=l.useState([]),[Ye,oa]=l.useState({}),[O,la]=l.useState("1"),[ra,na]=l.useState(!1),[ia,Fe]=l.useState(!1),[I,ca]=l.useState({balance:[],usedQuota:[],requestCount:[],times:[],consumeQuota:[],tokens:[],rpm:[],tpm:[]}),De=l.useRef(null),Ne=l.useRef(null),we=l.useRef(null),re=l.useRef({}),[ua,Je]=l.useState(!1),[da,Xe]=l.useState(!1),[et,ne]=l.useState(!1),[F,ma]=l.useState([]),[tt,at]=l.useState(!1),[D,st]=l.useState(""),{username:Te,model_name:Bs,start_timestamp:$,end_timestamp:R,channel:As}=Ot,[ha,pa]=l.useState({type:"pie",data:[{id:"id0",values:ea}],outerRadius:.8,innerRadius:.5,padAngle:.6,valueField:"value",categoryField:"type",pie:{style:{cornerRadius:10},state:{hover:{outerRadius:.85,stroke:"#000",lineWidth:1},selected:{outerRadius:.85,stroke:"#000",lineWidth:1}}},title:{visible:!0,text:o("模型调用次数占比"),subtext:`${o("总计")}：${E(A)}`},legends:{visible:!0,orient:"left"},label:{visible:!0},tooltip:{mark:{content:[{key:e=>e.type,value:e=>E(e.value)}]}},color:{specified:ae}}),[ga,xa]=l.useState({type:"bar",data:[{id:"barData",values:aa}],xField:"Time",yField:"Usage",seriesField:"Model",stack:!0,legends:{visible:!0,selectMode:"single"},title:{visible:!0,text:o("模型消耗分布"),subtext:`${o("总计")}：${N(ke,2)}`},bar:{state:{hover:{stroke:"#000",lineWidth:1}}},tooltip:{mark:{content:[{key:e=>e.Model,value:e=>N(e.rawQuota||0,4)}]},dimension:{content:[{key:e=>e.Model,value:e=>e.rawQuota||0}],updateContent:e=>{e.sort((s,r)=>r.value-s.value);let a=0;for(let s=0;s<e.length;s++){if(e[s].key=="其他")continue;let r=parseFloat(e[s].value);isNaN(r)&&(r=0),e[s].datum&&e[s].datum.TimeSum&&(a=e[s].datum.TimeSum),e[s].value=N(r,4)}return e.unshift({key:o("总计"),value:N(a,4)}),e}}},color:{specified:ae}}),[ba,fa]=l.useState({type:"line",data:[{id:"lineData",values:[]}],xField:"Time",yField:"Count",seriesField:"Model",legends:{visible:!0,selectMode:"single"},title:{visible:!0,text:o("模型消耗趋势"),subtext:""},tooltip:{mark:{content:[{key:e=>e.Model,value:e=>E(e.Count)}]}},color:{specified:ae}}),[Ca,ya]=l.useState({type:"bar",data:[{id:"rankData",values:[]}],xField:"Model",yField:"Count",seriesField:"Model",legends:{visible:!0,selectMode:"single"},title:{visible:!0,text:o("模型调用次数排行"),subtext:""},bar:{state:{hover:{stroke:"#000",lineWidth:1}}},tooltip:{mark:{content:[{key:e=>e.Model,value:e=>E(e.Count)}]}},color:{specified:ae}}),Ee=l.useMemo(()=>{const e=(Date.parse(R)-Date.parse($))/6e4,a=isNaN(A/e)?"0":(A/e).toFixed(3),s=isNaN(B/e)?"0":(B/e).toFixed(3);return{avgRPM:a,avgTPM:s,timeDiff:e}},[A,B,R,$]),va=l.useMemo(()=>{var r;const e=new Date().getHours();let a="";e>=5&&e<12?a=o("早上好"):e>=12&&e<14?a=o("中午好"):e>=14&&e<18?a=o("下午好"):a=o("晚上好");const s=((r=i==null?void 0:i.user)==null?void 0:r.username)||"";return`👋${a}，${s}`},[o,(Ct=i==null?void 0:i.user)==null?void 0:Ct.username]),ja=l.useCallback((e,a)=>({type:"line",data:[{id:"trend",values:e.map((s,r)=>({x:r,y:s}))}],xField:"x",yField:"y",height:40,width:100,axes:[{orient:"bottom",visible:!1},{orient:"left",visible:!1}],padding:0,autoFit:!1,legends:{visible:!1},tooltip:{visible:!1},crosshair:{visible:!1},line:{style:{stroke:a,lineWidth:2}},point:{visible:!1},background:{fill:"transparent"}}),[]),ka=l.useMemo(()=>{var e,a,s;return[{title:z(fs,o("账户数据")),color:"bg-blue-50",items:[{title:o("当前余额"),value:N((e=i==null?void 0:i.user)==null?void 0:e.quota),icon:t.jsx($a,{}),avatarColor:"blue",onClick:()=>f("/console/topup"),trendData:[],trendColor:"#3b82f6"},{title:o("历史消耗"),value:N((a=i==null?void 0:i.user)==null?void 0:a.used_quota),icon:t.jsx(Ae,{}),avatarColor:"purple",trendData:[],trendColor:"#8b5cf6"}]},{title:z(ts,o("使用统计")),color:"bg-green-50",items:[{title:o("请求次数"),value:(s=i.user)==null?void 0:s.request_count,icon:t.jsx(Ra,{}),avatarColor:"green",trendData:[],trendColor:"#10b981"},{title:o("统计次数"),value:A,icon:t.jsx(Tt,{}),avatarColor:"cyan",trendData:I.times,trendColor:"#06b6d4"}]},{title:z(as,o("资源消耗")),color:"bg-yellow-50",items:[{title:o("统计额度"),value:N(ke),icon:t.jsx(qa,{}),avatarColor:"yellow",trendData:I.consumeQuota,trendColor:"#f59e0b"},{title:o("统计Tokens"),value:isNaN(B)?0:B,icon:t.jsx(La,{}),avatarColor:"pink",trendData:I.tokens,trendColor:"#ec4899"}]},{title:z(xe,o("性能指标")),color:"bg-indigo-50",items:[{title:o("平均RPM"),value:Ee.avgRPM,icon:t.jsx(Pa,{}),avatarColor:"indigo",trendData:I.rpm,trendColor:"#6366f1"},{title:o("平均TPM"),value:Ee.avgTPM,icon:t.jsx(Ha,{}),avatarColor:"orange",trendData:I.tpm,trendColor:"#f97316"}]}]},[z,o,(yt=i==null?void 0:i.user)==null?void 0:yt.quota,(vt=i==null?void 0:i.user)==null?void 0:vt.used_quota,(jt=i==null?void 0:i.user)==null?void 0:jt.request_count,A,ke,B,I,Ee,f]),Fa=l.useCallback(async e=>{await ss(e)&&os(o("复制成功"))},[o]),Da=l.useCallback(e=>{const s=`https://www.tcptest.cn/http/${encodeURIComponent(e)}`;window.open(s,"_blank","noopener,noreferrer")},[]),ie=l.useCallback((e,a)=>{if(a==="data_export_default_time"){Wt(e);return}Vt(s=>({...s,[a]:e}))},[]),ce=l.useCallback(async()=>{Ze(!0);const e=Date.now();try{let a="",s=Date.parse($)/1e3,r=Date.parse(R)/1e3;Ce?a=`/api/data/?username=${Te}&start_timestamp=${s}&end_timestamp=${r}&default_time=${x}`:a=`/api/data/self/?start_timestamp=${s}&end_timestamp=${r}&default_time=${x}`;const u=await Re.get(a),{success:g,message:y,data:v}=u.data;g?(Zt(v),v.length===0&&v.push({count:0,model_name:"无数据",quota:0,created_at:Oe.getTime()/1e3}),v.sort((L,b)=>L.created_at-b.created_at),_a(v)):qe(y)}finally{const a=Date.now()-e,s=Math.max(0,500-a);setTimeout(()=>{Ze(!1)},s)}},[$,R,Te,x,Ce]),V=l.useCallback(async()=>{at(!0);try{const e=await Re.get("/api/uptime/status"),{success:a,message:s,data:r}=e.data;a?(ma(r||[]),r&&r.length>0&&!D&&st(r[0].categoryName)):qe(s)}catch(e){console.error(e)}finally{at(!1)}},[D]),Me=l.useCallback(async()=>{await Promise.all([ce(),V()])},[ce,V]),Na=l.useCallback(()=>{Me(),Fe(!1)},[Me]),wa=l.useCallback(async()=>{await ce(),await V()},[ce,V]),Ta=l.useCallback(()=>{Fe(!0)},[]),Ea=l.useCallback(()=>{Fe(!1)},[]),ot=()=>{if(fe.current){const e=fe.current,a=e.scrollHeight>e.clientHeight,s=e.scrollTop+e.clientHeight>=e.scrollHeight-5;na(a&&!s)}},Ma=()=>{ot()},W=(e,a)=>{if(e.current){const s=e.current,r=s.scrollHeight>s.clientHeight,u=s.scrollTop+s.clientHeight>=s.scrollHeight-5;a(r&&!u)}},ue=(e,a)=>{W(e,a)};l.useEffect(()=>{const e=setTimeout(()=>{if(ot(),W(De,Je),W(Ne,Xe),F.length===1)W(we,ne);else if(F.length>1&&D){const a=re.current[D];a&&W(a,ne)}},100);return()=>clearTimeout(e)},[F,D]),l.useEffect(()=>{const e=setTimeout(()=>{Gt(!0)},100);return()=>clearTimeout(e)},[]);const Sa=async()=>{let e=await Re.get("/api/user/self");const{success:a,message:s,data:r}=e.data;a?m({type:"login",payload:r}):qe(s)},lt=l.useCallback(e=>{const a={totalQuota:0,totalTimes:0,totalTokens:0,uniqueModels:new Set,timePoints:[],timeQuotaMap:new Map,timeTokensMap:new Map,timeCountMap:new Map};return e.forEach(s=>{a.uniqueModels.add(s.model_name),a.totalTokens+=s.token_used,a.totalQuota+=s.quota,a.totalTimes+=s.count;const r=Le(s.created_at,x);a.timePoints.includes(r)||a.timePoints.push(r),Ge(r,a.timeQuotaMap,a.timeTokensMap,a.timeCountMap),_(a.timeQuotaMap,r,s.quota),_(a.timeTokensMap,r,s.token_used),_(a.timeCountMap,r,s.count)}),a.timePoints.sort(),a},[x,Ge,_]),rt=l.useCallback((e,a,s,r)=>{const u=e.map(b=>a.get(b)||0),g=e.map(b=>s.get(b)||0),y=e.map(b=>r.get(b)||0),v=[],L=[];if(e.length>=2){const b=oe(x);for(let P=0;P<e.length;P++)v.push(r.get(e[P])/b),L.push(s.get(e[P])/b)}return{balance:[],usedQuota:[],requestCount:[],times:y,consumeQuota:u,tokens:g,rpm:v,tpm:L}},[x,oe]),nt=l.useCallback(e=>{const a={};return Array.from(e).forEach(s=>{a[s]=ae[s]||Ye[s]||ls(s)}),a},[Ye]),it=l.useCallback(e=>{const a=new Map;return e.forEach(s=>{const r=Le(s.created_at,x),u=s.model_name,g=`${r}-${u}`;a.has(g)||a.set(g,{time:r,model:u,quota:0,count:0});const y=a.get(g);y.quota+=s.quota,y.count+=s.count}),a},[x]),ct=l.useCallback((e,a)=>{let s=Array.from(new Set([...e.values()].map(r=>r.time)));if(s.length<7){const r=Math.max(...a.map(g=>g.created_at)),u=oe(x,!0);s=Array.from({length:7},(g,y)=>Le(r-(6-y)*u,x))}return s},[x,oe]),_a=l.useCallback(e=>{const a=lt(e),{totalQuota:s,totalTimes:r,totalTokens:u,uniqueModels:g,timePoints:y,timeQuotaMap:v,timeTokensMap:L,timeCountMap:b}=a,P=rt(y,v,L,b);ca(P);const K=nt(g);oa(K);const de=it(e),_e=new Map;for(let[p,d]of de)_(_e,d.model,d.count);const Nt=Array.from(_e).map(([p,d])=>({type:p,value:d})).sort((p,d)=>d.value-p.value),wt=ct(de,e);let me=[];wt.forEach(p=>{let d=Array.from(g).map(j=>{const k=`${p}-${j}`,T=de.get(k);return{Time:p,Model:j,rawQuota:(T==null?void 0:T.quota)||0,Usage:T!=null&&T.quota?rs(T.quota,4):0}});const he=d.reduce((j,k)=>j+k.rawQuota,0);d.sort((j,k)=>k.rawQuota-j.rawQuota),d=d.map(j=>({...j,TimeSum:he})),me.push(...d)}),me.sort((p,d)=>p.Time.localeCompare(d.Time)),Q(pa,Nt,`${o("总计")}：${E(r)}`,K,"id0"),Q(xa,me,`${o("总计")}：${N(s,2)}`,K,"barData");let Be=[];wt.forEach(p=>{const d=Array.from(g).map(he=>{const j=`${p}-${he}`,k=de.get(j);return{Time:p,Model:he,Count:(k==null?void 0:k.count)||0}});Be.push(...d)}),Be.sort((p,d)=>p.Time.localeCompare(d.Time));const Ia=Array.from(_e).map(([p,d])=>({Model:p,Count:d})).sort((p,d)=>d.Count-p.Count);Q(fa,Be,`${o("总计")}：${E(r)}`,K,"lineData"),Q(ya,Ia,`${o("总计")}：${E(r)}`,K,"rankData"),ta(Nt),sa(me),Yt(s),Xt(r),Jt(u)},[lt,rt,nt,it,ct,Q,_,o]),Ba=l.useMemo(()=>[{color:"grey",label:o("默认"),type:"default"},{color:"blue",label:o("进行中"),type:"ongoing"},{color:"green",label:o("成功"),type:"success"},{color:"orange",label:o("警告"),type:"warning"},{color:"red",label:o("异常"),type:"error"}],[o]),q=l.useMemo(()=>({1:{color:"#10b981",label:o("正常"),text:o("可用率")},0:{color:"#ef4444",label:o("异常"),text:o("有异常")},2:{color:"#f59e0b",label:o("高延迟"),text:o("高延迟")},3:{color:"#3b82f6",label:o("维护中"),text:o("维护中")}}),[o]),Aa=l.useMemo(()=>Object.entries(q).map(([e,a])=>({status:Number(e),color:a.color,label:a.label})),[q]),Se=l.useCallback(e=>{var a;return((a=q[e])==null?void 0:a.color)||"#8b9aa7"},[q]),ut=l.useCallback(e=>{var a;return((a=q[e])==null?void 0:a.text)||o("未知")},[q,o]),dt=l.useMemo(()=>{var e;return((e=n==null?void 0:n.status)==null?void 0:e.api_info)||[]},[(kt=n==null?void 0:n.status)==null?void 0:kt.api_info]),mt=l.useMemo(()=>{var a;return(((a=n==null?void 0:n.status)==null?void 0:a.announcements)||[]).map(s=>({...s,time:ns(s.publishDate)}))},[(Ft=n==null?void 0:n.status)==null?void 0:Ft.announcements]),ht=l.useMemo(()=>{var e;return((e=n==null?void 0:n.status)==null?void 0:e.faq)||[]},[(Dt=n==null?void 0:n.status)==null?void 0:Dt.faq]),pt=l.useCallback(e=>{if(!e||e.length===0)return t.jsx("div",{className:"flex justify-center items-center py-4",children:t.jsx(G,{image:t.jsx(te,{style:C}),darkModeImage:t.jsx(ee,{style:C}),title:o("暂无监控数据")})});const a={};e.forEach(r=>{const u=r.group||"";a[u]||(a[u]=[]),a[u].push(r)});const s=(r,u)=>t.jsxs("div",{className:"p-2 hover:bg-white rounded-lg transition-colors",children:[t.jsxs("div",{className:"flex items-center justify-between mb-1",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"w-2 h-2 rounded-full flex-shrink-0",style:{backgroundColor:Se(r.status)}}),t.jsx("span",{className:"text-sm font-medium text-gray-900",children:r.name})]}),t.jsxs("span",{className:"text-xs text-gray-500",children:[((r.uptime||0)*100).toFixed(2),"%"]})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("span",{className:"text-xs text-gray-500",children:ut(r.status)}),t.jsx("div",{className:"flex-1",children:t.jsx(Ka,{percent:(r.uptime||0)*100,showInfo:!1,"aria-label":`${r.name} uptime`,stroke:Se(r.status)})})]})]},u);return Object.entries(a).map(([r,u])=>t.jsxs("div",{className:"mb-2",children:[r&&t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"text-md font-semibold text-gray-500 px-2 py-1",children:r}),t.jsx(Et,{})]}),u.map(s)]},r||"default"))},[o,Se,ut]);return l.useEffect(()=>{Sa(),Qe.current||(Ss({isWatchingThemeSwitch:!0}),Qe.current=!0,wa())},[]),t.jsxs("div",{className:"bg-gray-50 h-full mt-[64px] px-2",children:[t.jsxs("div",{className:"flex items-center justify-between mb-4",children:[t.jsx("h2",{className:"text-2xl font-semibold text-gray-800 transition-opacity duration-1000 ease-in-out",style:{opacity:Kt?1:0},children:va}),t.jsxs("div",{className:"flex gap-3",children:[t.jsx(Ie,{type:"tertiary",icon:t.jsx(Ua,{}),onClick:Ta,className:`bg-green-500 hover:bg-green-600 ${ze}`}),t.jsx(Ie,{type:"tertiary",icon:t.jsx(Mt,{}),onClick:Me,loading:je,className:`bg-blue-500 hover:bg-blue-600 ${ze}`})]})]}),t.jsx(Qa,{title:o("搜索条件"),visible:ia,onOk:Na,onCancel:Ea,closeOnEsc:!0,size:w?"full-width":"small",centered:!0,children:t.jsxs(Z,{ref:M,layout:"vertical",className:"w-full",children:[le(Z.DatePicker,{field:"start_timestamp",label:o("起始时间"),initValue:$,value:$,type:"dateTime",name:"start_timestamp",onChange:e=>ie(e,"start_timestamp")}),le(Z.DatePicker,{field:"end_timestamp",label:o("结束时间"),initValue:R,value:R,type:"dateTime",name:"end_timestamp",onChange:e=>ie(e,"end_timestamp")}),le(Z.Select,{field:"data_export_default_time",label:o("时间粒度"),initValue:x,placeholder:o("时间粒度"),name:"data_export_default_time",optionList:zt,onChange:e=>ie(e,"data_export_default_time")}),Ce&&le(Z.Input,{field:"username",label:o("用户名称"),value:Te,placeholder:o("可选值"),name:"username",onChange:e=>ie(e,"username")})]})}),t.jsx("div",{className:"mb-4",children:t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:ka.map((e,a)=>t.jsx(H,{...S,className:`${e.color} border-0 !rounded-2xl w-full`,title:e.title,children:t.jsx("div",{className:"space-y-4",children:e.items.map((s,r)=>t.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:s.onClick,children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(St,{className:"mr-3",size:"small",color:s.avatarColor,children:s.icon}),t.jsxs("div",{children:[t.jsx("div",{className:"text-xs text-gray-500",children:s.title}),t.jsx("div",{className:"text-lg font-semibold",children:t.jsx(_t,{loading:je,active:!0,placeholder:t.jsx(_t.Paragraph,{active:!0,rows:1,style:{width:"65px",height:"24px",marginTop:"4px"}}),children:s.value})})]})]}),(je||s.trendData&&s.trendData.length>0)&&t.jsx("div",{className:"w-24 h-10",children:t.jsx(X,{spec:ja(s.trendData,s.trendColor),option:U})})]},r))})},a))})}),t.jsx("div",{className:"mb-4",children:t.jsxs("div",{className:`grid grid-cols-1 gap-4 ${ye?"lg:grid-cols-4":""}`,children:[t.jsx(H,{...S,className:`shadow-sm !rounded-2xl ${ye?"lg:col-span-3":""}`,title:t.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between w-full gap-3",children:[t.jsxs("div",{className:se,children:[t.jsx(ms,{size:16}),o("模型数据分析")]}),t.jsxs(Bt,{type:"button",activeKey:O,onChange:la,children:[t.jsx(Y,{tab:t.jsxs("span",{children:[t.jsx(Ae,{}),o("消耗分布")]}),itemKey:"1"}),t.jsx(Y,{tab:t.jsxs("span",{children:[t.jsx(Tt,{}),o("消耗趋势")]}),itemKey:"2"}),t.jsx(Y,{tab:t.jsxs("span",{children:[t.jsx(za,{}),o("调用次数分布")]}),itemKey:"3"}),t.jsx(Y,{tab:t.jsxs("span",{children:[t.jsx(Ae,{}),o("调用次数排行")]}),itemKey:"4"})]})]}),bodyStyle:{padding:0},children:t.jsxs("div",{className:"h-96 p-2",children:[O==="1"&&t.jsx(X,{spec:ga,option:U}),O==="2"&&t.jsx(X,{spec:ba,option:U}),O==="3"&&t.jsx(X,{spec:ha,option:U}),O==="4"&&t.jsx(X,{spec:Ca,option:U})]})}),ye&&t.jsx(H,{...S,className:"bg-gray-50 border-0 !rounded-2xl",title:t.jsxs("div",{className:se,children:[t.jsx(xs,{size:16}),o("API信息")]}),bodyStyle:{padding:0},children:t.jsxs("div",{className:"card-content-container",children:[t.jsx("div",{ref:fe,className:"p-2 max-h-96 overflow-y-auto card-content-scroll",onScroll:Ma,children:dt.length>0?dt.map(e=>t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"flex p-2 hover:bg-white rounded-lg transition-colors cursor-pointer",children:[t.jsx("div",{className:"flex-shrink-0 mr-3",children:t.jsx(St,{size:"extra-small",color:e.color,children:e.route.substring(0,2)})}),t.jsxs("div",{className:"flex-1",children:[t.jsxs("div",{className:"flex flex-wrap items-center justify-between mb-1 w-full gap-2",children:[t.jsx("span",{className:"text-sm font-medium text-gray-900 !font-bold break-all",children:e.route}),t.jsxs("div",{className:"flex items-center gap-1 mt-1 lg:mt-0",children:[t.jsx(pe,{prefixIcon:t.jsx(xe,{size:12}),size:"small",color:"white",shape:"circle",onClick:()=>Da(e.url),className:"cursor-pointer hover:opacity-80 text-xs",children:o("测速")}),t.jsx(pe,{prefixIcon:t.jsx(ps,{size:12}),size:"small",color:"white",shape:"circle",onClick:()=>window.open(e.url,"_blank","noopener,noreferrer"),className:"cursor-pointer hover:opacity-80 text-xs",children:o("跳转")})]})]}),t.jsx("div",{className:"!text-semi-color-primary break-all cursor-pointer hover:underline mb-1",onClick:()=>Fa(e.url),children:e.url}),t.jsx("div",{className:"text-gray-500",children:e.description})]})]},e.id),t.jsx(Et,{})]})):t.jsx("div",{className:"flex justify-center items-center py-8",children:t.jsx(G,{image:t.jsx(te,{style:C}),darkModeImage:t.jsx(ee,{style:C}),title:o("暂无API信息"),description:o("请联系管理员在系统设置中配置API信息")})})}),t.jsx("div",{className:"card-content-fade-indicator",style:{opacity:ra?1:0}})]})})]})}),Ut&&t.jsx("div",{className:"mb-4",children:t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-4",children:[Ve&&t.jsx(H,{...S,className:"shadow-sm !rounded-2xl lg:col-span-2",title:t.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-2 w-full",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(is,{size:16}),o("系统公告"),t.jsx(pe,{color:"white",shape:"circle",children:o("显示最新20条")})]}),t.jsx("div",{className:"flex flex-wrap gap-3 text-xs",children:Ba.map((e,a)=>t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx("div",{className:"w-2 h-2 rounded-full",style:{backgroundColor:e.color==="grey"?"#8b9aa7":e.color==="blue"?"#3b82f6":e.color==="green"?"#10b981":e.color==="orange"?"#f59e0b":e.color==="red"?"#ef4444":"#8b9aa7"}}),t.jsx("span",{className:"text-gray-600",children:e.label})]},a))})]}),bodyStyle:{padding:0},children:t.jsxs("div",{className:"card-content-container",children:[t.jsx("div",{ref:De,className:"p-2 max-h-96 overflow-y-auto card-content-scroll",onScroll:()=>ue(De,Je),children:mt.length>0?t.jsx(At,{mode:"alternate",children:mt.map((e,a)=>t.jsx(At.Item,{type:e.type||"default",time:e.time,children:t.jsxs("div",{children:[t.jsx("div",{dangerouslySetInnerHTML:{__html:$e.parse(e.content||"")}}),e.extra&&t.jsx("div",{className:"text-xs text-gray-500",dangerouslySetInnerHTML:{__html:$e.parse(e.extra)}})]})},a))}):t.jsx("div",{className:"flex justify-center items-center py-8",children:t.jsx(G,{image:t.jsx(te,{style:C}),darkModeImage:t.jsx(ee,{style:C}),title:o("暂无系统公告"),description:o("请联系管理员在系统设置中配置公告信息")})})}),t.jsx("div",{className:"card-content-fade-indicator",style:{opacity:ua?1:0}})]})}),We&&t.jsx(H,{...S,className:"shadow-sm !rounded-2xl lg:col-span-1",title:t.jsxs("div",{className:se,children:[t.jsx(cs,{size:16}),o("常见问答")]}),bodyStyle:{padding:0},children:t.jsxs("div",{className:"card-content-container",children:[t.jsx("div",{ref:Ne,className:"p-2 max-h-96 overflow-y-auto card-content-scroll",onScroll:()=>ue(Ne,Xe),children:ht.length>0?t.jsx(It,{accordion:!0,expandIcon:t.jsx(Va,{}),collapseIcon:t.jsx(Oa,{}),children:ht.map((e,a)=>t.jsx(It.Panel,{header:e.question,itemKey:a.toString(),children:t.jsx("div",{dangerouslySetInnerHTML:{__html:$e.parse(e.answer||"")}})},a))}):t.jsx("div",{className:"flex justify-center items-center py-8",children:t.jsx(G,{image:t.jsx(te,{style:C}),darkModeImage:t.jsx(ee,{style:C}),title:o("暂无常见问答"),description:o("请联系管理员在系统设置中配置常见问答")})})}),t.jsx("div",{className:"card-content-fade-indicator",style:{opacity:da?1:0}})]})}),Ke&&t.jsxs(H,{...S,className:"shadow-sm !rounded-2xl lg:col-span-1",title:t.jsxs("div",{className:"flex items-center justify-between w-full gap-2",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(xe,{size:16}),o("服务可用性")]}),t.jsx(Ie,{icon:t.jsx(Mt,{}),onClick:V,loading:tt,size:"small",theme:"borderless",type:"tertiary",className:"text-gray-500 hover:text-blue-500 hover:bg-blue-50 !rounded-full"})]}),bodyStyle:{padding:0},children:[t.jsx("div",{className:"relative",children:t.jsx(Wa,{spinning:tt,children:F.length>0?F.length===1?t.jsxs("div",{className:"card-content-container",children:[t.jsx("div",{ref:we,className:"p-2 max-h-[24rem] overflow-y-auto card-content-scroll",onScroll:()=>ue(we,ne),children:pt(F[0].monitors)}),t.jsx("div",{className:"card-content-fade-indicator",style:{opacity:et?1:0}})]}):t.jsx(Bt,{type:"card",collapsible:!0,activeKey:D,onChange:st,size:"small",children:F.map((e,a)=>{re.current[e.categoryName]||(re.current[e.categoryName]=Za.createRef());const s=re.current[e.categoryName];return t.jsx(Y,{tab:t.jsxs("span",{className:"flex items-center gap-2",children:[t.jsx(xe,{size:14}),e.categoryName,t.jsx(pe,{color:D===e.categoryName?"red":"grey",size:"small",shape:"circle",children:e.monitors?e.monitors.length:0})]}),itemKey:e.categoryName,children:t.jsxs("div",{className:"card-content-container",children:[t.jsx("div",{ref:s,className:"p-2 max-h-[21.5rem] overflow-y-auto card-content-scroll",onScroll:()=>ue(s,ne),children:pt(e.monitors)}),t.jsx("div",{className:"card-content-fade-indicator",style:{opacity:D===e.categoryName&&et?1:0}})]})},a)})}):t.jsx("div",{className:"flex justify-center items-center py-8",children:t.jsx(G,{image:t.jsx(te,{style:C}),darkModeImage:t.jsx(ee,{style:C}),title:o("暂无监控数据"),description:o("请联系管理员在系统设置中配置Uptime")})})})}),F.length>0&&t.jsx("div",{className:"p-3 bg-gray-50 rounded-b-2xl",children:t.jsx("div",{className:"flex flex-wrap gap-3 text-xs justify-center",children:Aa.map((e,a)=>t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx("div",{className:"w-2 h-2 rounded-full",style:{backgroundColor:e.color}}),t.jsx("span",{className:"text-gray-600",children:e.label})]},a))})})]})]})})]})};export{Qs as default};
