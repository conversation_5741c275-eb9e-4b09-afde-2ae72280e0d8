import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![x](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNOTIxIDkxMkw2MDEuMTEgNDQ1Ljc1bC41NS40M0w4OTAuMDggMTEySDc5My43TDU1OC43NCAzODQgMzcyLjE1IDExMkgxMTkuMzdsMjk4LjY1IDQzNS4zMS0uMDQtLjA0TDEwMyA5MTJoOTYuMzlMNDYwLjYgNjA5LjM4IDY2OC4yIDkxMnpNMzMzLjk2IDE4NC43M2w0NDguODMgNjU0LjU0SDcwNi40TDI1Ny4yIDE4NC43M3oiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
