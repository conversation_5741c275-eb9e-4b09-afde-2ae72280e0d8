var jt=Object.defineProperty;var Ft=(i,e,t)=>e in i?jt(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t;var qe=(i,e,t)=>Ft(i,typeof e!="symbol"?e+"":e,t);import{_ as Be}from"./semi-ui-Csx8wKaA.js";import{g as Mt}from"./react-core-DskXcPn0.js";var V;(function(i){i.Pop="POP",i.Push="PUSH",i.Replace="REPLACE"})(V||(V={}));var Ne=function(i){return i},Ue="beforeunload",Ht="popstate";function Er(i){i===void 0&&(i={});var e=i,t=e.window,n=t===void 0?document.defaultView:t,r=n.history;function s(){var x=n.location,b=x.pathname,$=x.search,q=x.hash,z=r.state||{};return[z.idx,Ne({pathname:b,search:$,hash:q,state:z.usr||null,key:z.key||"default"})]}var a=null;function o(){if(a)h.call(a),a=null;else{var x=V.Pop,b=s(),$=b[0],q=b[1];if(h.length){if($!=null){var z=c-$;z&&(a={action:x,location:q,retry:function(){g(z*-1)}},g(z))}}else v(x)}}n.addEventListener(Ht,o);var l=V.Pop,p=s(),c=p[0],f=p[1],u=Fe(),h=Fe();c==null&&(c=0,r.replaceState(Be({},r.state,{idx:c}),""));function P(x){return typeof x=="string"?x:Jt(x)}function E(x,b){return b===void 0&&(b=null),Ne(Be({pathname:f.pathname,hash:"",search:""},typeof x=="string"?Qt(x):x,{state:b,key:Zt()}))}function S(x,b){return[{usr:x.state,key:x.key,idx:b},P(x)]}function w(x,b,$){return!h.length||(h.call({action:x,location:b,retry:$}),!1)}function v(x){l=x;var b=s();c=b[0],f=b[1],u.call({action:l,location:f})}function T(x,b){var $=V.Push,q=E(x,b);function z(){T(x,b)}if(w($,q,z)){var _=S(q,c+1),U=_[0],B=_[1];try{r.pushState(U,"",B)}catch{n.location.assign(B)}v($)}}function C(x,b){var $=V.Replace,q=E(x,b);function z(){C(x,b)}if(w($,q,z)){var _=S(q,c),U=_[0],B=_[1];r.replaceState(U,"",B),v($)}}function g(x){r.go(x)}var D={get action(){return l},get location(){return f},createHref:P,push:T,replace:C,go:g,back:function(){g(-1)},forward:function(){g(1)},listen:function(b){return u.push(b)},block:function(b){var $=h.push(b);return h.length===1&&n.addEventListener(Ue,je),function(){$(),h.length||n.removeEventListener(Ue,je)}}};return D}function je(i){i.preventDefault(),i.returnValue=""}function Fe(){var i=[];return{get length(){return i.length},push:function(t){return i.push(t),function(){i=i.filter(function(n){return n!==t})}},call:function(t){i.forEach(function(n){return n&&n(t)})}}}function Zt(){return Math.random().toString(36).substr(2,8)}function Jt(i){var e=i.pathname,t=e===void 0?"/":e,n=i.search,r=n===void 0?"":n,s=i.hash,a=s===void 0?"":s;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),a&&a!=="#"&&(t+=a.charAt(0)==="#"?a:"#"+a),t}function Qt(i){var e={};if(i){var t=i.indexOf("#");t>=0&&(e.hash=i.substr(t),i=i.substr(0,t));var n=i.indexOf("?");n>=0&&(e.search=i.substr(n),i=i.substr(0,n)),i&&(e.pathname=i)}return e}var Se={exports:{}},xt=function(e,t){return function(){for(var r=new Array(arguments.length),s=0;s<r.length;s++)r[s]=arguments[s];return e.apply(t,r)}},Vt=xt,$e=Object.prototype.toString,Ae=function(i){return function(e){var t=$e.call(e);return i[t]||(i[t]=t.slice(8,-1).toLowerCase())}}(Object.create(null));function H(i){return i=i.toLowerCase(),function(t){return Ae(t)===i}}function Te(i){return Array.isArray(i)}function ne(i){return typeof i>"u"}function Wt(i){return i!==null&&!ne(i)&&i.constructor!==null&&!ne(i.constructor)&&typeof i.constructor.isBuffer=="function"&&i.constructor.isBuffer(i)}var wt=H("ArrayBuffer");function Xt(i){var e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(i):e=i&&i.buffer&&wt(i.buffer),e}function Kt(i){return typeof i=="string"}function Gt(i){return typeof i=="number"}function bt(i){return i!==null&&typeof i=="object"}function Y(i){if(Ae(i)!=="object")return!1;var e=Object.getPrototypeOf(i);return e===null||e===Object.prototype}var Yt=H("Date"),en=H("File"),tn=H("Blob"),nn=H("FileList");function Oe(i){return $e.call(i)==="[object Function]"}function rn(i){return bt(i)&&Oe(i.pipe)}function sn(i){var e="[object FormData]";return i&&(typeof FormData=="function"&&i instanceof FormData||$e.call(i)===e||Oe(i.toString)&&i.toString()===e)}var an=H("URLSearchParams");function on(i){return i.trim?i.trim():i.replace(/^\s+|\s+$/g,"")}function ln(){return typeof navigator<"u"&&(navigator.product==="ReactNative"||navigator.product==="NativeScript"||navigator.product==="NS")?!1:typeof window<"u"&&typeof document<"u"}function Ce(i,e){if(!(i===null||typeof i>"u"))if(typeof i!="object"&&(i=[i]),Te(i))for(var t=0,n=i.length;t<n;t++)e.call(null,i[t],t,i);else for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&e.call(null,i[r],r,i)}function Re(){var i={};function e(r,s){Y(i[s])&&Y(r)?i[s]=Re(i[s],r):Y(r)?i[s]=Re({},r):Te(r)?i[s]=r.slice():i[s]=r}for(var t=0,n=arguments.length;t<n;t++)Ce(arguments[t],e);return i}function un(i,e,t){return Ce(e,function(r,s){t&&typeof r=="function"?i[s]=Vt(r,t):i[s]=r}),i}function cn(i){return i.charCodeAt(0)===65279&&(i=i.slice(1)),i}function hn(i,e,t,n){i.prototype=Object.create(e.prototype,n),i.prototype.constructor=i,t&&Object.assign(i.prototype,t)}function fn(i,e,t){var n,r,s,a={};e=e||{};do{for(n=Object.getOwnPropertyNames(i),r=n.length;r-- >0;)s=n[r],a[s]||(e[s]=i[s],a[s]=!0);i=Object.getPrototypeOf(i)}while(i&&(!t||t(i,e))&&i!==Object.prototype);return e}function pn(i,e,t){i=String(i),(t===void 0||t>i.length)&&(t=i.length),t-=e.length;var n=i.indexOf(e,t);return n!==-1&&n===t}function dn(i){if(!i)return null;var e=i.length;if(ne(e))return null;for(var t=new Array(e);e-- >0;)t[e]=i[e];return t}var gn=function(i){return function(e){return i&&e instanceof i}}(typeof Uint8Array<"u"&&Object.getPrototypeOf(Uint8Array)),A={isArray:Te,isArrayBuffer:wt,isBuffer:Wt,isFormData:sn,isArrayBufferView:Xt,isString:Kt,isNumber:Gt,isObject:bt,isPlainObject:Y,isUndefined:ne,isDate:Yt,isFile:en,isBlob:tn,isFunction:Oe,isStream:rn,isURLSearchParams:an,isStandardBrowserEnv:ln,forEach:Ce,merge:Re,extend:un,trim:on,stripBOM:cn,inherits:hn,toFlatObject:fn,kindOf:Ae,kindOfTest:H,endsWith:pn,toArray:dn,isTypedArray:gn,isFileList:nn},J=A;function Me(i){return encodeURIComponent(i).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var vt=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(J.isURLSearchParams(t))r=t.toString();else{var s=[];J.forEach(t,function(l,p){l===null||typeof l>"u"||(J.isArray(l)?p=p+"[]":l=[l],J.forEach(l,function(f){J.isDate(f)?f=f.toISOString():J.isObject(f)&&(f=JSON.stringify(f)),s.push(Me(p)+"="+Me(f))}))}),r=s.join("&")}if(r){var a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+r}return e},mn=A;function se(){this.handlers=[]}se.prototype.use=function(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1};se.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)};se.prototype.forEach=function(e){mn.forEach(this.handlers,function(n){n!==null&&e(n)})};var kn=se,xn=A,wn=function(e,t){xn.forEach(e,function(r,s){s!==t&&s.toUpperCase()===t.toUpperCase()&&(e[t]=r,delete e[s])})},yt=A;function W(i,e,t,n,r){Error.call(this),this.message=i,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),n&&(this.request=n),r&&(this.response=r)}yt.inherits(W,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var Et=W.prototype,_t={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED"].forEach(function(i){_t[i]={value:i}});Object.defineProperties(W,_t);Object.defineProperty(Et,"isAxiosError",{value:!0});W.from=function(i,e,t,n,r,s){var a=Object.create(Et);return yt.toFlatObject(i,a,function(l){return l!==Error.prototype}),W.call(a,i.message,e,t,n,r),a.name=i.name,s&&Object.assign(a,s),a};var K=W,Rt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},N=A;function bn(i,e){e=e||new FormData;var t=[];function n(s){return s===null?"":N.isDate(s)?s.toISOString():N.isArrayBuffer(s)||N.isTypedArray(s)?typeof Blob=="function"?new Blob([s]):Buffer.from(s):s}function r(s,a){if(N.isPlainObject(s)||N.isArray(s)){if(t.indexOf(s)!==-1)throw Error("Circular reference detected in "+a);t.push(s),N.forEach(s,function(l,p){if(!N.isUndefined(l)){var c=a?a+"."+p:p,f;if(l&&!a&&typeof l=="object"){if(N.endsWith(p,"{}"))l=JSON.stringify(l);else if(N.endsWith(p,"[]")&&(f=N.toArray(l))){f.forEach(function(u){!N.isUndefined(u)&&e.append(c,n(u))});return}}r(l,c)}}),t.pop()}else e.append(a,n(s))}return r(i),e}var St=bn,ce,He;function vn(){if(He)return ce;He=1;var i=K;return ce=function(t,n,r){var s=r.config.validateStatus;!r.status||!s||s(r.status)?t(r):n(new i("Request failed with status code "+r.status,[i.ERR_BAD_REQUEST,i.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))},ce}var he,Ze;function yn(){if(Ze)return he;Ze=1;var i=A;return he=i.isStandardBrowserEnv()?function(){return{write:function(n,r,s,a,o,l){var p=[];p.push(n+"="+encodeURIComponent(r)),i.isNumber(s)&&p.push("expires="+new Date(s).toGMTString()),i.isString(a)&&p.push("path="+a),i.isString(o)&&p.push("domain="+o),l===!0&&p.push("secure"),document.cookie=p.join("; ")},read:function(n){var r=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove:function(n){this.write(n,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}(),he}var En=function(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)},_n=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e},Rn=En,Sn=_n,$t=function(e,t){return e&&!Rn(t)?Sn(e,t):t},fe,Je;function $n(){if(Je)return fe;Je=1;var i=A,e=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];return fe=function(n){var r={},s,a,o;return n&&i.forEach(n.split(`
`),function(p){if(o=p.indexOf(":"),s=i.trim(p.substr(0,o)).toLowerCase(),a=i.trim(p.substr(o+1)),s){if(r[s]&&e.indexOf(s)>=0)return;s==="set-cookie"?r[s]=(r[s]?r[s]:[]).concat([a]):r[s]=r[s]?r[s]+", "+a:a}}),r},fe}var pe,Qe;function An(){if(Qe)return pe;Qe=1;var i=A;return pe=i.isStandardBrowserEnv()?function(){var t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a"),r;function s(a){var o=a;return t&&(n.setAttribute("href",o),o=n.href),n.setAttribute("href",o),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:n.pathname.charAt(0)==="/"?n.pathname:"/"+n.pathname}}return r=s(window.location.href),function(o){var l=i.isString(o)?s(o):o;return l.protocol===r.protocol&&l.host===r.host}}():function(){return function(){return!0}}(),pe}var de,Ve;function ae(){if(Ve)return de;Ve=1;var i=K,e=A;function t(n){i.call(this,n??"canceled",i.ERR_CANCELED),this.name="CanceledError"}return e.inherits(t,i,{__CANCEL__:!0}),de=t,de}var ge,We;function Tn(){return We||(We=1,ge=function(e){var t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}),ge}var me,Xe;function Ke(){if(Xe)return me;Xe=1;var i=A,e=vn(),t=yn(),n=vt,r=$t,s=$n(),a=An(),o=Rt,l=K,p=ae(),c=Tn();return me=function(u){return new Promise(function(P,E){var S=u.data,w=u.headers,v=u.responseType,T;function C(){u.cancelToken&&u.cancelToken.unsubscribe(T),u.signal&&u.signal.removeEventListener("abort",T)}i.isFormData(S)&&i.isStandardBrowserEnv()&&delete w["Content-Type"];var g=new XMLHttpRequest;if(u.auth){var D=u.auth.username||"",x=u.auth.password?unescape(encodeURIComponent(u.auth.password)):"";w.Authorization="Basic "+btoa(D+":"+x)}var b=r(u.baseURL,u.url);g.open(u.method.toUpperCase(),n(b,u.params,u.paramsSerializer),!0),g.timeout=u.timeout;function $(){if(g){var _="getAllResponseHeaders"in g?s(g.getAllResponseHeaders()):null,U=!v||v==="text"||v==="json"?g.responseText:g.response,B={data:U,status:g.status,statusText:g.statusText,headers:_,config:u,request:g};e(function(ue){P(ue),C()},function(ue){E(ue),C()},B),g=null}}if("onloadend"in g?g.onloadend=$:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout($)},g.onabort=function(){g&&(E(new l("Request aborted",l.ECONNABORTED,u,g)),g=null)},g.onerror=function(){E(new l("Network Error",l.ERR_NETWORK,u,g,g)),g=null},g.ontimeout=function(){var U=u.timeout?"timeout of "+u.timeout+"ms exceeded":"timeout exceeded",B=u.transitional||o;u.timeoutErrorMessage&&(U=u.timeoutErrorMessage),E(new l(U,B.clarifyTimeoutError?l.ETIMEDOUT:l.ECONNABORTED,u,g)),g=null},i.isStandardBrowserEnv()){var q=(u.withCredentials||a(b))&&u.xsrfCookieName?t.read(u.xsrfCookieName):void 0;q&&(w[u.xsrfHeaderName]=q)}"setRequestHeader"in g&&i.forEach(w,function(U,B){typeof S>"u"&&B.toLowerCase()==="content-type"?delete w[B]:g.setRequestHeader(B,U)}),i.isUndefined(u.withCredentials)||(g.withCredentials=!!u.withCredentials),v&&v!=="json"&&(g.responseType=u.responseType),typeof u.onDownloadProgress=="function"&&g.addEventListener("progress",u.onDownloadProgress),typeof u.onUploadProgress=="function"&&g.upload&&g.upload.addEventListener("progress",u.onUploadProgress),(u.cancelToken||u.signal)&&(T=function(_){g&&(E(!_||_&&_.type?new p:_),g.abort(),g=null)},u.cancelToken&&u.cancelToken.subscribe(T),u.signal&&(u.signal.aborted?T():u.signal.addEventListener("abort",T))),S||(S=null);var z=c(b);if(z&&["http","https","file"].indexOf(z)===-1){E(new l("Unsupported protocol "+z+":",l.ERR_BAD_REQUEST,u));return}g.send(S)})},me}var ke,Ge;function On(){return Ge||(Ge=1,ke=null),ke}var R=A,Ye=wn,et=K,Cn=Rt,zn=St,Pn={"Content-Type":"application/x-www-form-urlencoded"};function tt(i,e){!R.isUndefined(i)&&R.isUndefined(i["Content-Type"])&&(i["Content-Type"]=e)}function Dn(){var i;return(typeof XMLHttpRequest<"u"||typeof process<"u"&&Object.prototype.toString.call(process)==="[object process]")&&(i=Ke()),i}function Ln(i,e,t){if(R.isString(i))try{return(e||JSON.parse)(i),R.trim(i)}catch(n){if(n.name!=="SyntaxError")throw n}return(t||JSON.stringify)(i)}var oe={transitional:Cn,adapter:Dn(),transformRequest:[function(e,t){if(Ye(t,"Accept"),Ye(t,"Content-Type"),R.isFormData(e)||R.isArrayBuffer(e)||R.isBuffer(e)||R.isStream(e)||R.isFile(e)||R.isBlob(e))return e;if(R.isArrayBufferView(e))return e.buffer;if(R.isURLSearchParams(e))return tt(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString();var n=R.isObject(e),r=t&&t["Content-Type"],s;if((s=R.isFileList(e))||n&&r==="multipart/form-data"){var a=this.env&&this.env.FormData;return zn(s?{"files[]":e}:e,a&&new a)}else if(n||r==="application/json")return tt(t,"application/json"),Ln(e);return e}],transformResponse:[function(e){var t=this.transitional||oe.transitional,n=t&&t.silentJSONParsing,r=t&&t.forcedJSONParsing,s=!n&&this.responseType==="json";if(s||r&&R.isString(e)&&e.length)try{return JSON.parse(e)}catch(a){if(s)throw a.name==="SyntaxError"?et.from(a,et.ERR_BAD_RESPONSE,this,null,this.response):a}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:On()},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};R.forEach(["delete","get","head"],function(e){oe.headers[e]={}});R.forEach(["post","put","patch"],function(e){oe.headers[e]=R.merge(Pn)});var ze=oe,In=A,qn=ze,Bn=function(e,t,n){var r=this||qn;return In.forEach(n,function(a){e=a.call(r,e,t)}),e},xe,nt;function At(){return nt||(nt=1,xe=function(e){return!!(e&&e.__CANCEL__)}),xe}var rt=A,we=Bn,Nn=At(),Un=ze,jn=ae();function be(i){if(i.cancelToken&&i.cancelToken.throwIfRequested(),i.signal&&i.signal.aborted)throw new jn}var Fn=function(e){be(e),e.headers=e.headers||{},e.data=we.call(e,e.data,e.headers,e.transformRequest),e.headers=rt.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),rt.forEach(["delete","get","head","post","put","patch","common"],function(r){delete e.headers[r]});var t=e.adapter||Un.adapter;return t(e).then(function(r){return be(e),r.data=we.call(e,r.data,r.headers,e.transformResponse),r},function(r){return Nn(r)||(be(e),r&&r.response&&(r.response.data=we.call(e,r.response.data,r.response.headers,e.transformResponse))),Promise.reject(r)})},I=A,Tt=function(e,t){t=t||{};var n={};function r(c,f){return I.isPlainObject(c)&&I.isPlainObject(f)?I.merge(c,f):I.isPlainObject(f)?I.merge({},f):I.isArray(f)?f.slice():f}function s(c){if(I.isUndefined(t[c])){if(!I.isUndefined(e[c]))return r(void 0,e[c])}else return r(e[c],t[c])}function a(c){if(!I.isUndefined(t[c]))return r(void 0,t[c])}function o(c){if(I.isUndefined(t[c])){if(!I.isUndefined(e[c]))return r(void 0,e[c])}else return r(void 0,t[c])}function l(c){if(c in t)return r(e[c],t[c]);if(c in e)return r(void 0,e[c])}var p={url:a,method:a,data:a,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:l};return I.forEach(Object.keys(e).concat(Object.keys(t)),function(f){var u=p[f]||s,h=u(f);I.isUndefined(h)&&u!==l||(n[f]=h)}),n},ve,it;function Ot(){return it||(it=1,ve={version:"0.27.2"}),ve}var Mn=Ot().version,j=K,Pe={};["object","boolean","number","function","string","symbol"].forEach(function(i,e){Pe[i]=function(n){return typeof n===i||"a"+(e<1?"n ":" ")+i}});var st={};Pe.transitional=function(e,t,n){function r(s,a){return"[Axios v"+Mn+"] Transitional option '"+s+"'"+a+(n?". "+n:"")}return function(s,a,o){if(e===!1)throw new j(r(a," has been removed"+(t?" in "+t:"")),j.ERR_DEPRECATED);return t&&!st[a]&&(st[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),e?e(s,a,o):!0}};function Hn(i,e,t){if(typeof i!="object")throw new j("options must be an object",j.ERR_BAD_OPTION_VALUE);for(var n=Object.keys(i),r=n.length;r-- >0;){var s=n[r],a=e[s];if(a){var o=i[s],l=o===void 0||a(o,s,i);if(l!==!0)throw new j("option "+s+" must be "+l,j.ERR_BAD_OPTION_VALUE);continue}if(t!==!0)throw new j("Unknown option "+s,j.ERR_BAD_OPTION)}}var Zn={assertOptions:Hn,validators:Pe},Ct=A,Jn=vt,at=kn,ot=Fn,le=Tt,Qn=$t,zt=Zn,Q=zt.validators;function X(i){this.defaults=i,this.interceptors={request:new at,response:new at}}X.prototype.request=function(e,t){typeof e=="string"?(t=t||{},t.url=e):t=e||{},t=le(this.defaults,t),t.method?t.method=t.method.toLowerCase():this.defaults.method?t.method=this.defaults.method.toLowerCase():t.method="get";var n=t.transitional;n!==void 0&&zt.assertOptions(n,{silentJSONParsing:Q.transitional(Q.boolean),forcedJSONParsing:Q.transitional(Q.boolean),clarifyTimeoutError:Q.transitional(Q.boolean)},!1);var r=[],s=!0;this.interceptors.request.forEach(function(h){typeof h.runWhen=="function"&&h.runWhen(t)===!1||(s=s&&h.synchronous,r.unshift(h.fulfilled,h.rejected))});var a=[];this.interceptors.response.forEach(function(h){a.push(h.fulfilled,h.rejected)});var o;if(!s){var l=[ot,void 0];for(Array.prototype.unshift.apply(l,r),l=l.concat(a),o=Promise.resolve(t);l.length;)o=o.then(l.shift(),l.shift());return o}for(var p=t;r.length;){var c=r.shift(),f=r.shift();try{p=c(p)}catch(u){f(u);break}}try{o=ot(p)}catch(u){return Promise.reject(u)}for(;a.length;)o=o.then(a.shift(),a.shift());return o};X.prototype.getUri=function(e){e=le(this.defaults,e);var t=Qn(e.baseURL,e.url);return Jn(t,e.params,e.paramsSerializer)};Ct.forEach(["delete","get","head","options"],function(e){X.prototype[e]=function(t,n){return this.request(le(n||{},{method:e,url:t,data:(n||{}).data}))}});Ct.forEach(["post","put","patch"],function(e){function t(n){return function(s,a,o){return this.request(le(o||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:s,data:a}))}}X.prototype[e]=t(),X.prototype[e+"Form"]=t(!0)});var Vn=X,ye,lt;function Wn(){if(lt)return ye;lt=1;var i=ae();function e(t){if(typeof t!="function")throw new TypeError("executor must be a function.");var n;this.promise=new Promise(function(a){n=a});var r=this;this.promise.then(function(s){if(r._listeners){var a,o=r._listeners.length;for(a=0;a<o;a++)r._listeners[a](s);r._listeners=null}}),this.promise.then=function(s){var a,o=new Promise(function(l){r.subscribe(l),a=l}).then(s);return o.cancel=function(){r.unsubscribe(a)},o},t(function(a){r.reason||(r.reason=new i(a),n(r.reason))})}return e.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},e.prototype.subscribe=function(n){if(this.reason){n(this.reason);return}this._listeners?this._listeners.push(n):this._listeners=[n]},e.prototype.unsubscribe=function(n){if(this._listeners){var r=this._listeners.indexOf(n);r!==-1&&this._listeners.splice(r,1)}},e.source=function(){var n,r=new e(function(a){n=a});return{token:r,cancel:n}},ye=e,ye}var Ee,ut;function Xn(){return ut||(ut=1,Ee=function(e){return function(n){return e.apply(null,n)}}),Ee}var _e,ct;function Kn(){if(ct)return _e;ct=1;var i=A;return _e=function(t){return i.isObject(t)&&t.isAxiosError===!0},_e}var ht=A,Gn=xt,ee=Vn,Yn=Tt,er=ze;function Pt(i){var e=new ee(i),t=Gn(ee.prototype.request,e);return ht.extend(t,ee.prototype,e),ht.extend(t,e),t.create=function(r){return Pt(Yn(i,r))},t}var L=Pt(er);L.Axios=ee;L.CanceledError=ae();L.CancelToken=Wn();L.isCancel=At();L.VERSION=Ot().version;L.toFormData=St;L.AxiosError=K;L.Cancel=L.CanceledError;L.all=function(e){return Promise.all(e)};L.spread=Xn();L.isAxiosError=Kn();Se.exports=L;Se.exports.default=L;var tr=Se.exports,nr=tr;const _r=Mt(nr);function Dt(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}let Z=Dt();function rr(i){Z=i}const Lt=/[&<>"']/,ir=new RegExp(Lt.source,"g"),It=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,sr=new RegExp(It.source,"g"),ar={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ft=i=>ar[i];function O(i,e){if(e){if(Lt.test(i))return i.replace(ir,ft)}else if(It.test(i))return i.replace(sr,ft);return i}const or=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function qt(i){return i.replace(or,(e,t)=>(t=t.toLowerCase(),t==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""))}const lr=/(^|[^\[])\^/g;function y(i,e){i=typeof i=="string"?i:i.source,e=e||"";const t={replace:(n,r)=>(r=r.source||r,r=r.replace(lr,"$1"),i=i.replace(n,r),t),getRegex:()=>new RegExp(i,e)};return t}const ur=/[^\w:]/g,cr=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function pt(i,e,t){if(i){let n;try{n=decodeURIComponent(qt(t)).replace(ur,"").toLowerCase()}catch{return null}if(n.indexOf("javascript:")===0||n.indexOf("vbscript:")===0||n.indexOf("data:")===0)return null}e&&!cr.test(t)&&(t=dr(e,t));try{t=encodeURI(t).replace(/%25/g,"%")}catch{return null}return t}const G={},hr=/^[^:]+:\/*[^/]*$/,fr=/^([^:]+:)[\s\S]*$/,pr=/^([^:]+:\/*[^/]*)[\s\S]*$/;function dr(i,e){G[" "+i]||(hr.test(i)?G[" "+i]=i+"/":G[" "+i]=te(i,"/",!0)),i=G[" "+i];const t=i.indexOf(":")===-1;return e.substring(0,2)==="//"?t?e:i.replace(fr,"$1")+e:e.charAt(0)==="/"?t?e:i.replace(pr,"$1")+e:i+e}const re={exec:function(){}};function dt(i,e){const t=i.replace(/\|/g,(s,a,o)=>{let l=!1,p=a;for(;--p>=0&&o[p]==="\\";)l=!l;return l?"|":" |"}),n=t.split(/ \|/);let r=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),n.length>e)n.splice(e);else for(;n.length<e;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(/\\\|/g,"|");return n}function te(i,e,t){const n=i.length;if(n===0)return"";let r=0;for(;r<n;){const s=i.charAt(n-r-1);if(s===e&&!t)r++;else if(s!==e&&t)r++;else break}return i.slice(0,n-r)}function gr(i,e){if(i.indexOf(e[1])===-1)return-1;const t=i.length;let n=0,r=0;for(;r<t;r++)if(i[r]==="\\")r++;else if(i[r]===e[0])n++;else if(i[r]===e[1]&&(n--,n<0))return r;return-1}function mr(i){i&&i.sanitize&&!i.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function gt(i,e){if(e<1)return"";let t="";for(;e>1;)e&1&&(t+=i),e>>=1,i+=i;return t+i}function mt(i,e,t,n){const r=e.href,s=e.title?O(e.title):null,a=i[1].replace(/\\([\[\]])/g,"$1");if(i[0].charAt(0)!=="!"){n.state.inLink=!0;const o={type:"link",raw:t,href:r,title:s,text:a,tokens:n.inlineTokens(a)};return n.state.inLink=!1,o}return{type:"image",raw:t,href:r,title:s,text:O(a)}}function kr(i,e){const t=i.match(/^(\s+)(?:```)/);if(t===null)return e;const n=t[1];return e.split(`
`).map(r=>{const s=r.match(/^\s+/);if(s===null)return r;const[a]=s;return a.length>=n.length?r.slice(n.length):r}).join(`
`)}class De{constructor(e){this.options=e||Z}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:te(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],r=kr(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline._escapes,"$1"):t[2],text:r}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const r=te(n,"#");(this.options.pedantic||!r||/ $/.test(r))&&(n=r.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=t[0].replace(/^ *>[ \t]?/gm,""),r=this.lexer.state.top;this.lexer.state.top=!0;const s=this.lexer.blockTokens(n);return this.lexer.state.top=r,{type:"blockquote",raw:t[0],tokens:s,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n,r,s,a,o,l,p,c,f,u,h,P,E=t[1].trim();const S=E.length>1,w={type:"list",raw:"",ordered:S,start:S?+E.slice(0,-1):"",loose:!1,items:[]};E=S?`\\d{1,9}\\${E.slice(-1)}`:`\\${E}`,this.options.pedantic&&(E=S?E:"[*+-]");const v=new RegExp(`^( {0,3}${E})((?:[	 ][^\\n]*)?(?:\\n|$))`);for(;e&&(P=!1,!(!(t=v.exec(e))||this.rules.block.hr.test(e)));){if(n=t[0],e=e.substring(n.length),c=t[2].split(`
`,1)[0].replace(/^\t+/,C=>" ".repeat(3*C.length)),f=e.split(`
`,1)[0],this.options.pedantic?(a=2,h=c.trimLeft()):(a=t[2].search(/[^ ]/),a=a>4?1:a,h=c.slice(a),a+=t[1].length),l=!1,!c&&/^ *$/.test(f)&&(n+=f+`
`,e=e.substring(f.length+1),P=!0),!P){const C=new RegExp(`^ {0,${Math.min(3,a-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),g=new RegExp(`^ {0,${Math.min(3,a-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),D=new RegExp(`^ {0,${Math.min(3,a-1)}}(?:\`\`\`|~~~)`),x=new RegExp(`^ {0,${Math.min(3,a-1)}}#`);for(;e&&(u=e.split(`
`,1)[0],f=u,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!(D.test(f)||x.test(f)||C.test(f)||g.test(e)));){if(f.search(/[^ ]/)>=a||!f.trim())h+=`
`+f.slice(a);else{if(l||c.search(/[^ ]/)>=4||D.test(c)||x.test(c)||g.test(c))break;h+=`
`+f}!l&&!f.trim()&&(l=!0),n+=u+`
`,e=e.substring(u.length+1),c=f.slice(a)}}w.loose||(p?w.loose=!0:/\n *\n *$/.test(n)&&(p=!0)),this.options.gfm&&(r=/^\[[ xX]\] /.exec(h),r&&(s=r[0]!=="[ ] ",h=h.replace(/^\[[ xX]\] +/,""))),w.items.push({type:"list_item",raw:n,task:!!r,checked:s,loose:!1,text:h}),w.raw+=n}w.items[w.items.length-1].raw=n.trimRight(),w.items[w.items.length-1].text=h.trimRight(),w.raw=w.raw.trimRight();const T=w.items.length;for(o=0;o<T;o++)if(this.lexer.state.top=!1,w.items[o].tokens=this.lexer.blockTokens(w.items[o].text,[]),!w.loose){const C=w.items[o].tokens.filter(D=>D.type==="space"),g=C.length>0&&C.some(D=>/\n.*\n/.test(D.raw));w.loose=g}if(w.loose)for(o=0;o<T;o++)w.items[o].loose=!0;return w}}html(e){const t=this.rules.block.html.exec(e);if(t){const n={type:"html",raw:t[0],pre:!this.options.sanitizer&&(t[1]==="pre"||t[1]==="script"||t[1]==="style"),text:t[0]};if(this.options.sanitize){const r=this.options.sanitizer?this.options.sanitizer(t[0]):O(t[0]);n.type="paragraph",n.text=r,n.tokens=this.lexer.inline(r)}return n}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),r=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",s=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline._escapes,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:r,title:s}}}table(e){const t=this.rules.block.table.exec(e);if(t){const n={type:"table",header:dt(t[1]).map(r=>({text:r})),align:t[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[]};if(n.header.length===n.align.length){n.raw=t[0];let r=n.align.length,s,a,o,l;for(s=0;s<r;s++)/^ *-+: *$/.test(n.align[s])?n.align[s]="right":/^ *:-+: *$/.test(n.align[s])?n.align[s]="center":/^ *:-+ *$/.test(n.align[s])?n.align[s]="left":n.align[s]=null;for(r=n.rows.length,s=0;s<r;s++)n.rows[s]=dt(n.rows[s],n.header.length).map(p=>({text:p}));for(r=n.header.length,a=0;a<r;a++)n.header[a].tokens=this.lexer.inline(n.header[a].text);for(r=n.rows.length,a=0;a<r;a++)for(l=n.rows[a],o=0;o<l.length;o++)l[o].tokens=this.lexer.inline(l[o].text);return n}}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:O(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):O(t[0]):t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const a=te(n.slice(0,-1),"\\");if((n.length-a.length)%2===0)return}else{const a=gr(t[2],"()");if(a>-1){const l=(t[0].indexOf("!")===0?5:4)+t[1].length+a;t[2]=t[2].substring(0,a),t[0]=t[0].substring(0,l).trim(),t[3]=""}}let r=t[2],s="";if(this.options.pedantic){const a=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(r);a&&(r=a[1],s=a[3])}else s=t[3]?t[3].slice(1,-1):"";return r=r.trim(),/^</.test(r)&&(this.options.pedantic&&!/>$/.test(n)?r=r.slice(1):r=r.slice(1,-1)),mt(t,{href:r&&r.replace(this.rules.inline._escapes,"$1"),title:s&&s.replace(this.rules.inline._escapes,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let r=(n[2]||n[1]).replace(/\s+/g," ");if(r=t[r.toLowerCase()],!r){const s=n[0].charAt(0);return{type:"text",raw:s,text:s}}return mt(n,r,n[0],this.lexer)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrong.lDelim.exec(e);if(!r||r[3]&&n.match(/[\p{L}\p{N}]/u))return;const s=r[1]||r[2]||"";if(!s||s&&(n===""||this.rules.inline.punctuation.exec(n))){const a=r[0].length-1;let o,l,p=a,c=0;const f=r[0][0]==="*"?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(f.lastIndex=0,t=t.slice(-1*e.length+a);(r=f.exec(t))!=null;){if(o=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!o)continue;if(l=o.length,r[3]||r[4]){p+=l;continue}else if((r[5]||r[6])&&a%3&&!((a+l)%3)){c+=l;continue}if(p-=l,p>0)continue;l=Math.min(l,l+p+c);const u=e.slice(0,a+r.index+(r[0].length-o.length)+l);if(Math.min(a,l)%2){const P=u.slice(1,-1);return{type:"em",raw:u,text:P,tokens:this.lexer.inlineTokens(P)}}const h=u.slice(2,-2);return{type:"strong",raw:u,text:h,tokens:this.lexer.inlineTokens(h)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const r=/[^ ]/.test(n),s=/^ /.test(n)&&/ $/.test(n);return r&&s&&(n=n.substring(1,n.length-1)),n=O(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e,t){const n=this.rules.inline.autolink.exec(e);if(n){let r,s;return n[2]==="@"?(r=O(this.options.mangle?t(n[1]):n[1]),s="mailto:"+r):(r=O(n[1]),s=r),{type:"link",raw:n[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}url(e,t){let n;if(n=this.rules.inline.url.exec(e)){let r,s;if(n[2]==="@")r=O(this.options.mangle?t(n[0]):n[0]),s="mailto:"+r;else{let a;do a=n[0],n[0]=this.rules.inline._backpedal.exec(n[0])[0];while(a!==n[0]);r=O(n[0]),n[1]==="www."?s="http://"+n[0]:s=n[0]}return{type:"link",raw:n[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(e,t){const n=this.rules.inline.text.exec(e);if(n){let r;return this.lexer.state.inRawBlock?r=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(n[0]):O(n[0]):n[0]:r=O(this.options.smartypants?t(n[0]):n[0]),{type:"text",raw:n[0],text:r}}}}const m={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:re,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/};m._label=/(?!\s*\])(?:\\.|[^\[\]\\])+/;m._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/;m.def=y(m.def).replace("label",m._label).replace("title",m._title).getRegex();m.bullet=/(?:[*+-]|\d{1,9}[.)])/;m.listItemStart=y(/^( *)(bull) */).replace("bull",m.bullet).getRegex();m.list=y(m.list).replace(/bull/g,m.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+m.def.source+")").getRegex();m._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul";m._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/;m.html=y(m.html,"i").replace("comment",m._comment).replace("tag",m._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex();m.paragraph=y(m._paragraph).replace("hr",m.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",m._tag).getRegex();m.blockquote=y(m.blockquote).replace("paragraph",m.paragraph).getRegex();m.normal={...m};m.gfm={...m.normal,table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"};m.gfm.table=y(m.gfm.table).replace("hr",m.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",m._tag).getRegex();m.gfm.paragraph=y(m._paragraph).replace("hr",m.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",m.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",m._tag).getRegex();m.pedantic={...m.normal,html:y(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",m._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:re,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:y(m.normal._paragraph).replace("hr",m.hr).replace("heading",` *#{1,6} *[^
]`).replace("lheading",m.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()};const d={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:re,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:re,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};d._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~";d.punctuation=y(d.punctuation).replace(/punctuation/g,d._punctuation).getRegex();d.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g;d.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g;d._comment=y(m._comment).replace("(?:-->|$)","-->").getRegex();d.emStrong.lDelim=y(d.emStrong.lDelim).replace(/punct/g,d._punctuation).getRegex();d.emStrong.rDelimAst=y(d.emStrong.rDelimAst,"g").replace(/punct/g,d._punctuation).getRegex();d.emStrong.rDelimUnd=y(d.emStrong.rDelimUnd,"g").replace(/punct/g,d._punctuation).getRegex();d._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g;d._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;d._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;d.autolink=y(d.autolink).replace("scheme",d._scheme).replace("email",d._email).getRegex();d._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/;d.tag=y(d.tag).replace("comment",d._comment).replace("attribute",d._attribute).getRegex();d._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/;d._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/;d._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/;d.link=y(d.link).replace("label",d._label).replace("href",d._href).replace("title",d._title).getRegex();d.reflink=y(d.reflink).replace("label",d._label).replace("ref",m._label).getRegex();d.nolink=y(d.nolink).replace("ref",m._label).getRegex();d.reflinkSearch=y(d.reflinkSearch,"g").replace("reflink",d.reflink).replace("nolink",d.nolink).getRegex();d.normal={...d};d.pedantic={...d.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:y(/^!?\[(label)\]\((.*?)\)/).replace("label",d._label).getRegex(),reflink:y(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",d._label).getRegex()};d.gfm={...d.normal,escape:y(d.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/};d.gfm.url=y(d.gfm.url,"i").replace("email",d.gfm._extended_email).getRegex();d.breaks={...d.gfm,br:y(d.br).replace("{2,}","*").getRegex(),text:y(d.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};function xr(i){return i.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function kt(i){let e="",t,n;const r=i.length;for(t=0;t<r;t++)n=i.charCodeAt(t),Math.random()>.5&&(n="x"+n.toString(16)),e+="&#"+n+";";return e}class F{constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Z,this.options.tokenizer=this.options.tokenizer||new De,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:m.normal,inline:d.normal};this.options.pedantic?(t.block=m.pedantic,t.inline=d.pedantic):this.options.gfm&&(t.block=m.gfm,this.options.breaks?t.inline=d.breaks:t.inline=d.gfm),this.tokenizer.rules=t}static get rules(){return{block:m,inline:d}}static lex(e,t){return new F(t).lex(e)}static lexInline(e,t){return new F(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);let t;for(;t=this.inlineQueue.shift();)this.inlineTokens(t.src,t.tokens);return this.tokens}blockTokens(e,t=[]){this.options.pedantic?e=e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e=e.replace(/^( *)(\t+)/gm,(o,l,p)=>l+"    ".repeat(p.length));let n,r,s,a;for(;e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(o=>(n=o.call({lexer:this},e,t))?(e=e.substring(n.raw.length),t.push(n),!0):!1))){if(n=this.tokenizer.space(e)){e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);continue}if(n=this.tokenizer.code(e)){e=e.substring(n.raw.length),r=t[t.length-1],r&&(r.type==="paragraph"||r.type==="text")?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n);continue}if(n=this.tokenizer.fences(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.heading(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.hr(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.blockquote(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.list(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.html(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.def(e)){e=e.substring(n.raw.length),r=t[t.length-1],r&&(r.type==="paragraph"||r.type==="text")?(r.raw+=`
`+n.raw,r.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=r.text):this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title});continue}if(n=this.tokenizer.table(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.lheading(e)){e=e.substring(n.raw.length),t.push(n);continue}if(s=e,this.options.extensions&&this.options.extensions.startBlock){let o=1/0;const l=e.slice(1);let p;this.options.extensions.startBlock.forEach(function(c){p=c.call({lexer:this},l),typeof p=="number"&&p>=0&&(o=Math.min(o,p))}),o<1/0&&o>=0&&(s=e.substring(0,o+1))}if(this.state.top&&(n=this.tokenizer.paragraph(s))){r=t[t.length-1],a&&r.type==="paragraph"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n),a=s.length!==e.length,e=e.substring(n.raw.length);continue}if(n=this.tokenizer.text(e)){e=e.substring(n.raw.length),r=t[t.length-1],r&&r.type==="text"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n);continue}if(e){const o="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(o);break}else throw new Error(o)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,r,s,a=e,o,l,p;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(a))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(a=a.slice(0,o.index)+"["+gt("a",o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(a))!=null;)a=a.slice(0,o.index)+"["+gt("a",o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.escapedEmSt.exec(a))!=null;)a=a.slice(0,o.index+o[0].length-2)+"++"+a.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(;e;)if(l||(p=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>(n=c.call({lexer:this},e,t))?(e=e.substring(n.raw.length),t.push(n),!0):!1))){if(n=this.tokenizer.escape(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.tag(e)){e=e.substring(n.raw.length),r=t[t.length-1],r&&n.type==="text"&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);continue}if(n=this.tokenizer.link(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(n.raw.length),r=t[t.length-1],r&&n.type==="text"&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);continue}if(n=this.tokenizer.emStrong(e,a,p)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.codespan(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.br(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.del(e)){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.autolink(e,kt)){e=e.substring(n.raw.length),t.push(n);continue}if(!this.state.inLink&&(n=this.tokenizer.url(e,kt))){e=e.substring(n.raw.length),t.push(n);continue}if(s=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const f=e.slice(1);let u;this.options.extensions.startInline.forEach(function(h){u=h.call({lexer:this},f),typeof u=="number"&&u>=0&&(c=Math.min(c,u))}),c<1/0&&c>=0&&(s=e.substring(0,c+1))}if(n=this.tokenizer.inlineText(s,xr)){e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(p=n.raw.slice(-1)),l=!0,r=t[t.length-1],r&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);continue}if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return t}}class Le{constructor(e){this.options=e||Z}code(e,t,n){const r=(t||"").match(/\S*/)[0];if(this.options.highlight){const s=this.options.highlight(e,r);s!=null&&s!==e&&(n=!0,e=s)}return e=e.replace(/\n$/,"")+`
`,r?'<pre><code class="'+this.options.langPrefix+O(r)+'">'+(n?e:O(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:O(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e){return e}heading(e,t,n,r){if(this.options.headerIds){const s=this.options.headerPrefix+r.slug(n);return`<h${t} id="${s}">${e}</h${t}>
`}return`<h${t}>${e}</h${t}>
`}hr(){return this.options.xhtml?`<hr/>
`:`<hr>
`}list(e,t,n){const r=t?"ol":"ul",s=t&&n!==1?' start="'+n+'"':"";return"<"+r+s+`>
`+e+"</"+r+`>
`}listitem(e){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return this.options.xhtml?"<br/>":"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){if(e=pt(this.options.sanitize,this.options.baseUrl,e),e===null)return n;let r='<a href="'+e+'"';return t&&(r+=' title="'+t+'"'),r+=">"+n+"</a>",r}image(e,t,n){if(e=pt(this.options.sanitize,this.options.baseUrl,e),e===null)return n;let r=`<img src="${e}" alt="${n}"`;return t&&(r+=` title="${t}"`),r+=this.options.xhtml?"/>":">",r}text(e){return e}}class Bt{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class Nt{constructor(){this.seen={}}serialize(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/ig,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(e,t){let n=e,r=0;if(this.seen.hasOwnProperty(n)){r=this.seen[e];do r++,n=e+"-"+r;while(this.seen.hasOwnProperty(n))}return t||(this.seen[e]=r,this.seen[n]=0),n}slug(e,t={}){const n=this.serialize(e);return this.getNextSafeSlug(n,t.dryrun)}}class M{constructor(e){this.options=e||Z,this.options.renderer=this.options.renderer||new Le,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Bt,this.slugger=new Nt}static parse(e,t){return new M(t).parse(e)}static parseInline(e,t){return new M(t).parseInline(e)}parse(e,t=!0){let n="",r,s,a,o,l,p,c,f,u,h,P,E,S,w,v,T,C,g,D;const x=e.length;for(r=0;r<x;r++){if(h=e[r],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[h.type]&&(D=this.options.extensions.renderers[h.type].call({parser:this},h),D!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(h.type))){n+=D||"";continue}switch(h.type){case"space":continue;case"hr":{n+=this.renderer.hr();continue}case"heading":{n+=this.renderer.heading(this.parseInline(h.tokens),h.depth,qt(this.parseInline(h.tokens,this.textRenderer)),this.slugger);continue}case"code":{n+=this.renderer.code(h.text,h.lang,h.escaped);continue}case"table":{for(f="",c="",o=h.header.length,s=0;s<o;s++)c+=this.renderer.tablecell(this.parseInline(h.header[s].tokens),{header:!0,align:h.align[s]});for(f+=this.renderer.tablerow(c),u="",o=h.rows.length,s=0;s<o;s++){for(p=h.rows[s],c="",l=p.length,a=0;a<l;a++)c+=this.renderer.tablecell(this.parseInline(p[a].tokens),{header:!1,align:h.align[a]});u+=this.renderer.tablerow(c)}n+=this.renderer.table(f,u);continue}case"blockquote":{u=this.parse(h.tokens),n+=this.renderer.blockquote(u);continue}case"list":{for(P=h.ordered,E=h.start,S=h.loose,o=h.items.length,u="",s=0;s<o;s++)v=h.items[s],T=v.checked,C=v.task,w="",v.task&&(g=this.renderer.checkbox(T),S?v.tokens.length>0&&v.tokens[0].type==="paragraph"?(v.tokens[0].text=g+" "+v.tokens[0].text,v.tokens[0].tokens&&v.tokens[0].tokens.length>0&&v.tokens[0].tokens[0].type==="text"&&(v.tokens[0].tokens[0].text=g+" "+v.tokens[0].tokens[0].text)):v.tokens.unshift({type:"text",text:g}):w+=g),w+=this.parse(v.tokens,S),u+=this.renderer.listitem(w,C,T);n+=this.renderer.list(u,P,E);continue}case"html":{n+=this.renderer.html(h.text);continue}case"paragraph":{n+=this.renderer.paragraph(this.parseInline(h.tokens));continue}case"text":{for(u=h.tokens?this.parseInline(h.tokens):h.text;r+1<x&&e[r+1].type==="text";)h=e[++r],u+=`
`+(h.tokens?this.parseInline(h.tokens):h.text);n+=t?this.renderer.paragraph(u):u;continue}default:{const b='Token with "'+h.type+'" type was not found.';if(this.options.silent){console.error(b);return}else throw new Error(b)}}}return n}parseInline(e,t){t=t||this.renderer;let n="",r,s,a;const o=e.length;for(r=0;r<o;r++){if(s=e[r],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]&&(a=this.options.extensions.renderers[s.type].call({parser:this},s),a!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type))){n+=a||"";continue}switch(s.type){case"escape":{n+=t.text(s.text);break}case"html":{n+=t.html(s.text);break}case"link":{n+=t.link(s.href,s.title,this.parseInline(s.tokens,t));break}case"image":{n+=t.image(s.href,s.title,s.text);break}case"strong":{n+=t.strong(this.parseInline(s.tokens,t));break}case"em":{n+=t.em(this.parseInline(s.tokens,t));break}case"codespan":{n+=t.codespan(s.text);break}case"br":{n+=t.br();break}case"del":{n+=t.del(this.parseInline(s.tokens,t));break}case"text":{n+=t.text(s.text);break}default:{const l='Token with "'+s.type+'" type was not found.';if(this.options.silent){console.error(l);return}else throw new Error(l)}}}return n}}class ie{constructor(e){this.options=e||Z}preprocess(e){return e}postprocess(e){return e}}qe(ie,"passThroughHooks",new Set(["preprocess","postprocess"]));function wr(i,e,t){return n=>{if(n.message+=`
Please report this to https://github.com/markedjs/marked.`,i){const r="<p>An error occurred:</p><pre>"+O(n.message+"",!0)+"</pre>";if(e)return Promise.resolve(r);if(t){t(null,r);return}return r}if(e)return Promise.reject(n);if(t){t(n);return}throw n}}function Ut(i,e){return(t,n,r)=>{typeof n=="function"&&(r=n,n=null);const s={...n};n={...k.defaults,...s};const a=wr(n.silent,n.async,r);if(typeof t>"u"||t===null)return a(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return a(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(mr(n),n.hooks&&(n.hooks.options=n),r){const o=n.highlight;let l;try{n.hooks&&(t=n.hooks.preprocess(t)),l=i(t,n)}catch(f){return a(f)}const p=function(f){let u;if(!f)try{n.walkTokens&&k.walkTokens(l,n.walkTokens),u=e(l,n),n.hooks&&(u=n.hooks.postprocess(u))}catch(h){f=h}return n.highlight=o,f?a(f):r(null,u)};if(!o||o.length<3||(delete n.highlight,!l.length))return p();let c=0;k.walkTokens(l,function(f){f.type==="code"&&(c++,setTimeout(()=>{o(f.text,f.lang,function(u,h){if(u)return p(u);h!=null&&h!==f.text&&(f.text=h,f.escaped=!0),c--,c===0&&p()})},0))}),c===0&&p();return}if(n.async)return Promise.resolve(n.hooks?n.hooks.preprocess(t):t).then(o=>i(o,n)).then(o=>n.walkTokens?Promise.all(k.walkTokens(o,n.walkTokens)).then(()=>o):o).then(o=>e(o,n)).then(o=>n.hooks?n.hooks.postprocess(o):o).catch(a);try{n.hooks&&(t=n.hooks.preprocess(t));const o=i(t,n);n.walkTokens&&k.walkTokens(o,n.walkTokens);let l=e(o,n);return n.hooks&&(l=n.hooks.postprocess(l)),l}catch(o){return a(o)}}}function k(i,e,t){return Ut(F.lex,M.parse)(i,e,t)}k.options=k.setOptions=function(i){return k.defaults={...k.defaults,...i},rr(k.defaults),k};k.getDefaults=Dt;k.defaults=Z;k.use=function(...i){const e=k.defaults.extensions||{renderers:{},childTokens:{}};i.forEach(t=>{const n={...t};if(n.async=k.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(r=>{if(!r.name)throw new Error("extension name required");if(r.renderer){const s=e.renderers[r.name];s?e.renderers[r.name]=function(...a){let o=r.renderer.apply(this,a);return o===!1&&(o=s.apply(this,a)),o}:e.renderers[r.name]=r.renderer}if(r.tokenizer){if(!r.level||r.level!=="block"&&r.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");e[r.level]?e[r.level].unshift(r.tokenizer):e[r.level]=[r.tokenizer],r.start&&(r.level==="block"?e.startBlock?e.startBlock.push(r.start):e.startBlock=[r.start]:r.level==="inline"&&(e.startInline?e.startInline.push(r.start):e.startInline=[r.start]))}r.childTokens&&(e.childTokens[r.name]=r.childTokens)}),n.extensions=e),t.renderer){const r=k.defaults.renderer||new Le;for(const s in t.renderer){const a=r[s];r[s]=(...o)=>{let l=t.renderer[s].apply(r,o);return l===!1&&(l=a.apply(r,o)),l}}n.renderer=r}if(t.tokenizer){const r=k.defaults.tokenizer||new De;for(const s in t.tokenizer){const a=r[s];r[s]=(...o)=>{let l=t.tokenizer[s].apply(r,o);return l===!1&&(l=a.apply(r,o)),l}}n.tokenizer=r}if(t.hooks){const r=k.defaults.hooks||new ie;for(const s in t.hooks){const a=r[s];ie.passThroughHooks.has(s)?r[s]=o=>{if(k.defaults.async)return Promise.resolve(t.hooks[s].call(r,o)).then(p=>a.call(r,p));const l=t.hooks[s].call(r,o);return a.call(r,l)}:r[s]=(...o)=>{let l=t.hooks[s].apply(r,o);return l===!1&&(l=a.apply(r,o)),l}}n.hooks=r}if(t.walkTokens){const r=k.defaults.walkTokens;n.walkTokens=function(s){let a=[];return a.push(t.walkTokens.call(this,s)),r&&(a=a.concat(r.call(this,s))),a}}k.setOptions(n)})};k.walkTokens=function(i,e){let t=[];for(const n of i)switch(t=t.concat(e.call(k,n)),n.type){case"table":{for(const r of n.header)t=t.concat(k.walkTokens(r.tokens,e));for(const r of n.rows)for(const s of r)t=t.concat(k.walkTokens(s.tokens,e));break}case"list":{t=t.concat(k.walkTokens(n.items,e));break}default:k.defaults.extensions&&k.defaults.extensions.childTokens&&k.defaults.extensions.childTokens[n.type]?k.defaults.extensions.childTokens[n.type].forEach(function(r){t=t.concat(k.walkTokens(n[r],e))}):n.tokens&&(t=t.concat(k.walkTokens(n.tokens,e)))}return t};k.parseInline=Ut(F.lexInline,M.parseInline);k.Parser=M;k.parser=M.parse;k.Renderer=Le;k.TextRenderer=Bt;k.Lexer=F;k.lexer=F.lex;k.Tokenizer=De;k.Slugger=Nt;k.Hooks=ie;k.parse=k;k.options;k.setOptions;k.use;k.walkTokens;k.parseInline;M.parse;F.lex;export{_r as a,Er as c,k as m};
