import{s as a,c as s,a as e,C as t}from"./chunk-SZ463SBG-CjVtUQLg.js";import{_ as i}from"./index-DTzYmM8W.js";import"./chunk-E2GYISFI-Q1iAz61J.js";import"./chunk-BFAMUDN2-DFWQ9R70.js";import"./chunk-SKB7J2MH-B3bZuGXG.js";import"./semi-ui-Csx8wKaA.js";import"./react-core-DskXcPn0.js";import"./tools-C3llIrvJ.js";import"./react-components-C55tCU1e.js";import"./i18n-bne0o_C4.js";var c={parser:e,get db(){return new t},renderer:s,styles:a,init:i(r=>{r.class||(r.class={}),r.class.arrowMarkerAbsolute=r.arrowMarkerAbsolute},"init")};export{c as diagram};
