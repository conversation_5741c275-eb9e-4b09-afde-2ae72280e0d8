package main

import (
	"fmt"
	"log"
	"one-api/common"
	"one-api/model"
	"time"
)

func main() {
	// 初始化数据库连接
	err := common.InitDB()
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// 创建新的渠道
	channel := &model.Channel{
		Type:        8, // 自定义渠道类型
		Name:        "赚钱机场",
		Key:         "19168228ef329e1edd063077c534ea14",
		BaseURL:     stringPtr("https://dash.pqjc.site/api/v1/client/subscribe"),
		Models:      "gpt-3.5-turbo,gpt-4,gpt-4-turbo,claude-3-sonnet,claude-3-opus", // 常见AI模型
		Group:       "default",
		Weight:      uintPtr(1),
		Status:      1, // 启用状态
		CreatedTime: time.Now().Unix(),
		AutoBan:     intPtr(1),
		Priority:    int64Ptr(0),
		Other:       "", // 可以存储额外配置
		Tag:         stringPtr("机场代理"),
	}

	// 插入渠道到数据库
	err = channel.Insert()
	if err != nil {
		log.Fatal("Failed to insert channel:", err)
	}

	fmt.Printf("✅ 成功添加渠道: %s (ID: %d)\n", channel.Name, channel.Id)
	fmt.Println("渠道信息:")
	fmt.Printf("  - 名称: %s\n", channel.Name)
	fmt.Printf("  - 类型: %d (自定义)\n", channel.Type)
	fmt.Printf("  - Base URL: %s\n", *channel.BaseURL)
	fmt.Printf("  - API Key: %s\n", channel.Key)
	fmt.Printf("  - 支持模型: %s\n", channel.Models)
	fmt.Printf("  - 分组: %s\n", channel.Group)
	fmt.Printf("  - 权重: %d\n", *channel.Weight)
	fmt.Printf("  - 状态: %s\n", getStatusText(channel.Status))
	
	// 初始化渠道缓存
	model.InitChannelCache()
	fmt.Println("✅ 渠道缓存已更新")
}

// 辅助函数
func stringPtr(s string) *string {
	return &s
}

func uintPtr(u uint) *uint {
	return &u
}

func intPtr(i int) *int {
	return &i
}

func int64Ptr(i int64) *int64 {
	return &i
}

func getStatusText(status int) string {
	switch status {
	case 1:
		return "启用"
	case 2:
		return "禁用"
	default:
		return "未知"
	}
}
