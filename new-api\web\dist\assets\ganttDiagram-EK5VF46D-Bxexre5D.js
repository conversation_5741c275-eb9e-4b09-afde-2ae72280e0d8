import{bF as Ke,bG as In,bH as tn,bI as en,bJ as nn,bK as re,bL as An,_ as y,g as Wn,a as Nn,t as Hn,q as On,b as Vn,c as zn,d as _t,e as Zt,f as Pn,bM as it,l as jt,m as Rn,k as Bn,z as Zn,u as qn}from"./index-DTzYmM8W.js";import{g as be,c as xe}from"./react-core-DskXcPn0.js";import{c as Gn,a as Xn,l as Qn}from"./linear-BFJNc_PP.js";import{i as jn}from"./init-Gi6I4Gst.js";import"./semi-ui-Csx8wKaA.js";import"./tools-C3llIrvJ.js";import"./react-components-C55tCU1e.js";import"./i18n-bne0o_C4.js";import"./defaultLocale-R7l282Le.js";var rn={exports:{}};(function(t,e){(function(n,r){t.exports=r()})(xe,function(){return function(n,r){var i=r.prototype,a=i.format;i.format=function(s){var h=this,p=this.$locale();if(!this.isValid())return a.bind(this)(s);var d=this.$utils(),m=(s||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,function(M){switch(M){case"Q":return Math.ceil((h.$M+1)/3);case"Do":return p.ordinal(h.$D);case"gggg":return h.weekYear();case"GGGG":return h.isoWeekYear();case"wo":return p.ordinal(h.week(),"W");case"w":case"ww":return d.s(h.week(),M==="w"?1:2,"0");case"W":case"WW":return d.s(h.isoWeek(),M==="W"?1:2,"0");case"k":case"kk":return d.s(String(h.$H===0?24:h.$H),M==="k"?1:2,"0");case"X":return Math.floor(h.$d.getTime()/1e3);case"x":return h.$d.getTime();case"z":return"["+h.offsetName()+"]";case"zzz":return"["+h.offsetName("long")+"]";default:return M}});return a.bind(this)(m)}}})})(rn);var $n=rn.exports;const Jn=be($n);var an={exports:{}};(function(t,e){(function(n,r){t.exports=r()})(xe,function(){var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},r=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,i=/\d/,a=/\d\d/,s=/\d\d?/,h=/\d*[^-_:/,()\s\d]+/,p={},d=function(_){return(_=+_)+(_>68?1900:2e3)},m=function(_){return function(A){this[_]=+A}},M=[/[+-]\d\d:?(\d\d)?|Z/,function(_){(this.zone||(this.zone={})).offset=function(A){if(!A||A==="Z")return 0;var V=A.match(/([+-]|\d\d)/g),W=60*V[1]+(+V[2]||0);return W===0?0:V[0]==="+"?-W:W}(_)}],D=function(_){var A=p[_];return A&&(A.indexOf?A:A.s.concat(A.f))},x=function(_,A){var V,W=p.meridiem;if(W){for(var Z=1;Z<=24;Z+=1)if(_.indexOf(W(Z,0,A))>-1){V=Z>12;break}}else V=_===(A?"pm":"PM");return V},G={A:[h,function(_){this.afternoon=x(_,!1)}],a:[h,function(_){this.afternoon=x(_,!0)}],Q:[i,function(_){this.month=3*(_-1)+1}],S:[i,function(_){this.milliseconds=100*+_}],SS:[a,function(_){this.milliseconds=10*+_}],SSS:[/\d{3}/,function(_){this.milliseconds=+_}],s:[s,m("seconds")],ss:[s,m("seconds")],m:[s,m("minutes")],mm:[s,m("minutes")],H:[s,m("hours")],h:[s,m("hours")],HH:[s,m("hours")],hh:[s,m("hours")],D:[s,m("day")],DD:[a,m("day")],Do:[h,function(_){var A=p.ordinal,V=_.match(/\d+/);if(this.day=V[0],A)for(var W=1;W<=31;W+=1)A(W).replace(/\[|\]/g,"")===_&&(this.day=W)}],w:[s,m("week")],ww:[a,m("week")],M:[s,m("month")],MM:[a,m("month")],MMM:[h,function(_){var A=D("months"),V=(D("monthsShort")||A.map(function(W){return W.slice(0,3)})).indexOf(_)+1;if(V<1)throw new Error;this.month=V%12||V}],MMMM:[h,function(_){var A=D("months").indexOf(_)+1;if(A<1)throw new Error;this.month=A%12||A}],Y:[/[+-]?\d+/,m("year")],YY:[a,function(_){this.year=d(_)}],YYYY:[/\d{4}/,m("year")],Z:M,ZZ:M};function N(_){var A,V;A=_,V=p&&p.formats;for(var W=(_=A.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(F,S,T){var U=T&&T.toUpperCase();return S||V[T]||n[T]||V[U].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(u,g,v){return g||v.slice(1)})})).match(r),Z=W.length,j=0;j<Z;j+=1){var C=W[j],H=G[C],w=H&&H[0],Y=H&&H[1];W[j]=Y?{regex:w,parser:Y}:C.replace(/^\[|\]$/g,"")}return function(F){for(var S={},T=0,U=0;T<Z;T+=1){var u=W[T];if(typeof u=="string")U+=u.length;else{var g=u.regex,v=u.parser,k=F.slice(U),E=g.exec(k)[0];v.call(S,E),F=F.replace(E,"")}}return function(c){var l=c.afternoon;if(l!==void 0){var o=c.hours;l?o<12&&(c.hours+=12):o===12&&(c.hours=0),delete c.afternoon}}(S),S}}return function(_,A,V){V.p.customParseFormat=!0,_&&_.parseTwoDigitYear&&(d=_.parseTwoDigitYear);var W=A.prototype,Z=W.parse;W.parse=function(j){var C=j.date,H=j.utc,w=j.args;this.$u=H;var Y=w[1];if(typeof Y=="string"){var F=w[2]===!0,S=w[3]===!0,T=F||S,U=w[2];S&&(U=w[2]),p=this.$locale(),!F&&U&&(p=V.Ls[U]),this.$d=function(k,E,c,l){try{if(["x","X"].indexOf(E)>-1)return new Date((E==="X"?1e3:1)*k);var o=N(E)(k),R=o.year,z=o.month,P=o.day,K=o.hours,X=o.minutes,$=o.seconds,at=o.milliseconds,b=o.zone,I=o.week,O=new Date,f=P||(R||z?1:O.getDate()),J=R||O.getFullYear(),L=0;R&&!z||(L=z>0?z-1:O.getMonth());var Q,q=K||0,rt=X||0,st=$||0,pt=at||0;return b?new Date(Date.UTC(J,L,f,q,rt,st,pt+60*b.offset*1e3)):c?new Date(Date.UTC(J,L,f,q,rt,st,pt)):(Q=new Date(J,L,f,q,rt,st,pt),I&&(Q=l(Q).week(I).toDate()),Q)}catch{return new Date("")}}(C,Y,H,V),this.init(),U&&U!==!0&&(this.$L=this.locale(U).$L),T&&C!=this.format(Y)&&(this.$d=new Date("")),p={}}else if(Y instanceof Array)for(var u=Y.length,g=1;g<=u;g+=1){w[1]=Y[g-1];var v=V.apply(this,w);if(v.isValid()){this.$d=v.$d,this.$L=v.$L,this.init();break}g===u&&(this.$d=new Date(""))}else Z.call(this,j)}}})})(an);var Kn=an.exports;const tr=be(Kn);function er(t,e){let n;if(e===void 0)for(const r of t)r!=null&&(n<r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of t)(i=e(i,++r,t))!=null&&(n<i||n===void 0&&i>=i)&&(n=i)}return n}function nr(t,e){let n;if(e===void 0)for(const r of t)r!=null&&(n>r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of t)(i=e(i,++r,t))!=null&&(n>i||n===void 0&&i>=i)&&(n=i)}return n}function rr(t){return t}var Gt=1,ie=2,ge=3,qt=4,We=1e-6;function ir(t){return"translate("+t+",0)"}function ar(t){return"translate(0,"+t+")"}function sr(t){return e=>+t(e)}function or(t,e){return e=Math.max(0,t.bandwidth()-e*2)/2,t.round()&&(e=Math.round(e)),n=>+t(n)+e}function cr(){return!this.__axis}function sn(t,e){var n=[],r=null,i=null,a=6,s=6,h=3,p=typeof window<"u"&&window.devicePixelRatio>1?0:.5,d=t===Gt||t===qt?-1:1,m=t===qt||t===ie?"x":"y",M=t===Gt||t===ge?ir:ar;function D(x){var G=r??(e.ticks?e.ticks.apply(e,n):e.domain()),N=i??(e.tickFormat?e.tickFormat.apply(e,n):rr),_=Math.max(a,0)+h,A=e.range(),V=+A[0]+p,W=+A[A.length-1]+p,Z=(e.bandwidth?or:sr)(e.copy(),p),j=x.selection?x.selection():x,C=j.selectAll(".domain").data([null]),H=j.selectAll(".tick").data(G,e).order(),w=H.exit(),Y=H.enter().append("g").attr("class","tick"),F=H.select("line"),S=H.select("text");C=C.merge(C.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),H=H.merge(Y),F=F.merge(Y.append("line").attr("stroke","currentColor").attr(m+"2",d*a)),S=S.merge(Y.append("text").attr("fill","currentColor").attr(m,d*_).attr("dy",t===Gt?"0em":t===ge?"0.71em":"0.32em")),x!==j&&(C=C.transition(x),H=H.transition(x),F=F.transition(x),S=S.transition(x),w=w.transition(x).attr("opacity",We).attr("transform",function(T){return isFinite(T=Z(T))?M(T+p):this.getAttribute("transform")}),Y.attr("opacity",We).attr("transform",function(T){var U=this.parentNode.__axis;return M((U&&isFinite(U=U(T))?U:Z(T))+p)})),w.remove(),C.attr("d",t===qt||t===ie?s?"M"+d*s+","+V+"H"+p+"V"+W+"H"+d*s:"M"+p+","+V+"V"+W:s?"M"+V+","+d*s+"V"+p+"H"+W+"V"+d*s:"M"+V+","+p+"H"+W),H.attr("opacity",1).attr("transform",function(T){return M(Z(T)+p)}),F.attr(m+"2",d*a),S.attr(m,d*_).text(N),j.filter(cr).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===ie?"start":t===qt?"end":"middle"),j.each(function(){this.__axis=Z})}return D.scale=function(x){return arguments.length?(e=x,D):e},D.ticks=function(){return n=Array.from(arguments),D},D.tickArguments=function(x){return arguments.length?(n=x==null?[]:Array.from(x),D):n.slice()},D.tickValues=function(x){return arguments.length?(r=x==null?null:Array.from(x),D):r&&r.slice()},D.tickFormat=function(x){return arguments.length?(i=x,D):i},D.tickSize=function(x){return arguments.length?(a=s=+x,D):a},D.tickSizeInner=function(x){return arguments.length?(a=+x,D):a},D.tickSizeOuter=function(x){return arguments.length?(s=+x,D):s},D.tickPadding=function(x){return arguments.length?(h=+x,D):h},D.offset=function(x){return arguments.length?(p=+x,D):p},D}function lr(t){return sn(Gt,t)}function ur(t){return sn(ge,t)}const fr=Math.PI/180,hr=180/Math.PI,$t=18,on=.96422,cn=1,ln=.82521,un=4/29,St=6/29,fn=3*St*St,dr=St*St*St;function hn(t){if(t instanceof ft)return new ft(t.l,t.a,t.b,t.opacity);if(t instanceof dt)return dn(t);t instanceof Ke||(t=In(t));var e=ce(t.r),n=ce(t.g),r=ce(t.b),i=ae((.2225045*e+.7168786*n+.0606169*r)/cn),a,s;return e===n&&n===r?a=s=i:(a=ae((.4360747*e+.3850649*n+.1430804*r)/on),s=ae((.0139322*e+.0971045*n+.7141733*r)/ln)),new ft(116*i-16,500*(a-i),200*(i-s),t.opacity)}function mr(t,e,n,r){return arguments.length===1?hn(t):new ft(t,e,n,r??1)}function ft(t,e,n,r){this.l=+t,this.a=+e,this.b=+n,this.opacity=+r}tn(ft,mr,en(nn,{brighter(t){return new ft(this.l+$t*(t??1),this.a,this.b,this.opacity)},darker(t){return new ft(this.l-$t*(t??1),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,n=isNaN(this.b)?t:t-this.b/200;return e=on*se(e),t=cn*se(t),n=ln*se(n),new Ke(oe(3.1338561*e-1.6168667*t-.4906146*n),oe(-.9787684*e+1.9161415*t+.033454*n),oe(.0719453*e-.2289914*t+1.4052427*n),this.opacity)}}));function ae(t){return t>dr?Math.pow(t,1/3):t/fn+un}function se(t){return t>St?t*t*t:fn*(t-un)}function oe(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function ce(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function gr(t){if(t instanceof dt)return new dt(t.h,t.c,t.l,t.opacity);if(t instanceof ft||(t=hn(t)),t.a===0&&t.b===0)return new dt(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*hr;return new dt(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function ye(t,e,n,r){return arguments.length===1?gr(t):new dt(t,e,n,r??1)}function dt(t,e,n,r){this.h=+t,this.c=+e,this.l=+n,this.opacity=+r}function dn(t){if(isNaN(t.h))return new ft(t.l,0,0,t.opacity);var e=t.h*fr;return new ft(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}tn(dt,ye,en(nn,{brighter(t){return new dt(this.h,this.c,this.l+$t*(t??1),this.opacity)},darker(t){return new dt(this.h,this.c,this.l-$t*(t??1),this.opacity)},rgb(){return dn(this).rgb()}}));function yr(t){return function(e,n){var r=t((e=ye(e)).h,(n=ye(n)).h),i=re(e.c,n.c),a=re(e.l,n.l),s=re(e.opacity,n.opacity);return function(h){return e.h=r(h),e.c=i(h),e.l=a(h),e.opacity=s(h),e+""}}}const kr=yr(An);function pr(t,e){t=t.slice();var n=0,r=t.length-1,i=t[n],a=t[r],s;return a<i&&(s=n,n=r,r=s,s=i,i=a,a=s),t[n]=e.floor(i),t[r]=e.ceil(a),t}const le=new Date,ue=new Date;function et(t,e,n,r){function i(a){return t(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(t(a=new Date(+a)),a),i.ceil=a=>(t(a=new Date(a-1)),e(a,1),t(a),a),i.round=a=>{const s=i(a),h=i.ceil(a);return a-s<h-a?s:h},i.offset=(a,s)=>(e(a=new Date(+a),s==null?1:Math.floor(s)),a),i.range=(a,s,h)=>{const p=[];if(a=i.ceil(a),h=h==null?1:Math.floor(h),!(a<s)||!(h>0))return p;let d;do p.push(d=new Date(+a)),e(a,h),t(a);while(d<a&&a<s);return p},i.filter=a=>et(s=>{if(s>=s)for(;t(s),!a(s);)s.setTime(s-1)},(s,h)=>{if(s>=s)if(h<0)for(;++h<=0;)for(;e(s,-1),!a(s););else for(;--h>=0;)for(;e(s,1),!a(s););}),n&&(i.count=(a,s)=>(le.setTime(+a),ue.setTime(+s),t(le),t(ue),Math.floor(n(le,ue))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(r?s=>r(s)%a===0:s=>i.count(0,s)%a===0):i)),i}const Yt=et(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);Yt.every=t=>(t=Math.floor(t),!isFinite(t)||!(t>0)?null:t>1?et(e=>{e.setTime(Math.floor(e/t)*t)},(e,n)=>{e.setTime(+e+n*t)},(e,n)=>(n-e)/t):Yt);Yt.range;const mt=1e3,ct=mt*60,gt=ct*60,yt=gt*24,we=yt*7,Ne=yt*30,fe=yt*365,vt=et(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*mt)},(t,e)=>(e-t)/mt,t=>t.getUTCSeconds());vt.range;const Wt=et(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*mt)},(t,e)=>{t.setTime(+t+e*ct)},(t,e)=>(e-t)/ct,t=>t.getMinutes());Wt.range;const vr=et(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*ct)},(t,e)=>(e-t)/ct,t=>t.getUTCMinutes());vr.range;const Nt=et(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*mt-t.getMinutes()*ct)},(t,e)=>{t.setTime(+t+e*gt)},(t,e)=>(e-t)/gt,t=>t.getHours());Nt.range;const Tr=et(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*gt)},(t,e)=>(e-t)/gt,t=>t.getUTCHours());Tr.range;const Tt=et(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*ct)/yt,t=>t.getDate()-1);Tt.range;const Me=et(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/yt,t=>t.getUTCDate()-1);Me.range;const br=et(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/yt,t=>Math.floor(t/yt));br.range;function wt(t){return et(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(e,n)=>{e.setDate(e.getDate()+n*7)},(e,n)=>(n-e-(n.getTimezoneOffset()-e.getTimezoneOffset())*ct)/we)}const Vt=wt(0),Ht=wt(1),mn=wt(2),gn=wt(3),bt=wt(4),yn=wt(5),kn=wt(6);Vt.range;Ht.range;mn.range;gn.range;bt.range;yn.range;kn.range;function Mt(t){return et(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCDate(e.getUTCDate()+n*7)},(e,n)=>(n-e)/we)}const pn=Mt(0),Jt=Mt(1),xr=Mt(2),wr=Mt(3),Ut=Mt(4),Mr=Mt(5),Cr=Mt(6);pn.range;Jt.range;xr.range;wr.range;Ut.range;Mr.range;Cr.range;const Ot=et(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());Ot.range;const Dr=et(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());Dr.range;const kt=et(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());kt.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:et(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,n)=>{e.setFullYear(e.getFullYear()+n*t)});kt.range;const xt=et(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());xt.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:et(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCFullYear(e.getUTCFullYear()+n*t)});xt.range;function he(t,e){return t==null||e==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function _r(t,e){return t==null||e==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function Sr(t){let e,n,r;t.length!==2?(e=he,n=(h,p)=>he(t(h),p),r=(h,p)=>t(h)-p):(e=t===he||t===_r?t:Fr,n=t,r=t);function i(h,p,d=0,m=h.length){if(d<m){if(e(p,p)!==0)return m;do{const M=d+m>>>1;n(h[M],p)<0?d=M+1:m=M}while(d<m)}return d}function a(h,p,d=0,m=h.length){if(d<m){if(e(p,p)!==0)return m;do{const M=d+m>>>1;n(h[M],p)<=0?d=M+1:m=M}while(d<m)}return d}function s(h,p,d=0,m=h.length){const M=i(h,p,d,m-1);return M>d&&r(h[M-1],p)>-r(h[M],p)?M-1:M}return{left:i,center:s,right:a}}function Fr(){return 0}const Yr=Math.sqrt(50),Ur=Math.sqrt(10),Er=Math.sqrt(2);function vn(t,e,n){const r=(e-t)/Math.max(0,n),i=Math.floor(Math.log10(r)),a=r/Math.pow(10,i),s=a>=Yr?10:a>=Ur?5:a>=Er?2:1;let h,p,d;return i<0?(d=Math.pow(10,-i)/s,h=Math.round(t*d),p=Math.round(e*d),h/d<t&&++h,p/d>e&&--p,d=-d):(d=Math.pow(10,i)*s,h=Math.round(t/d),p=Math.round(e/d),h*d<t&&++h,p*d>e&&--p),p<h&&.5<=n&&n<2?vn(t,e,n*2):[h,p,d]}function He(t,e,n){return e=+e,t=+t,n=+n,vn(t,e,n)[2]}function Oe(t,e,n){e=+e,t=+t,n=+n;const r=e<t,i=r?He(e,t,n):He(t,e,n);return(r?-1:1)*(i<0?1/-i:i)}function Lr(t,e,n,r,i,a){const s=[[vt,1,mt],[vt,5,5*mt],[vt,15,15*mt],[vt,30,30*mt],[a,1,ct],[a,5,5*ct],[a,15,15*ct],[a,30,30*ct],[i,1,gt],[i,3,3*gt],[i,6,6*gt],[i,12,12*gt],[r,1,yt],[r,2,2*yt],[n,1,we],[e,1,Ne],[e,3,3*Ne],[t,1,fe]];function h(d,m,M){const D=m<d;D&&([d,m]=[m,d]);const x=M&&typeof M.range=="function"?M:p(d,m,M),G=x?x.range(d,+m+1):[];return D?G.reverse():G}function p(d,m,M){const D=Math.abs(m-d)/M,x=Sr(([,,_])=>_).right(s,D);if(x===s.length)return t.every(Oe(d/fe,m/fe,M));if(x===0)return Yt.every(Math.max(Oe(d,m,M),1));const[G,N]=s[D/s[x-1][2]<s[x][2]/D?x-1:x];return G.every(N)}return[h,p]}const[Ir,Ar]=Lr(kt,Ot,Vt,Tt,Nt,Wt);function de(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function me(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function Lt(t,e,n){return{y:t,m:e,d:n,H:0,M:0,S:0,L:0}}function Wr(t){var e=t.dateTime,n=t.date,r=t.time,i=t.periods,a=t.days,s=t.shortDays,h=t.months,p=t.shortMonths,d=It(i),m=At(i),M=It(a),D=At(a),x=It(s),G=At(s),N=It(h),_=At(h),A=It(p),V=At(p),W={a:k,A:E,b:c,B:l,c:null,d:Ze,e:Ze,f:ai,g:gi,G:ki,H:ni,I:ri,j:ii,L:Tn,m:si,M:oi,p:o,q:R,Q:Xe,s:Qe,S:ci,u:li,U:ui,V:fi,w:hi,W:di,x:null,X:null,y:mi,Y:yi,Z:pi,"%":Ge},Z={a:z,A:P,b:K,B:X,c:null,d:qe,e:qe,f:xi,g:Ei,G:Ii,H:vi,I:Ti,j:bi,L:xn,m:wi,M:Mi,p:$,q:at,Q:Xe,s:Qe,S:Ci,u:Di,U:_i,V:Si,w:Fi,W:Yi,x:null,X:null,y:Ui,Y:Li,Z:Ai,"%":Ge},j={a:F,A:S,b:T,B:U,c:u,d:Re,e:Re,f:Jr,g:Pe,G:ze,H:Be,I:Be,j:Xr,L:$r,m:Gr,M:Qr,p:Y,q:qr,Q:ti,s:ei,S:jr,u:zr,U:Pr,V:Rr,w:Vr,W:Br,x:g,X:v,y:Pe,Y:ze,Z:Zr,"%":Kr};W.x=C(n,W),W.X=C(r,W),W.c=C(e,W),Z.x=C(n,Z),Z.X=C(r,Z),Z.c=C(e,Z);function C(b,I){return function(O){var f=[],J=-1,L=0,Q=b.length,q,rt,st;for(O instanceof Date||(O=new Date(+O));++J<Q;)b.charCodeAt(J)===37&&(f.push(b.slice(L,J)),(rt=Ve[q=b.charAt(++J)])!=null?q=b.charAt(++J):rt=q==="e"?" ":"0",(st=I[q])&&(q=st(O,rt)),f.push(q),L=J+1);return f.push(b.slice(L,J)),f.join("")}}function H(b,I){return function(O){var f=Lt(1900,void 0,1),J=w(f,b,O+="",0),L,Q;if(J!=O.length)return null;if("Q"in f)return new Date(f.Q);if("s"in f)return new Date(f.s*1e3+("L"in f?f.L:0));if(I&&!("Z"in f)&&(f.Z=0),"p"in f&&(f.H=f.H%12+f.p*12),f.m===void 0&&(f.m="q"in f?f.q:0),"V"in f){if(f.V<1||f.V>53)return null;"w"in f||(f.w=1),"Z"in f?(L=me(Lt(f.y,0,1)),Q=L.getUTCDay(),L=Q>4||Q===0?Jt.ceil(L):Jt(L),L=Me.offset(L,(f.V-1)*7),f.y=L.getUTCFullYear(),f.m=L.getUTCMonth(),f.d=L.getUTCDate()+(f.w+6)%7):(L=de(Lt(f.y,0,1)),Q=L.getDay(),L=Q>4||Q===0?Ht.ceil(L):Ht(L),L=Tt.offset(L,(f.V-1)*7),f.y=L.getFullYear(),f.m=L.getMonth(),f.d=L.getDate()+(f.w+6)%7)}else("W"in f||"U"in f)&&("w"in f||(f.w="u"in f?f.u%7:"W"in f?1:0),Q="Z"in f?me(Lt(f.y,0,1)).getUTCDay():de(Lt(f.y,0,1)).getDay(),f.m=0,f.d="W"in f?(f.w+6)%7+f.W*7-(Q+5)%7:f.w+f.U*7-(Q+6)%7);return"Z"in f?(f.H+=f.Z/100|0,f.M+=f.Z%100,me(f)):de(f)}}function w(b,I,O,f){for(var J=0,L=I.length,Q=O.length,q,rt;J<L;){if(f>=Q)return-1;if(q=I.charCodeAt(J++),q===37){if(q=I.charAt(J++),rt=j[q in Ve?I.charAt(J++):q],!rt||(f=rt(b,O,f))<0)return-1}else if(q!=O.charCodeAt(f++))return-1}return f}function Y(b,I,O){var f=d.exec(I.slice(O));return f?(b.p=m.get(f[0].toLowerCase()),O+f[0].length):-1}function F(b,I,O){var f=x.exec(I.slice(O));return f?(b.w=G.get(f[0].toLowerCase()),O+f[0].length):-1}function S(b,I,O){var f=M.exec(I.slice(O));return f?(b.w=D.get(f[0].toLowerCase()),O+f[0].length):-1}function T(b,I,O){var f=A.exec(I.slice(O));return f?(b.m=V.get(f[0].toLowerCase()),O+f[0].length):-1}function U(b,I,O){var f=N.exec(I.slice(O));return f?(b.m=_.get(f[0].toLowerCase()),O+f[0].length):-1}function u(b,I,O){return w(b,e,I,O)}function g(b,I,O){return w(b,n,I,O)}function v(b,I,O){return w(b,r,I,O)}function k(b){return s[b.getDay()]}function E(b){return a[b.getDay()]}function c(b){return p[b.getMonth()]}function l(b){return h[b.getMonth()]}function o(b){return i[+(b.getHours()>=12)]}function R(b){return 1+~~(b.getMonth()/3)}function z(b){return s[b.getUTCDay()]}function P(b){return a[b.getUTCDay()]}function K(b){return p[b.getUTCMonth()]}function X(b){return h[b.getUTCMonth()]}function $(b){return i[+(b.getUTCHours()>=12)]}function at(b){return 1+~~(b.getUTCMonth()/3)}return{format:function(b){var I=C(b+="",W);return I.toString=function(){return b},I},parse:function(b){var I=H(b+="",!1);return I.toString=function(){return b},I},utcFormat:function(b){var I=C(b+="",Z);return I.toString=function(){return b},I},utcParse:function(b){var I=H(b+="",!0);return I.toString=function(){return b},I}}}var Ve={"-":"",_:" ",0:"0"},nt=/^\s*\d+/,Nr=/^%/,Hr=/[\\^$*+?|[\]().{}]/g;function B(t,e,n){var r=t<0?"-":"",i=(r?-t:t)+"",a=i.length;return r+(a<n?new Array(n-a+1).join(e)+i:i)}function Or(t){return t.replace(Hr,"\\$&")}function It(t){return new RegExp("^(?:"+t.map(Or).join("|")+")","i")}function At(t){return new Map(t.map((e,n)=>[e.toLowerCase(),n]))}function Vr(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.w=+r[0],n+r[0].length):-1}function zr(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.u=+r[0],n+r[0].length):-1}function Pr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.U=+r[0],n+r[0].length):-1}function Rr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.V=+r[0],n+r[0].length):-1}function Br(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.W=+r[0],n+r[0].length):-1}function ze(t,e,n){var r=nt.exec(e.slice(n,n+4));return r?(t.y=+r[0],n+r[0].length):-1}function Pe(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function Zr(t,e,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(n,n+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function qr(t,e,n){var r=nt.exec(e.slice(n,n+1));return r?(t.q=r[0]*3-3,n+r[0].length):-1}function Gr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.m=r[0]-1,n+r[0].length):-1}function Re(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.d=+r[0],n+r[0].length):-1}function Xr(t,e,n){var r=nt.exec(e.slice(n,n+3));return r?(t.m=0,t.d=+r[0],n+r[0].length):-1}function Be(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.H=+r[0],n+r[0].length):-1}function Qr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.M=+r[0],n+r[0].length):-1}function jr(t,e,n){var r=nt.exec(e.slice(n,n+2));return r?(t.S=+r[0],n+r[0].length):-1}function $r(t,e,n){var r=nt.exec(e.slice(n,n+3));return r?(t.L=+r[0],n+r[0].length):-1}function Jr(t,e,n){var r=nt.exec(e.slice(n,n+6));return r?(t.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function Kr(t,e,n){var r=Nr.exec(e.slice(n,n+1));return r?n+r[0].length:-1}function ti(t,e,n){var r=nt.exec(e.slice(n));return r?(t.Q=+r[0],n+r[0].length):-1}function ei(t,e,n){var r=nt.exec(e.slice(n));return r?(t.s=+r[0],n+r[0].length):-1}function Ze(t,e){return B(t.getDate(),e,2)}function ni(t,e){return B(t.getHours(),e,2)}function ri(t,e){return B(t.getHours()%12||12,e,2)}function ii(t,e){return B(1+Tt.count(kt(t),t),e,3)}function Tn(t,e){return B(t.getMilliseconds(),e,3)}function ai(t,e){return Tn(t,e)+"000"}function si(t,e){return B(t.getMonth()+1,e,2)}function oi(t,e){return B(t.getMinutes(),e,2)}function ci(t,e){return B(t.getSeconds(),e,2)}function li(t){var e=t.getDay();return e===0?7:e}function ui(t,e){return B(Vt.count(kt(t)-1,t),e,2)}function bn(t){var e=t.getDay();return e>=4||e===0?bt(t):bt.ceil(t)}function fi(t,e){return t=bn(t),B(bt.count(kt(t),t)+(kt(t).getDay()===4),e,2)}function hi(t){return t.getDay()}function di(t,e){return B(Ht.count(kt(t)-1,t),e,2)}function mi(t,e){return B(t.getFullYear()%100,e,2)}function gi(t,e){return t=bn(t),B(t.getFullYear()%100,e,2)}function yi(t,e){return B(t.getFullYear()%1e4,e,4)}function ki(t,e){var n=t.getDay();return t=n>=4||n===0?bt(t):bt.ceil(t),B(t.getFullYear()%1e4,e,4)}function pi(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+B(e/60|0,"0",2)+B(e%60,"0",2)}function qe(t,e){return B(t.getUTCDate(),e,2)}function vi(t,e){return B(t.getUTCHours(),e,2)}function Ti(t,e){return B(t.getUTCHours()%12||12,e,2)}function bi(t,e){return B(1+Me.count(xt(t),t),e,3)}function xn(t,e){return B(t.getUTCMilliseconds(),e,3)}function xi(t,e){return xn(t,e)+"000"}function wi(t,e){return B(t.getUTCMonth()+1,e,2)}function Mi(t,e){return B(t.getUTCMinutes(),e,2)}function Ci(t,e){return B(t.getUTCSeconds(),e,2)}function Di(t){var e=t.getUTCDay();return e===0?7:e}function _i(t,e){return B(pn.count(xt(t)-1,t),e,2)}function wn(t){var e=t.getUTCDay();return e>=4||e===0?Ut(t):Ut.ceil(t)}function Si(t,e){return t=wn(t),B(Ut.count(xt(t),t)+(xt(t).getUTCDay()===4),e,2)}function Fi(t){return t.getUTCDay()}function Yi(t,e){return B(Jt.count(xt(t)-1,t),e,2)}function Ui(t,e){return B(t.getUTCFullYear()%100,e,2)}function Ei(t,e){return t=wn(t),B(t.getUTCFullYear()%100,e,2)}function Li(t,e){return B(t.getUTCFullYear()%1e4,e,4)}function Ii(t,e){var n=t.getUTCDay();return t=n>=4||n===0?Ut(t):Ut.ceil(t),B(t.getUTCFullYear()%1e4,e,4)}function Ai(){return"+0000"}function Ge(){return"%"}function Xe(t){return+t}function Qe(t){return Math.floor(+t/1e3)}var Dt,Kt;Wi({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function Wi(t){return Dt=Wr(t),Kt=Dt.format,Dt.parse,Dt.utcFormat,Dt.utcParse,Dt}function Ni(t){return new Date(t)}function Hi(t){return t instanceof Date?+t:+new Date(+t)}function Mn(t,e,n,r,i,a,s,h,p,d){var m=Gn(),M=m.invert,D=m.domain,x=d(".%L"),G=d(":%S"),N=d("%I:%M"),_=d("%I %p"),A=d("%a %d"),V=d("%b %d"),W=d("%B"),Z=d("%Y");function j(C){return(p(C)<C?x:h(C)<C?G:s(C)<C?N:a(C)<C?_:r(C)<C?i(C)<C?A:V:n(C)<C?W:Z)(C)}return m.invert=function(C){return new Date(M(C))},m.domain=function(C){return arguments.length?D(Array.from(C,Hi)):D().map(Ni)},m.ticks=function(C){var H=D();return t(H[0],H[H.length-1],C??10)},m.tickFormat=function(C,H){return H==null?j:d(H)},m.nice=function(C){var H=D();return(!C||typeof C.range!="function")&&(C=e(H[0],H[H.length-1],C??10)),C?D(pr(H,C)):m},m.copy=function(){return Xn(m,Mn(t,e,n,r,i,a,s,h,p,d))},m}function Oi(){return jn.apply(Mn(Ir,Ar,kt,Ot,Vt,Tt,Nt,Wt,vt,Kt).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}var Cn={exports:{}};(function(t,e){(function(n,r){t.exports=r()})(xe,function(){var n="day";return function(r,i,a){var s=function(d){return d.add(4-d.isoWeekday(),n)},h=i.prototype;h.isoWeekYear=function(){return s(this).year()},h.isoWeek=function(d){if(!this.$utils().u(d))return this.add(7*(d-this.isoWeek()),n);var m,M,D,x,G=s(this),N=(m=this.isoWeekYear(),M=this.$u,D=(M?a.utc:a)().year(m).startOf("year"),x=4-D.isoWeekday(),D.isoWeekday()>4&&(x+=7),D.add(x,n));return G.diff(N,"week")+1},h.isoWeekday=function(d){return this.$utils().u(d)?this.day()||7:this.day(this.day()%7?d:d-7)};var p=h.startOf;h.startOf=function(d,m){var M=this.$utils(),D=!!M.u(m)||m;return M.p(d)==="isoweek"?D?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):p.bind(this)(d,m)}}})})(Cn);var Vi=Cn.exports;const zi=be(Vi);var ke=function(){var t=y(function(U,u,g,v){for(g=g||{},v=U.length;v--;g[U[v]]=u);return g},"o"),e=[6,8,10,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28,29,30,31,33,35,36,38,40],n=[1,26],r=[1,27],i=[1,28],a=[1,29],s=[1,30],h=[1,31],p=[1,32],d=[1,33],m=[1,34],M=[1,9],D=[1,10],x=[1,11],G=[1,12],N=[1,13],_=[1,14],A=[1,15],V=[1,16],W=[1,19],Z=[1,20],j=[1,21],C=[1,22],H=[1,23],w=[1,25],Y=[1,35],F={trace:y(function(){},"trace"),yy:{},symbols_:{error:2,start:3,gantt:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NL:10,weekday:11,weekday_monday:12,weekday_tuesday:13,weekday_wednesday:14,weekday_thursday:15,weekday_friday:16,weekday_saturday:17,weekday_sunday:18,weekend:19,weekend_friday:20,weekend_saturday:21,dateFormat:22,inclusiveEndDates:23,topAxis:24,axisFormat:25,tickInterval:26,excludes:27,includes:28,todayMarker:29,title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,section:36,clickStatement:37,taskTxt:38,taskData:39,click:40,callbackname:41,callbackargs:42,href:43,clickStatementDebug:44,$accept:0,$end:1},terminals_:{2:"error",4:"gantt",6:"EOF",8:"SPACE",10:"NL",12:"weekday_monday",13:"weekday_tuesday",14:"weekday_wednesday",15:"weekday_thursday",16:"weekday_friday",17:"weekday_saturday",18:"weekday_sunday",20:"weekend_friday",21:"weekend_saturday",22:"dateFormat",23:"inclusiveEndDates",24:"topAxis",25:"axisFormat",26:"tickInterval",27:"excludes",28:"includes",29:"todayMarker",30:"title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"section",38:"taskTxt",39:"taskData",40:"click",41:"callbackname",42:"callbackargs",43:"href"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[19,1],[19,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,2],[37,2],[37,3],[37,3],[37,4],[37,3],[37,4],[37,2],[44,2],[44,3],[44,3],[44,4],[44,3],[44,4],[44,2]],performAction:y(function(u,g,v,k,E,c,l){var o=c.length-1;switch(E){case 1:return c[o-1];case 2:this.$=[];break;case 3:c[o-1].push(c[o]),this.$=c[o-1];break;case 4:case 5:this.$=c[o];break;case 6:case 7:this.$=[];break;case 8:k.setWeekday("monday");break;case 9:k.setWeekday("tuesday");break;case 10:k.setWeekday("wednesday");break;case 11:k.setWeekday("thursday");break;case 12:k.setWeekday("friday");break;case 13:k.setWeekday("saturday");break;case 14:k.setWeekday("sunday");break;case 15:k.setWeekend("friday");break;case 16:k.setWeekend("saturday");break;case 17:k.setDateFormat(c[o].substr(11)),this.$=c[o].substr(11);break;case 18:k.enableInclusiveEndDates(),this.$=c[o].substr(18);break;case 19:k.TopAxis(),this.$=c[o].substr(8);break;case 20:k.setAxisFormat(c[o].substr(11)),this.$=c[o].substr(11);break;case 21:k.setTickInterval(c[o].substr(13)),this.$=c[o].substr(13);break;case 22:k.setExcludes(c[o].substr(9)),this.$=c[o].substr(9);break;case 23:k.setIncludes(c[o].substr(9)),this.$=c[o].substr(9);break;case 24:k.setTodayMarker(c[o].substr(12)),this.$=c[o].substr(12);break;case 27:k.setDiagramTitle(c[o].substr(6)),this.$=c[o].substr(6);break;case 28:this.$=c[o].trim(),k.setAccTitle(this.$);break;case 29:case 30:this.$=c[o].trim(),k.setAccDescription(this.$);break;case 31:k.addSection(c[o].substr(8)),this.$=c[o].substr(8);break;case 33:k.addTask(c[o-1],c[o]),this.$="task";break;case 34:this.$=c[o-1],k.setClickEvent(c[o-1],c[o],null);break;case 35:this.$=c[o-2],k.setClickEvent(c[o-2],c[o-1],c[o]);break;case 36:this.$=c[o-2],k.setClickEvent(c[o-2],c[o-1],null),k.setLink(c[o-2],c[o]);break;case 37:this.$=c[o-3],k.setClickEvent(c[o-3],c[o-2],c[o-1]),k.setLink(c[o-3],c[o]);break;case 38:this.$=c[o-2],k.setClickEvent(c[o-2],c[o],null),k.setLink(c[o-2],c[o-1]);break;case 39:this.$=c[o-3],k.setClickEvent(c[o-3],c[o-1],c[o]),k.setLink(c[o-3],c[o-2]);break;case 40:this.$=c[o-1],k.setLink(c[o-1],c[o]);break;case 41:case 47:this.$=c[o-1]+" "+c[o];break;case 42:case 43:case 45:this.$=c[o-2]+" "+c[o-1]+" "+c[o];break;case 44:case 46:this.$=c[o-3]+" "+c[o-2]+" "+c[o-1]+" "+c[o];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},t(e,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:17,12:n,13:r,14:i,15:a,16:s,17:h,18:p,19:18,20:d,21:m,22:M,23:D,24:x,25:G,26:N,27:_,28:A,29:V,30:W,31:Z,33:j,35:C,36:H,37:24,38:w,40:Y},t(e,[2,7],{1:[2,1]}),t(e,[2,3]),{9:36,11:17,12:n,13:r,14:i,15:a,16:s,17:h,18:p,19:18,20:d,21:m,22:M,23:D,24:x,25:G,26:N,27:_,28:A,29:V,30:W,31:Z,33:j,35:C,36:H,37:24,38:w,40:Y},t(e,[2,5]),t(e,[2,6]),t(e,[2,17]),t(e,[2,18]),t(e,[2,19]),t(e,[2,20]),t(e,[2,21]),t(e,[2,22]),t(e,[2,23]),t(e,[2,24]),t(e,[2,25]),t(e,[2,26]),t(e,[2,27]),{32:[1,37]},{34:[1,38]},t(e,[2,30]),t(e,[2,31]),t(e,[2,32]),{39:[1,39]},t(e,[2,8]),t(e,[2,9]),t(e,[2,10]),t(e,[2,11]),t(e,[2,12]),t(e,[2,13]),t(e,[2,14]),t(e,[2,15]),t(e,[2,16]),{41:[1,40],43:[1,41]},t(e,[2,4]),t(e,[2,28]),t(e,[2,29]),t(e,[2,33]),t(e,[2,34],{42:[1,42],43:[1,43]}),t(e,[2,40],{41:[1,44]}),t(e,[2,35],{43:[1,45]}),t(e,[2,36]),t(e,[2,38],{42:[1,46]}),t(e,[2,37]),t(e,[2,39])],defaultActions:{},parseError:y(function(u,g){if(g.recoverable)this.trace(u);else{var v=new Error(u);throw v.hash=g,v}},"parseError"),parse:y(function(u){var g=this,v=[0],k=[],E=[null],c=[],l=this.table,o="",R=0,z=0,P=2,K=1,X=c.slice.call(arguments,1),$=Object.create(this.lexer),at={yy:{}};for(var b in this.yy)Object.prototype.hasOwnProperty.call(this.yy,b)&&(at.yy[b]=this.yy[b]);$.setInput(u,at.yy),at.yy.lexer=$,at.yy.parser=this,typeof $.yylloc>"u"&&($.yylloc={});var I=$.yylloc;c.push(I);var O=$.options&&$.options.ranges;typeof at.yy.parseError=="function"?this.parseError=at.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function f(ot){v.length=v.length-2*ot,E.length=E.length-ot,c.length=c.length-ot}y(f,"popStack");function J(){var ot;return ot=k.pop()||$.lex()||K,typeof ot!="number"&&(ot instanceof Array&&(k=ot,ot=k.pop()),ot=g.symbols_[ot]||ot),ot}y(J,"lex");for(var L,Q,q,rt,st={},pt,lt,Ae,Bt;;){if(Q=v[v.length-1],this.defaultActions[Q]?q=this.defaultActions[Q]:((L===null||typeof L>"u")&&(L=J()),q=l[Q]&&l[Q][L]),typeof q>"u"||!q.length||!q[0]){var ne="";Bt=[];for(pt in l[Q])this.terminals_[pt]&&pt>P&&Bt.push("'"+this.terminals_[pt]+"'");$.showPosition?ne="Parse error on line "+(R+1)+`:
`+$.showPosition()+`
Expecting `+Bt.join(", ")+", got '"+(this.terminals_[L]||L)+"'":ne="Parse error on line "+(R+1)+": Unexpected "+(L==K?"end of input":"'"+(this.terminals_[L]||L)+"'"),this.parseError(ne,{text:$.match,token:this.terminals_[L]||L,line:$.yylineno,loc:I,expected:Bt})}if(q[0]instanceof Array&&q.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Q+", token: "+L);switch(q[0]){case 1:v.push(L),E.push($.yytext),c.push($.yylloc),v.push(q[1]),L=null,z=$.yyleng,o=$.yytext,R=$.yylineno,I=$.yylloc;break;case 2:if(lt=this.productions_[q[1]][1],st.$=E[E.length-lt],st._$={first_line:c[c.length-(lt||1)].first_line,last_line:c[c.length-1].last_line,first_column:c[c.length-(lt||1)].first_column,last_column:c[c.length-1].last_column},O&&(st._$.range=[c[c.length-(lt||1)].range[0],c[c.length-1].range[1]]),rt=this.performAction.apply(st,[o,z,R,at.yy,q[1],E,c].concat(X)),typeof rt<"u")return rt;lt&&(v=v.slice(0,-1*lt*2),E=E.slice(0,-1*lt),c=c.slice(0,-1*lt)),v.push(this.productions_[q[1]][0]),E.push(st.$),c.push(st._$),Ae=l[v[v.length-2]][v[v.length-1]],v.push(Ae);break;case 3:return!0}}return!0},"parse")},S=function(){var U={EOF:1,parseError:y(function(g,v){if(this.yy.parser)this.yy.parser.parseError(g,v);else throw new Error(g)},"parseError"),setInput:y(function(u,g){return this.yy=g||this.yy||{},this._input=u,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:y(function(){var u=this._input[0];this.yytext+=u,this.yyleng++,this.offset++,this.match+=u,this.matched+=u;var g=u.match(/(?:\r\n?|\n).*/g);return g?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),u},"input"),unput:y(function(u){var g=u.length,v=u.split(/(?:\r\n?|\n)/g);this._input=u+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-g),this.offset-=g;var k=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),v.length-1&&(this.yylineno-=v.length-1);var E=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:v?(v.length===k.length?this.yylloc.first_column:0)+k[k.length-v.length].length-v[0].length:this.yylloc.first_column-g},this.options.ranges&&(this.yylloc.range=[E[0],E[0]+this.yyleng-g]),this.yyleng=this.yytext.length,this},"unput"),more:y(function(){return this._more=!0,this},"more"),reject:y(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:y(function(u){this.unput(this.match.slice(u))},"less"),pastInput:y(function(){var u=this.matched.substr(0,this.matched.length-this.match.length);return(u.length>20?"...":"")+u.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:y(function(){var u=this.match;return u.length<20&&(u+=this._input.substr(0,20-u.length)),(u.substr(0,20)+(u.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:y(function(){var u=this.pastInput(),g=new Array(u.length+1).join("-");return u+this.upcomingInput()+`
`+g+"^"},"showPosition"),test_match:y(function(u,g){var v,k,E;if(this.options.backtrack_lexer&&(E={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(E.yylloc.range=this.yylloc.range.slice(0))),k=u[0].match(/(?:\r\n?|\n).*/g),k&&(this.yylineno+=k.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:k?k[k.length-1].length-k[k.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+u[0].length},this.yytext+=u[0],this.match+=u[0],this.matches=u,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(u[0].length),this.matched+=u[0],v=this.performAction.call(this,this.yy,this,g,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),v)return v;if(this._backtrack){for(var c in E)this[c]=E[c];return!1}return!1},"test_match"),next:y(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var u,g,v,k;this._more||(this.yytext="",this.match="");for(var E=this._currentRules(),c=0;c<E.length;c++)if(v=this._input.match(this.rules[E[c]]),v&&(!g||v[0].length>g[0].length)){if(g=v,k=c,this.options.backtrack_lexer){if(u=this.test_match(v,E[c]),u!==!1)return u;if(this._backtrack){g=!1;continue}else return!1}else if(!this.options.flex)break}return g?(u=this.test_match(g,E[k]),u!==!1?u:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:y(function(){var g=this.next();return g||this.lex()},"lex"),begin:y(function(g){this.conditionStack.push(g)},"begin"),popState:y(function(){var g=this.conditionStack.length-1;return g>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:y(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:y(function(g){return g=this.conditionStack.length-1-Math.abs(g||0),g>=0?this.conditionStack[g]:"INITIAL"},"topState"),pushState:y(function(g){this.begin(g)},"pushState"),stateStackSize:y(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:y(function(g,v,k,E){switch(k){case 0:return this.begin("open_directive"),"open_directive";case 1:return this.begin("acc_title"),31;case 2:return this.popState(),"acc_title_value";case 3:return this.begin("acc_descr"),33;case 4:return this.popState(),"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:break;case 9:break;case 10:break;case 11:return 10;case 12:break;case 13:break;case 14:this.begin("href");break;case 15:this.popState();break;case 16:return 43;case 17:this.begin("callbackname");break;case 18:this.popState();break;case 19:this.popState(),this.begin("callbackargs");break;case 20:return 41;case 21:this.popState();break;case 22:return 42;case 23:this.begin("click");break;case 24:this.popState();break;case 25:return 40;case 26:return 4;case 27:return 22;case 28:return 23;case 29:return 24;case 30:return 25;case 31:return 26;case 32:return 28;case 33:return 27;case 34:return 29;case 35:return 12;case 36:return 13;case 37:return 14;case 38:return 15;case 39:return 16;case 40:return 17;case 41:return 18;case 42:return 20;case 43:return 21;case 44:return"date";case 45:return 30;case 46:return"accDescription";case 47:return 36;case 48:return 38;case 49:return 39;case 50:return":";case 51:return 6;case 52:return"INVALID"}},"anonymous"),rules:[/^(?:%%\{)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:%%(?!\{)*[^\n]*)/i,/^(?:[^\}]%%*[^\n]*)/i,/^(?:%%*[^\n]*[\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:%[^\n]*)/i,/^(?:href[\s]+["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:call[\s]+)/i,/^(?:\([\s]*\))/i,/^(?:\()/i,/^(?:[^(]*)/i,/^(?:\))/i,/^(?:[^)]*)/i,/^(?:click[\s]+)/i,/^(?:[\s\n])/i,/^(?:[^\s\n]*)/i,/^(?:gantt\b)/i,/^(?:dateFormat\s[^#\n;]+)/i,/^(?:inclusiveEndDates\b)/i,/^(?:topAxis\b)/i,/^(?:axisFormat\s[^#\n;]+)/i,/^(?:tickInterval\s[^#\n;]+)/i,/^(?:includes\s[^#\n;]+)/i,/^(?:excludes\s[^#\n;]+)/i,/^(?:todayMarker\s[^\n;]+)/i,/^(?:weekday\s+monday\b)/i,/^(?:weekday\s+tuesday\b)/i,/^(?:weekday\s+wednesday\b)/i,/^(?:weekday\s+thursday\b)/i,/^(?:weekday\s+friday\b)/i,/^(?:weekday\s+saturday\b)/i,/^(?:weekday\s+sunday\b)/i,/^(?:weekend\s+friday\b)/i,/^(?:weekend\s+saturday\b)/i,/^(?:\d\d\d\d-\d\d-\d\d\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accDescription\s[^#\n;]+)/i,/^(?:section\s[^\n]+)/i,/^(?:[^:\n]+)/i,/^(?::[^#\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[6,7],inclusive:!1},acc_descr:{rules:[4],inclusive:!1},acc_title:{rules:[2],inclusive:!1},callbackargs:{rules:[21,22],inclusive:!1},callbackname:{rules:[18,19,20],inclusive:!1},href:{rules:[15,16],inclusive:!1},click:{rules:[24,25],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,17,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],inclusive:!0}}};return U}();F.lexer=S;function T(){this.yy={}}return y(T,"Parser"),T.prototype=F,F.Parser=T,new T}();ke.parser=ke;var Pi=ke;it.extend(zi);it.extend(tr);it.extend(Jn);var je={friday:5,saturday:6},ut="",Ce="",De=void 0,_e="",zt=[],Pt=[],Se=new Map,Fe=[],te=[],Et="",Ye="",Dn=["active","done","crit","milestone","vert"],Ue=[],Rt=!1,Ee=!1,Le="sunday",ee="saturday",pe=0,Ri=y(function(){Fe=[],te=[],Et="",Ue=[],Xt=0,Te=void 0,Qt=void 0,tt=[],ut="",Ce="",Ye="",De=void 0,_e="",zt=[],Pt=[],Rt=!1,Ee=!1,pe=0,Se=new Map,Zn(),Le="sunday",ee="saturday"},"clear"),Bi=y(function(t){Ce=t},"setAxisFormat"),Zi=y(function(){return Ce},"getAxisFormat"),qi=y(function(t){De=t},"setTickInterval"),Gi=y(function(){return De},"getTickInterval"),Xi=y(function(t){_e=t},"setTodayMarker"),Qi=y(function(){return _e},"getTodayMarker"),ji=y(function(t){ut=t},"setDateFormat"),$i=y(function(){Rt=!0},"enableInclusiveEndDates"),Ji=y(function(){return Rt},"endDatesAreInclusive"),Ki=y(function(){Ee=!0},"enableTopAxis"),ta=y(function(){return Ee},"topAxisEnabled"),ea=y(function(t){Ye=t},"setDisplayMode"),na=y(function(){return Ye},"getDisplayMode"),ra=y(function(){return ut},"getDateFormat"),ia=y(function(t){zt=t.toLowerCase().split(/[\s,]+/)},"setIncludes"),aa=y(function(){return zt},"getIncludes"),sa=y(function(t){Pt=t.toLowerCase().split(/[\s,]+/)},"setExcludes"),oa=y(function(){return Pt},"getExcludes"),ca=y(function(){return Se},"getLinks"),la=y(function(t){Et=t,Fe.push(t)},"addSection"),ua=y(function(){return Fe},"getSections"),fa=y(function(){let t=$e();const e=10;let n=0;for(;!t&&n<e;)t=$e(),n++;return te=tt,te},"getTasks"),_n=y(function(t,e,n,r){return r.includes(t.format(e.trim()))?!1:n.includes("weekends")&&(t.isoWeekday()===je[ee]||t.isoWeekday()===je[ee]+1)||n.includes(t.format("dddd").toLowerCase())?!0:n.includes(t.format(e.trim()))},"isInvalidDate"),ha=y(function(t){Le=t},"setWeekday"),da=y(function(){return Le},"getWeekday"),ma=y(function(t){ee=t},"setWeekend"),Sn=y(function(t,e,n,r){if(!n.length||t.manualEndTime)return;let i;t.startTime instanceof Date?i=it(t.startTime):i=it(t.startTime,e,!0),i=i.add(1,"d");let a;t.endTime instanceof Date?a=it(t.endTime):a=it(t.endTime,e,!0);const[s,h]=ga(i,a,e,n,r);t.endTime=s.toDate(),t.renderEndTime=h},"checkTaskDates"),ga=y(function(t,e,n,r,i){let a=!1,s=null;for(;t<=e;)a||(s=e.toDate()),a=_n(t,n,r,i),a&&(e=e.add(1,"d")),t=t.add(1,"d");return[e,s]},"fixTaskDates"),ve=y(function(t,e,n){n=n.trim();const i=/^after\s+(?<ids>[\d\w- ]+)/.exec(n);if(i!==null){let s=null;for(const p of i.groups.ids.split(" ")){let d=Ct(p);d!==void 0&&(!s||d.endTime>s.endTime)&&(s=d)}if(s)return s.endTime;const h=new Date;return h.setHours(0,0,0,0),h}let a=it(n,e.trim(),!0);if(a.isValid())return a.toDate();{jt.debug("Invalid date:"+n),jt.debug("With date format:"+e.trim());const s=new Date(n);if(s===void 0||isNaN(s.getTime())||s.getFullYear()<-1e4||s.getFullYear()>1e4)throw new Error("Invalid date:"+n);return s}},"getStartDate"),Fn=y(function(t){const e=/^(\d+(?:\.\d+)?)([Mdhmswy]|ms)$/.exec(t.trim());return e!==null?[Number.parseFloat(e[1]),e[2]]:[NaN,"ms"]},"parseDuration"),Yn=y(function(t,e,n,r=!1){n=n.trim();const a=/^until\s+(?<ids>[\d\w- ]+)/.exec(n);if(a!==null){let m=null;for(const D of a.groups.ids.split(" ")){let x=Ct(D);x!==void 0&&(!m||x.startTime<m.startTime)&&(m=x)}if(m)return m.startTime;const M=new Date;return M.setHours(0,0,0,0),M}let s=it(n,e.trim(),!0);if(s.isValid())return r&&(s=s.add(1,"d")),s.toDate();let h=it(t);const[p,d]=Fn(n);if(!Number.isNaN(p)){const m=h.add(p,d);m.isValid()&&(h=m)}return h.toDate()},"getEndDate"),Xt=0,Ft=y(function(t){return t===void 0?(Xt=Xt+1,"task"+Xt):t},"parseId"),ya=y(function(t,e){let n;e.substr(0,1)===":"?n=e.substr(1,e.length):n=e;const r=n.split(","),i={};Ie(r,i,Dn);for(let s=0;s<r.length;s++)r[s]=r[s].trim();let a="";switch(r.length){case 1:i.id=Ft(),i.startTime=t.endTime,a=r[0];break;case 2:i.id=Ft(),i.startTime=ve(void 0,ut,r[0]),a=r[1];break;case 3:i.id=Ft(r[0]),i.startTime=ve(void 0,ut,r[1]),a=r[2];break}return a&&(i.endTime=Yn(i.startTime,ut,a,Rt),i.manualEndTime=it(a,"YYYY-MM-DD",!0).isValid(),Sn(i,ut,Pt,zt)),i},"compileData"),ka=y(function(t,e){let n;e.substr(0,1)===":"?n=e.substr(1,e.length):n=e;const r=n.split(","),i={};Ie(r,i,Dn);for(let a=0;a<r.length;a++)r[a]=r[a].trim();switch(r.length){case 1:i.id=Ft(),i.startTime={type:"prevTaskEnd",id:t},i.endTime={data:r[0]};break;case 2:i.id=Ft(),i.startTime={type:"getStartDate",startData:r[0]},i.endTime={data:r[1]};break;case 3:i.id=Ft(r[0]),i.startTime={type:"getStartDate",startData:r[1]},i.endTime={data:r[2]};break}return i},"parseData"),Te,Qt,tt=[],Un={},pa=y(function(t,e){const n={section:Et,type:Et,processed:!1,manualEndTime:!1,renderEndTime:null,raw:{data:e},task:t,classes:[]},r=ka(Qt,e);n.raw.startTime=r.startTime,n.raw.endTime=r.endTime,n.id=r.id,n.prevTaskId=Qt,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,n.vert=r.vert,n.order=pe,pe++;const i=tt.push(n);Qt=n.id,Un[n.id]=i-1},"addTask"),Ct=y(function(t){const e=Un[t];return tt[e]},"findTaskById"),va=y(function(t,e){const n={section:Et,type:Et,description:t,task:t,classes:[]},r=ya(Te,e);n.startTime=r.startTime,n.endTime=r.endTime,n.id=r.id,n.active=r.active,n.done=r.done,n.crit=r.crit,n.milestone=r.milestone,n.vert=r.vert,Te=n,te.push(n)},"addTaskOrg"),$e=y(function(){const t=y(function(n){const r=tt[n];let i="";switch(tt[n].raw.startTime.type){case"prevTaskEnd":{const a=Ct(r.prevTaskId);r.startTime=a.endTime;break}case"getStartDate":i=ve(void 0,ut,tt[n].raw.startTime.startData),i&&(tt[n].startTime=i);break}return tt[n].startTime&&(tt[n].endTime=Yn(tt[n].startTime,ut,tt[n].raw.endTime.data,Rt),tt[n].endTime&&(tt[n].processed=!0,tt[n].manualEndTime=it(tt[n].raw.endTime.data,"YYYY-MM-DD",!0).isValid(),Sn(tt[n],ut,Pt,zt))),tt[n].processed},"compileTask");let e=!0;for(const[n,r]of tt.entries())t(n),e=e&&r.processed;return e},"compileTasks"),Ta=y(function(t,e){let n=e;_t().securityLevel!=="loose"&&(n=Bn(e)),t.split(",").forEach(function(r){Ct(r)!==void 0&&(Ln(r,()=>{window.open(n,"_self")}),Se.set(r,n))}),En(t,"clickable")},"setLink"),En=y(function(t,e){t.split(",").forEach(function(n){let r=Ct(n);r!==void 0&&r.classes.push(e)})},"setClass"),ba=y(function(t,e,n){if(_t().securityLevel!=="loose"||e===void 0)return;let r=[];if(typeof n=="string"){r=n.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let a=0;a<r.length;a++){let s=r[a].trim();s.startsWith('"')&&s.endsWith('"')&&(s=s.substr(1,s.length-2)),r[a]=s}}r.length===0&&r.push(t),Ct(t)!==void 0&&Ln(t,()=>{qn.runFunc(e,...r)})},"setClickFun"),Ln=y(function(t,e){Ue.push(function(){const n=document.querySelector(`[id="${t}"]`);n!==null&&n.addEventListener("click",function(){e()})},function(){const n=document.querySelector(`[id="${t}-text"]`);n!==null&&n.addEventListener("click",function(){e()})})},"pushFun"),xa=y(function(t,e,n){t.split(",").forEach(function(r){ba(r,e,n)}),En(t,"clickable")},"setClickEvent"),wa=y(function(t){Ue.forEach(function(e){e(t)})},"bindFunctions"),Ma={getConfig:y(()=>_t().gantt,"getConfig"),clear:Ri,setDateFormat:ji,getDateFormat:ra,enableInclusiveEndDates:$i,endDatesAreInclusive:Ji,enableTopAxis:Ki,topAxisEnabled:ta,setAxisFormat:Bi,getAxisFormat:Zi,setTickInterval:qi,getTickInterval:Gi,setTodayMarker:Xi,getTodayMarker:Qi,setAccTitle:zn,getAccTitle:Vn,setDiagramTitle:On,getDiagramTitle:Hn,setDisplayMode:ea,getDisplayMode:na,setAccDescription:Nn,getAccDescription:Wn,addSection:la,getSections:ua,getTasks:fa,addTask:pa,findTaskById:Ct,addTaskOrg:va,setIncludes:ia,getIncludes:aa,setExcludes:sa,getExcludes:oa,setClickEvent:xa,setLink:Ta,getLinks:ca,bindFunctions:wa,parseDuration:Fn,isInvalidDate:_n,setWeekday:ha,getWeekday:da,setWeekend:ma};function Ie(t,e,n){let r=!0;for(;r;)r=!1,n.forEach(function(i){const a="^\\s*"+i+"\\s*$",s=new RegExp(a);t[0].match(s)&&(e[i]=!0,t.shift(1),r=!0)})}y(Ie,"getTaskTags");var Ca=y(function(){jt.debug("Something is calling, setConf, remove the call")},"setConf"),Je={monday:Ht,tuesday:mn,wednesday:gn,thursday:bt,friday:yn,saturday:kn,sunday:Vt},Da=y((t,e)=>{let n=[...t].map(()=>-1/0),r=[...t].sort((a,s)=>a.startTime-s.startTime||a.order-s.order),i=0;for(const a of r)for(let s=0;s<n.length;s++)if(a.startTime>=n[s]){n[s]=a.endTime,a.order=s+e,s>i&&(i=s);break}return i},"getMaxIntersections"),ht,_a=y(function(t,e,n,r){const i=_t().gantt,a=_t().securityLevel;let s;a==="sandbox"&&(s=Zt("#i"+e));const h=a==="sandbox"?Zt(s.nodes()[0].contentDocument.body):Zt("body"),p=a==="sandbox"?s.nodes()[0].contentDocument:document,d=p.getElementById(e);ht=d.parentElement.offsetWidth,ht===void 0&&(ht=1200),i.useWidth!==void 0&&(ht=i.useWidth);const m=r.db.getTasks();let M=[];for(const w of m)M.push(w.type);M=H(M);const D={};let x=2*i.topPadding;if(r.db.getDisplayMode()==="compact"||i.displayMode==="compact"){const w={};for(const F of m)w[F.section]===void 0?w[F.section]=[F]:w[F.section].push(F);let Y=0;for(const F of Object.keys(w)){const S=Da(w[F],Y)+1;Y+=S,x+=S*(i.barHeight+i.barGap),D[F]=S}}else{x+=m.length*(i.barHeight+i.barGap);for(const w of M)D[w]=m.filter(Y=>Y.type===w).length}d.setAttribute("viewBox","0 0 "+ht+" "+x);const G=h.select(`[id="${e}"]`),N=Oi().domain([nr(m,function(w){return w.startTime}),er(m,function(w){return w.endTime})]).rangeRound([0,ht-i.leftPadding-i.rightPadding]);function _(w,Y){const F=w.startTime,S=Y.startTime;let T=0;return F>S?T=1:F<S&&(T=-1),T}y(_,"taskCompare"),m.sort(_),A(m,ht,x),Pn(G,x,ht,i.useMaxWidth),G.append("text").text(r.db.getDiagramTitle()).attr("x",ht/2).attr("y",i.titleTopMargin).attr("class","titleText");function A(w,Y,F){const S=i.barHeight,T=S+i.barGap,U=i.topPadding,u=i.leftPadding,g=Qn().domain([0,M.length]).range(["#00B9FA","#F95002"]).interpolate(kr);W(T,U,u,Y,F,w,r.db.getExcludes(),r.db.getIncludes()),Z(u,U,Y,F),V(w,T,U,u,S,g,Y),j(T,U),C(u,U,Y,F)}y(A,"makeGantt");function V(w,Y,F,S,T,U,u){w.sort((l,o)=>l.vert===o.vert?0:l.vert?1:-1);const v=[...new Set(w.map(l=>l.order))].map(l=>w.find(o=>o.order===l));G.append("g").selectAll("rect").data(v).enter().append("rect").attr("x",0).attr("y",function(l,o){return o=l.order,o*Y+F-2}).attr("width",function(){return u-i.rightPadding/2}).attr("height",Y).attr("class",function(l){for(const[o,R]of M.entries())if(l.type===R)return"section section"+o%i.numberSectionStyles;return"section section0"}).enter();const k=G.append("g").selectAll("rect").data(w).enter(),E=r.db.getLinks();if(k.append("rect").attr("id",function(l){return l.id}).attr("rx",3).attr("ry",3).attr("x",function(l){return l.milestone?N(l.startTime)+S+.5*(N(l.endTime)-N(l.startTime))-.5*T:N(l.startTime)+S}).attr("y",function(l,o){return o=l.order,l.vert?i.gridLineStartPadding:o*Y+F}).attr("width",function(l){return l.milestone?T:l.vert?.08*T:N(l.renderEndTime||l.endTime)-N(l.startTime)}).attr("height",function(l){return l.vert?m.length*(i.barHeight+i.barGap)+i.barHeight*2:T}).attr("transform-origin",function(l,o){return o=l.order,(N(l.startTime)+S+.5*(N(l.endTime)-N(l.startTime))).toString()+"px "+(o*Y+F+.5*T).toString()+"px"}).attr("class",function(l){const o="task";let R="";l.classes.length>0&&(R=l.classes.join(" "));let z=0;for(const[K,X]of M.entries())l.type===X&&(z=K%i.numberSectionStyles);let P="";return l.active?l.crit?P+=" activeCrit":P=" active":l.done?l.crit?P=" doneCrit":P=" done":l.crit&&(P+=" crit"),P.length===0&&(P=" task"),l.milestone&&(P=" milestone "+P),l.vert&&(P=" vert "+P),P+=z,P+=" "+R,o+P}),k.append("text").attr("id",function(l){return l.id+"-text"}).text(function(l){return l.task}).attr("font-size",i.fontSize).attr("x",function(l){let o=N(l.startTime),R=N(l.renderEndTime||l.endTime);if(l.milestone&&(o+=.5*(N(l.endTime)-N(l.startTime))-.5*T,R=o+T),l.vert)return N(l.startTime)+S;const z=this.getBBox().width;return z>R-o?R+z+1.5*i.leftPadding>u?o+S-5:R+S+5:(R-o)/2+o+S}).attr("y",function(l,o){return l.vert?i.gridLineStartPadding+m.length*(i.barHeight+i.barGap)+60:(o=l.order,o*Y+i.barHeight/2+(i.fontSize/2-2)+F)}).attr("text-height",T).attr("class",function(l){const o=N(l.startTime);let R=N(l.endTime);l.milestone&&(R=o+T);const z=this.getBBox().width;let P="";l.classes.length>0&&(P=l.classes.join(" "));let K=0;for(const[$,at]of M.entries())l.type===at&&(K=$%i.numberSectionStyles);let X="";return l.active&&(l.crit?X="activeCritText"+K:X="activeText"+K),l.done?l.crit?X=X+" doneCritText"+K:X=X+" doneText"+K:l.crit&&(X=X+" critText"+K),l.milestone&&(X+=" milestoneText"),l.vert&&(X+=" vertText"),z>R-o?R+z+1.5*i.leftPadding>u?P+" taskTextOutsideLeft taskTextOutside"+K+" "+X:P+" taskTextOutsideRight taskTextOutside"+K+" "+X+" width-"+z:P+" taskText taskText"+K+" "+X+" width-"+z}),_t().securityLevel==="sandbox"){let l;l=Zt("#i"+e);const o=l.nodes()[0].contentDocument;k.filter(function(R){return E.has(R.id)}).each(function(R){var z=o.querySelector("#"+R.id),P=o.querySelector("#"+R.id+"-text");const K=z.parentNode;var X=o.createElement("a");X.setAttribute("xlink:href",E.get(R.id)),X.setAttribute("target","_top"),K.appendChild(X),X.appendChild(z),X.appendChild(P)})}}y(V,"drawRects");function W(w,Y,F,S,T,U,u,g){if(u.length===0&&g.length===0)return;let v,k;for(const{startTime:z,endTime:P}of U)(v===void 0||z<v)&&(v=z),(k===void 0||P>k)&&(k=P);if(!v||!k)return;if(it(k).diff(it(v),"year")>5){jt.warn("The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.");return}const E=r.db.getDateFormat(),c=[];let l=null,o=it(v);for(;o.valueOf()<=k;)r.db.isInvalidDate(o,E,u,g)?l?l.end=o:l={start:o,end:o}:l&&(c.push(l),l=null),o=o.add(1,"d");G.append("g").selectAll("rect").data(c).enter().append("rect").attr("id",function(z){return"exclude-"+z.start.format("YYYY-MM-DD")}).attr("x",function(z){return N(z.start)+F}).attr("y",i.gridLineStartPadding).attr("width",function(z){const P=z.end.add(1,"day");return N(P)-N(z.start)}).attr("height",T-Y-i.gridLineStartPadding).attr("transform-origin",function(z,P){return(N(z.start)+F+.5*(N(z.end)-N(z.start))).toString()+"px "+(P*w+.5*T).toString()+"px"}).attr("class","exclude-range")}y(W,"drawExcludeDays");function Z(w,Y,F,S){let T=ur(N).tickSize(-S+Y+i.gridLineStartPadding).tickFormat(Kt(r.db.getAxisFormat()||i.axisFormat||"%Y-%m-%d"));const u=/^([1-9]\d*)(millisecond|second|minute|hour|day|week|month)$/.exec(r.db.getTickInterval()||i.tickInterval);if(u!==null){const g=u[1],v=u[2],k=r.db.getWeekday()||i.weekday;switch(v){case"millisecond":T.ticks(Yt.every(g));break;case"second":T.ticks(vt.every(g));break;case"minute":T.ticks(Wt.every(g));break;case"hour":T.ticks(Nt.every(g));break;case"day":T.ticks(Tt.every(g));break;case"week":T.ticks(Je[k].every(g));break;case"month":T.ticks(Ot.every(g));break}}if(G.append("g").attr("class","grid").attr("transform","translate("+w+", "+(S-50)+")").call(T).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10).attr("dy","1em"),r.db.topAxisEnabled()||i.topAxis){let g=lr(N).tickSize(-S+Y+i.gridLineStartPadding).tickFormat(Kt(r.db.getAxisFormat()||i.axisFormat||"%Y-%m-%d"));if(u!==null){const v=u[1],k=u[2],E=r.db.getWeekday()||i.weekday;switch(k){case"millisecond":g.ticks(Yt.every(v));break;case"second":g.ticks(vt.every(v));break;case"minute":g.ticks(Wt.every(v));break;case"hour":g.ticks(Nt.every(v));break;case"day":g.ticks(Tt.every(v));break;case"week":g.ticks(Je[E].every(v));break;case"month":g.ticks(Ot.every(v));break}}G.append("g").attr("class","grid").attr("transform","translate("+w+", "+Y+")").call(g).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10)}}y(Z,"makeGrid");function j(w,Y){let F=0;const S=Object.keys(D).map(T=>[T,D[T]]);G.append("g").selectAll("text").data(S).enter().append(function(T){const U=T[0].split(Rn.lineBreakRegex),u=-(U.length-1)/2,g=p.createElementNS("http://www.w3.org/2000/svg","text");g.setAttribute("dy",u+"em");for(const[v,k]of U.entries()){const E=p.createElementNS("http://www.w3.org/2000/svg","tspan");E.setAttribute("alignment-baseline","central"),E.setAttribute("x","10"),v>0&&E.setAttribute("dy","1em"),E.textContent=k,g.appendChild(E)}return g}).attr("x",10).attr("y",function(T,U){if(U>0)for(let u=0;u<U;u++)return F+=S[U-1][1],T[1]*w/2+F*w+Y;else return T[1]*w/2+Y}).attr("font-size",i.sectionFontSize).attr("class",function(T){for(const[U,u]of M.entries())if(T[0]===u)return"sectionTitle sectionTitle"+U%i.numberSectionStyles;return"sectionTitle"})}y(j,"vertLabels");function C(w,Y,F,S){const T=r.db.getTodayMarker();if(T==="off")return;const U=G.append("g").attr("class","today"),u=new Date,g=U.append("line");g.attr("x1",N(u)+w).attr("x2",N(u)+w).attr("y1",i.titleTopMargin).attr("y2",S-i.titleTopMargin).attr("class","today"),T!==""&&g.attr("style",T.replace(/,/g,";"))}y(C,"drawToday");function H(w){const Y={},F=[];for(let S=0,T=w.length;S<T;++S)Object.prototype.hasOwnProperty.call(Y,w[S])||(Y[w[S]]=!0,F.push(w[S]));return F}y(H,"checkUnique")},"draw"),Sa={setConf:Ca,draw:_a},Fa=y(t=>`
  .mermaid-main-font {
        font-family: ${t.fontFamily};
  }

  .exclude-range {
    fill: ${t.excludeBkgColor};
  }

  .section {
    stroke: none;
    opacity: 0.2;
  }

  .section0 {
    fill: ${t.sectionBkgColor};
  }

  .section2 {
    fill: ${t.sectionBkgColor2};
  }

  .section1,
  .section3 {
    fill: ${t.altSectionBkgColor};
    opacity: 0.2;
  }

  .sectionTitle0 {
    fill: ${t.titleColor};
  }

  .sectionTitle1 {
    fill: ${t.titleColor};
  }

  .sectionTitle2 {
    fill: ${t.titleColor};
  }

  .sectionTitle3 {
    fill: ${t.titleColor};
  }

  .sectionTitle {
    text-anchor: start;
    font-family: ${t.fontFamily};
  }


  /* Grid and axis */

  .grid .tick {
    stroke: ${t.gridColor};
    opacity: 0.8;
    shape-rendering: crispEdges;
  }

  .grid .tick text {
    font-family: ${t.fontFamily};
    fill: ${t.textColor};
  }

  .grid path {
    stroke-width: 0;
  }


  /* Today line */

  .today {
    fill: none;
    stroke: ${t.todayLineColor};
    stroke-width: 2px;
  }


  /* Task styling */

  /* Default task */

  .task {
    stroke-width: 2;
  }

  .taskText {
    text-anchor: middle;
    font-family: ${t.fontFamily};
  }

  .taskTextOutsideRight {
    fill: ${t.taskTextDarkColor};
    text-anchor: start;
    font-family: ${t.fontFamily};
  }

  .taskTextOutsideLeft {
    fill: ${t.taskTextDarkColor};
    text-anchor: end;
  }


  /* Special case clickable */

  .task.clickable {
    cursor: pointer;
  }

  .taskText.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideLeft.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }

  .taskTextOutsideRight.clickable {
    cursor: pointer;
    fill: ${t.taskTextClickableColor} !important;
    font-weight: bold;
  }


  /* Specific task settings for the sections*/

  .taskText0,
  .taskText1,
  .taskText2,
  .taskText3 {
    fill: ${t.taskTextColor};
  }

  .task0,
  .task1,
  .task2,
  .task3 {
    fill: ${t.taskBkgColor};
    stroke: ${t.taskBorderColor};
  }

  .taskTextOutside0,
  .taskTextOutside2
  {
    fill: ${t.taskTextOutsideColor};
  }

  .taskTextOutside1,
  .taskTextOutside3 {
    fill: ${t.taskTextOutsideColor};
  }


  /* Active task */

  .active0,
  .active1,
  .active2,
  .active3 {
    fill: ${t.activeTaskBkgColor};
    stroke: ${t.activeTaskBorderColor};
  }

  .activeText0,
  .activeText1,
  .activeText2,
  .activeText3 {
    fill: ${t.taskTextDarkColor} !important;
  }


  /* Completed task */

  .done0,
  .done1,
  .done2,
  .done3 {
    stroke: ${t.doneTaskBorderColor};
    fill: ${t.doneTaskBkgColor};
    stroke-width: 2;
  }

  .doneText0,
  .doneText1,
  .doneText2,
  .doneText3 {
    fill: ${t.taskTextDarkColor} !important;
  }


  /* Tasks on the critical line */

  .crit0,
  .crit1,
  .crit2,
  .crit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.critBkgColor};
    stroke-width: 2;
  }

  .activeCrit0,
  .activeCrit1,
  .activeCrit2,
  .activeCrit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.activeTaskBkgColor};
    stroke-width: 2;
  }

  .doneCrit0,
  .doneCrit1,
  .doneCrit2,
  .doneCrit3 {
    stroke: ${t.critBorderColor};
    fill: ${t.doneTaskBkgColor};
    stroke-width: 2;
    cursor: pointer;
    shape-rendering: crispEdges;
  }

  .milestone {
    transform: rotate(45deg) scale(0.8,0.8);
  }

  .milestoneText {
    font-style: italic;
  }
  .doneCritText0,
  .doneCritText1,
  .doneCritText2,
  .doneCritText3 {
    fill: ${t.taskTextDarkColor} !important;
  }

  .vert {
    stroke: ${t.vertLineColor};
  }

  .vertText {
    font-size: 15px;
    text-anchor: middle;
    fill: ${t.vertLineColor} !important;
  }

  .activeCritText0,
  .activeCritText1,
  .activeCritText2,
  .activeCritText3 {
    fill: ${t.taskTextDarkColor} !important;
  }

  .titleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${t.titleColor||t.textColor};
    font-family: ${t.fontFamily};
  }
`,"getStyles"),Ya=Fa,Va={parser:Pi,db:Ma,renderer:Sa,styles:Ya};export{Va as diagram};
